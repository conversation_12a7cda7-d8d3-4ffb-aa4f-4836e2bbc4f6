<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="88px" height="1080px" viewBox="0 0 88 1080">

<!-- left arrow -->
<g transform="translate(0, 0)">
	<path fill="#fff" d="M1.293,11.293L9,3.586L10.414,5l-6,6H22c0.553,0,1,0.448,1,1s-0.447,1-1,1H4.414l6,6L9,20.414l-7.707-7.707&#10;&#9;C0.902,12.316,0.902,11.684,1.293,11.293z"/>
</g>
<!-- right arrow -->
<g transform="translate(0, 44)">
	<path fill="#fff" d="M22.707,11.293L15,3.586L13.586,5l6,6H2c-0.553,0-1,0.448-1,1s0.447,1,1,1h17.586l-6,6L15,20.414&#10;&#9;l7.707-7.707C23.098,12.316,23.098,11.684,22.707,11.293z"/>
</g>
<!-- close -->
<g transform="translate(0, 88)">
	<path fill="#fff" d="M15.657,0.343c-0.457-0.457-1.143-0.457-1.6,0L8,6.4L1.943,0.343c-0.458-0.457-1.144-0.457-1.601,0 c-0.458,0.457-0.458,1.143,0,1.601L6.4,8l-6.057,6.057c-0.458,0.458-0.458,1.144,0,1.601C0.571,15.885,0.8,16,1.143,16 s0.571-0.114,0.801-0.343L8,9.6l6.058,6.057c0.228,0.229,0.57,0.343,0.8,0.343s0.57-0.114,0.8-0.343 c0.456-0.457,0.456-1.143,0-1.601L9.6,8l6.058-6.057C16.113,1.485,16.113,0.8,15.657,0.343z"/>
</g>
<!-- zoom in -->
<g transform="translate(0, 132)">
	<path fill="#fff" d="M12.7,11.3c0.9-1.2,1.4-2.6,1.4-4.2C14.1,3.2,11,0,7.1,0S0,3.2,0,7.1c0,3.9,3.2,7.1,7.1,7.1&#10;&#9;c1.6,0,3.1-0.5,4.2-1.4l3,3c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3c0.4-0.4,0.4-1,0-1.4L12.7,11.3z M7.1,12.1&#10;&#9;C4.3,12.1,2,9.9,2,7.1S4.3,2,7.1,2s5.1,2.3,5.1,5.1S9.9,12.1,7.1,12.1z"/>
	<polygon fill="#fff" points="8,4 6,4 6,6 4,6 4,8 6,8 6,10 8,10 8,8 10,8 10,6 8,6 "/>
</g>
<!-- zoom out -->
<g transform="translate(0, 176)">
	<path fill="#fff" d="M12.7,11.3c0.9-1.2,1.4-2.6,1.4-4.2C14.1,3.2,11,0,7.1,0S0,3.2,0,7.1c0,3.9,3.2,7.1,7.1,7.1&#10;&#9;c1.6,0,3.1-0.5,4.2-1.4l3,3c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3c0.4-0.4,0.4-1,0-1.4L12.7,11.3z M7.1,12.1&#10;&#9;C4.3,12.1,2,9.9,2,7.1S4.3,2,7.1,2s5.1,2.3,5.1,5.1S9.9,12.1,7.1,12.1z"/>
	<rect x="4" y="6" fill="#fff" width="6" height="2"/>
</g>
<!-- play -->
<g transform="translate(0, 220)">
	<path fill="#fff" d="M14,7.999c0-0.326-0.159-0.632-0.427-0.819l-10-7C3.269-0.034,2.869-0.058,2.538,0.112&#10;&#9;C2.207,0.285,2,0.626,2,0.999v14.001c0,0.373,0.207,0.715,0.538,0.887c0.331,0.17,0.73,0.146,1.035-0.068l10-7&#10;&#9;C13.841,8.633,14,8.327,14,8.001C14,8,14,8,14,7.999C14,8,14,8,14,7.999z"/>
</g>
<!-- pause -->
<g transform="translate(0, 264)">
	<path fill="#fff" d="M5,1H2C1.4,1,1,1.4,1,2v12c0,0.6,0.4,1,1,1h3c0.6,0,1-0.4,1-1V2C6,1.4,5.6,1,5,1z"/>
	<path fill="#fff" d="M14,1h-3c-0.6,0-1,0.4-1,1v12c0,0.6,0.4,1,1,1h3c0.6,0,1-0.4,1-1V2C15,1.4,14.6,1,14,1z"/>
</g>
<!-- download -->
<g transform="translate(0, 308)">
	<path fill="#fff" d="M8,12c0.3,0,0.5-0.1,0.7-0.3L14.4,6L13,4.6l-4,4V0H7v8.6l-4-4L1.6,6l5.7,5.7C7.5,11.9,7.7,12,8,12z"/>
	<path fill="#fff" d="M14,14H2v-3H0v4c0,0.6,0.4,1,1,1h14c0.6,0,1-0.4,1-1v-4h-2V14z"/>
</g>
<!-- share -->
<g transform="translate(0, 352)">
	<path fill="#fff" d="M15,16H1c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1h3v2H2v10h12V9h2v6C16,15.6,15.6,16,15,16z"></path>
	<path fill="#fff" d="M10,3c-3.2,0-6,2.5-6,7c1.1-1.7,2.4-3,6-3v3l6-5l-6-5V3z"></path>
</g>
<!-- to fullscreen -->
<g transform="translate(0, 396)">
	<path fill="#fff" d="M2,6H0V1c0-0.6,0.4-1,1-1h5v2H2V6z"/>
	<path fill="#fff" d="M16,6h-2V2h-4V0h5c0.6,0,1,0.4,1,1V6z"/>
	<path fill="#fff" d="M15,16h-5v-2h4v-4h2v5C16,15.6,15.6,16,15,16z"/>
	<path fill="#fff" d="M6,16H1c-0.6,0-1-0.4-1-1v-5h2v4h4V16z"/>
</g>
<!-- un fullscreen -->
<g transform="translate(0, 440)">
	<path fill="#fff" d="M12,16h-2v-5c0-0.601,0.4-1,1-1h5v2h-4V16z"/>
	<path fill="#fff" d="M6,16H4v-4H0v-2h5c0.6,0,1,0.399,1,1V16z"/>
	<path fill="#fff" d="M5,6H0V4h4V0h2v5C6,5.6,5.6,6,5,6z"/>
	<path fill="#fff" d="M16,6h-5c-0.6,0-1-0.4-1-1V0h2v4h4V6z"/>
</g>
<!-- Facebook -->
<g transform="translate(0, 484)">
	<path fill="#fff" d="M6.02293,16L6,9H3V6h3V4c0-2.6992,1.67151-4,4.07938-4c1.15339,0,2.14468,0.08587,2.43356,0.12425v2.82082&#10;&#9;l-1.66998,0.00076c-1.30953,0-1.56309,0.62227-1.56309,1.53541V6H13l-1,3H9.27986v7H6.02293z"/>
</g>
<!-- twitter -->
<g transform="translate(0, 528)">
	<path fill="#fff" d="M16,3c-0.6,0.3-1.2,0.4-1.9,0.5c0.7-0.4,1.2-1,1.4-1.8c-0.6,0.4-1.3,0.6-2.1,0.8c-0.6-0.6-1.5-1-2.4-1&#10;&#9;C9.3,1.5,7.8,3,7.8,4.8c0,0.3,0,0.5,0.1,0.7C5.2,5.4,2.7,4.1,1.1,2.1c-0.3,0.5-0.4,1-0.4,1.7c0,1.1,0.6,2.1,1.5,2.7&#10;&#9;c-0.5,0-1-0.2-1.5-0.4c0,0,0,0,0,0c0,1.6,1.1,2.9,2.6,3.2C3,9.4,2.7,9.4,2.4,9.4c-0.2,0-0.4,0-0.6-0.1c0.4,1.3,1.6,2.3,3.1,2.3&#10;&#9;c-1.1,0.9-2.5,1.4-4.1,1.4c-0.3,0-0.5,0-0.8,0c1.5,0.9,3.2,1.5,5,1.5c6,0,9.3-5,9.3-9.3c0-0.1,0-0.3,0-0.4C15,4.3,15.6,3.7,16,3z"/>
</g>
<!-- Google plus -->
<g transform="translate(0, 572)">
	<path fill="#fff" d="M8,7v2.4h4.1c-0.2,1-1.2,3-4,3c-2.4,0-4.3-2-4.3-4.4s2-4.4,4.3-4.4&#10;&#9;c1.4,0,2.3,0.6,2.8,1.1l1.9-1.8C11.6,1.7,10,1,8.1,1c-3.9,0-7,3.1-7,7s3.1,7,7,7c4,0,6.7-2.8,6.7-6.8c0-0.5,0-0.8-0.1-1.2H8L8,7z"/>
</g>
<!-- pinterest -->
<g transform="translate(0, 616)">
	<path fill="#fff" d="M8,0C3.6,0,0,3.6,0,8c0,3.4,2.1,6.3,5.1,7.4c-0.1-0.6-0.1-1.6,0-2.3c0.1-0.6,0.9-4,0.9-4S5.8,8.7,5.8,8&#10;&#9;C5.8,6.9,6.5,6,7.3,6c0.7,0,1,0.5,1,1.1c0,0.7-0.4,1.7-0.7,2.7c-0.2,0.8,0.4,1.4,1.2,1.4c1.4,0,2.5-1.5,2.5-3.7&#10;&#9;c0-1.9-1.4-3.3-3.3-3.3c-2.3,0-3.6,1.7-3.6,3.5c0,0.7,0.3,1.4,0.6,1.8C5,9.7,5,9.8,5,9.9c-0.1,0.3-0.2,0.8-0.2,0.9&#10;&#9;c0,0.1-0.1,0.2-0.3,0.1c-1-0.5-1.6-1.9-1.6-3.1C2.9,5.3,4.7,3,8.2,3c2.8,0,4.9,2,4.9,4.6c0,2.8-1.7,5-4.2,5c-0.8,0-1.6-0.4-1.8-0.9&#10;&#9;c0,0-0.4,1.5-0.5,1.9c-0.2,0.7-0.7,1.6-1,2.1C6.4,15.9,7.2,16,8,16c4.4,0,8-3.6,8-8C16,3.6,12.4,0,8,0z"/>
</g>
<!-- linkedin -->
<g transform="translate(0, 660)">
	<path fill="#fff" d="M15.3,0H0.7C0.3,0,0,0.3,0,0.7v14.7C0,15.7,0.3,16,0.7,16h14.7c0.4,0,0.7-0.3,0.7-0.7V0.7&#10;&#9;C16,0.3,15.7,0,15.3,0z M4.7,13.6H2.4V6h2.4V13.6z M3.6,5C2.8,5,2.2,4.3,2.2,3.6c0-0.8,0.6-1.4,1.4-1.4c0.8,0,1.4,0.6,1.4,1.4&#10;&#9;C4.9,4.3,4.3,5,3.6,5z M13.6,13.6h-2.4V9.9c0-0.9,0-2-1.2-2c-1.2,0-1.4,1-1.4,2v3.8H6.2V6h2.3v1h0c0.3-0.6,1.1-1.2,2.2-1.2&#10;&#9;c2.4,0,2.8,1.6,2.8,3.6V13.6z"/>
</g>
<!-- reddit -->
<g transform="translate(0, 704)">
	<path fill="#fff" d="M16,7.9c0-1.1-0.9-1.9-1.9-1.9c-0.5,0-0.9,0.2-1.2,0.4c-1.2-0.7-2.7-1.2-4.3-1.3l0.8-2.6L11.7,3&#10;&#9;c0.1,0.8,0.8,1.5,1.6,1.5c0.9,0,1.6-0.7,1.6-1.6c0-0.9-0.7-1.6-1.6-1.6c-0.6,0-1.2,0.4-1.4,0.9L9.2,1.5C9,1.5,8.8,1.6,8.7,1.8&#10;&#9;l-1,3.3C6,5.1,4.4,5.6,3.1,6.3C2.8,6.1,2.4,5.9,1.9,5.9C0.9,5.9,0,6.8,0,7.9c0,0.7,0.3,1.2,0.8,1.6c0,0.2,0,0.3,0,0.5&#10;&#9;c0,1.3,0.8,2.6,2.2,3.5c1.3,0.9,3.1,1.4,5,1.4c1.9,0,3.7-0.5,5-1.4c1.4-0.9,2.2-2.1,2.2-3.5c0-0.1,0-0.3,0-0.4&#10;&#9;C15.6,9.1,16,8.5,16,7.9z M4.5,9c0-0.6,0.5-1.1,1.1-1.1c0.6,0,1.1,0.5,1.1,1.1s-0.5,1.1-1.1,1.1C5,10.1,4.5,9.6,4.5,9z M10.6,12.2&#10;&#9;c-0.6,0.6-1.4,0.8-2.6,0.8c0,0,0,0,0,0c0,0,0,0,0,0c-1.2,0-2.1-0.3-2.6-0.8c-0.2-0.2-0.2-0.4,0-0.6c0.2-0.2,0.4-0.2,0.6,0&#10;&#9;c0.4,0.4,1,0.6,2,0.6c0,0,0,0,0,0c0,0,0,0,0,0c1,0,1.6-0.2,2-0.6c0.2-0.2,0.4-0.2,0.6,0C10.8,11.8,10.8,12.1,10.6,12.2z M10.4,10.1&#10;&#9;c-0.6,0-1.1-0.5-1.1-1.1c0-0.6,0.5-1.1,1.1-1.1c0.6,0,1.1,0.5,1.1,1.1C11.5,9.6,11,10.1,10.4,10.1z"/>
</g>
<!-- tumblr -->
<g transform="translate(0, 748)">
	<path fill="#fff" d="M11.8,12.8c-0.3,0.1-0.9,0.3-1.3,0.3c-1.3,0-1.5-1-1.5-2.1V7h3.5L12,4H9l0-4H6c0,0,0,3.1-3,4v3h2v5&#10;&#9;c0,1.8,1.3,4.1,4.8,4c1.2,0,2.5-0.5,2.8-0.9L11.8,12.8z"/>
</g>
<!-- blogger -->
<g transform="translate(0, 792)">
	<path fill-rule="evenodd" clip-rule="evenodd" fill="#fff" d="M15.6,6.2c-0.3-0.1-1.8,0-2.2-0.3c-0.3-0.3-0.3-1.9-0.9-3.1&#10;&#9;C11.8,1.3,10.1,0,8.6,0H5.1C2.3,0,0,2.3,0,5.1V11c0,2.8,2.3,5,5.1,5h5.8c2.8,0,5-2.3,5.1-5l0-4.1C16,6.9,16,6.4,15.6,6.2z M5,4h3&#10;&#9;c0.6,0,1,0.4,1,1c0,0.6-0.4,1-1,1H5C4.4,6,4,5.6,4,5C4,4.4,4.4,4,5,4z M11,12H5c-0.6,0-1-0.4-1-1c0-0.6,0.4-1,1-1h6c0.6,0,1,0.4,1,1&#10;&#9;C12,11.6,11.6,12,11,12z"/>
</g>
<!-- buffer -->
<g transform="translate(0, 836)">
	<path fill="#fff" d="M7.6,0.1L0.4,3.5C0.2,3.6,0.2,3.9,0.4,4l7.3,3.3c0.2,0.1,0.5,0.1,0.7,0L15.6,4c0.2-0.1,0.2-0.4,0-0.5&#10;&#9;L8.4,0.1C8.1,0,7.9,0,7.6,0.1z"/>
	<path fill="#fff" d="M15.6,7.8l-1.5-0.7c-0.2-0.1-0.5-0.1-0.7,0L8.3,9.4c-0.2,0.1-0.5,0.1-0.7,0L2.6,7.1C2.3,7,2.1,7,1.9,7.1&#10;&#9;L0.4,7.8c-0.2,0.1-0.2,0.4,0,0.5l7.3,3.3c0.2,0.1,0.5,0.1,0.7,0l7.3-3.3C15.8,8.2,15.8,7.9,15.6,7.8z"/>
	<path fill="#fff" d="M15.6,12.1l-1.5-0.7c-0.2-0.1-0.5-0.1-0.7,0l-5.1,2.3c-0.2,0.1-0.5,0.1-0.7,0l-5.1-2.3&#10;&#9;c-0.2-0.1-0.5-0.1-0.7,0l-1.5,0.7c-0.2,0.1-0.2,0.4,0,0.5l7.3,3.3c0.2,0.1,0.5,0.1,0.7,0l7.3-3.3C15.8,12.5,15.8,12.2,15.6,12.1z"/>
</g>
<!-- digg -->
<g transform="translate(0, 880)">
	<path fill="#fff" d="M3.203,2.809h1.992v9.604H0V5.602h3.204L3.203,2.809L3.203,2.809z M3.203,10.813V7.204H2v3.609H3.203z
		 M5.996,5.601v6.803h2.001V5.601H5.996L5.996,5.601z M5.996,2.809V4.8h2.001V2.809H5.996L5.996,2.809z M8.795,5.601h5.211v9.2
		H8.795v-1.597h3.202v-0.801H8.795V5.601L8.795,5.601z M11.997,10.813V7.204h-1.198v3.609H11.997z M14.805,5.601H20v9.2h-5.195
		v-1.597h3.189v-0.801h-3.189V5.601L14.805,5.601z M17.994,10.813V7.204h-1.198v3.609H17.994z"/>
</g>
<!-- stumbleupon -->
<g transform="translate(0, 924)">
	<path fill="#fff" d="M9.4,6.862l1.121,0.623l1.771-0.599V5.685C12.293,3.613,10.562,2,8.5,2C6.447,2,4.708,3.507,4.708,5.663v5.488
	c0,0.498-0.404,0.901-0.901,0.901c-0.497,0-0.899-0.403-0.899-0.901V8.825H0v2.357c0,2.099,1.701,3.801,3.8,3.801
	c2.081,0,3.771-1.671,3.799-3.747V5.815c0-0.497,0.403-0.898,0.901-0.898c0.497,0,0.9,0.401,0.9,0.898V6.862z M14.091,8.826v2.434
	c0,0.498-0.403,0.9-0.899,0.9c-0.497,0-0.898-0.402-0.898-0.9V8.872l-1.771,0.6L9.4,8.847v2.37c0.017,2.08,1.711,3.767,3.8,3.767
	c2.099,0,3.8-1.702,3.8-3.801V8.825L14.091,8.826L14.091,8.826z"/>
</g>
<!-- evernote -->
<g transform="translate(0, 968)">
	<path fill="#fff" d="M14.3,2.8c-0.1-0.6-0.5-1-0.8-1.1c-0.4-0.1-1.1-0.3-2.1-0.4c-0.8-0.1-1.7-0.1-2.2-0.1
	c-0.1-0.4-0.4-0.8-0.7-1C7.8,0,6.3,0,6.3,0c-1,0-1.4,0.1-1.8,0.4c0,0-2.9,2.9-2.9,2.9C1,3.9,1.4,5.6,1.4,5.6l0,0
	c0,0,0.5,3.4,0.9,4.3c0.2,0.3,0.3,0.5,0.6,0.6c0.7,0.3,2.4,0.7,3.2,0.8c0.8,0.1,1.3,0.3,1.6-0.3c0,0,0.4-0.6,0.4-2.3
	c0,0,0.1-0.1,0.1,0c0,0.3-0.1,1.6,0.9,1.9c0.4,0.1,1.1,0.2,1.9,0.3c0.7,0.1,1.2,0.4,1.2,2.1c0,1.1-0.2,1.2-1.4,1.2
	c-1,0-1.3,0-1.3-0.7c0-0.6,0.6-0.6,1.1-0.6c0.2,0,0.1-0.2,0.1-0.5c0-0.4,0.2-0.6,0-0.6c-1.6,0-2.5,0-2.5,2c0,1.8,0.7,2.1,2.9,2.1
	c1.8,0,2.4-0.1,3.1-2.3c0.1-0.4,0.5-1.8,0.7-4.1C14.8,8.2,14.5,3.9,14.3,2.8z M10.7,7.6c0.1-0.4,0.2-1,0.9-1c0.7,0,0.8,0.7,0.8,1.2
	C12.1,7.7,11.3,7.4,10.7,7.6z"></path>
</g>



<!-- black -->

<!-- left arrow -->
<g transform="translate(44, 0)">
	<path fill="#444" d="M1.293,11.293L9,3.586L10.414,5l-6,6H22c0.553,0,1,0.448,1,1s-0.447,1-1,1H4.414l6,6L9,20.414l-7.707-7.707&#10;&#9;C0.902,12.316,0.902,11.684,1.293,11.293z"/>
</g>
<!-- right arrow -->
<g transform="translate(44, 44)">
	<path fill="#444" d="M22.707,11.293L15,3.586L13.586,5l6,6H2c-0.553,0-1,0.448-1,1s0.447,1,1,1h17.586l-6,6L15,20.414&#10;&#9;l7.707-7.707C23.098,12.316,23.098,11.684,22.707,11.293z"/>
</g>
<!-- close -->
<g transform="translate(44, 88)">
	<path fill="#444" d="M14.7,1.3c-0.4-0.4-1-0.4-1.4,0L8,6.6L2.7,1.3c-0.4-0.4-1-0.4-1.4,0s-0.4,1,0,1.4L6.6,8l-5.3,5.3&#10;&#9;c-0.4,0.4-0.4,1,0,1.4C1.5,14.9,1.7,15,2,15s0.5-0.1,0.7-0.3L8,9.4l5.3,5.3c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3&#10;&#9;c0.4-0.4,0.4-1,0-1.4L9.4,8l5.3-5.3C15.1,2.3,15.1,1.7,14.7,1.3z"/>
</g>
<!-- zoom in -->
<g transform="translate(44, 132)">
	<path fill="#444" d="M12.7,11.3c0.9-1.2,1.4-2.6,1.4-4.2C14.1,3.2,11,0,7.1,0S0,3.2,0,7.1c0,3.9,3.2,7.1,7.1,7.1&#10;&#9;c1.6,0,3.1-0.5,4.2-1.4l3,3c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3c0.4-0.4,0.4-1,0-1.4L12.7,11.3z M7.1,12.1&#10;&#9;C4.3,12.1,2,9.9,2,7.1S4.3,2,7.1,2s5.1,2.3,5.1,5.1S9.9,12.1,7.1,12.1z"/>
	<polygon fill="#444" points="8,4 6,4 6,6 4,6 4,8 6,8 6,10 8,10 8,8 10,8 10,6 8,6 "/>
</g>
<!-- zoom out -->
<g transform="translate(44, 176)">
	<path fill="#444" d="M12.7,11.3c0.9-1.2,1.4-2.6,1.4-4.2C14.1,3.2,11,0,7.1,0S0,3.2,0,7.1c0,3.9,3.2,7.1,7.1,7.1&#10;&#9;c1.6,0,3.1-0.5,4.2-1.4l3,3c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3c0.4-0.4,0.4-1,0-1.4L12.7,11.3z M7.1,12.1&#10;&#9;C4.3,12.1,2,9.9,2,7.1S4.3,2,7.1,2s5.1,2.3,5.1,5.1S9.9,12.1,7.1,12.1z"/>
	<rect x="4" y="6" fill="#444" width="6" height="2"/>
</g>
<!-- play -->
<g transform="translate(44, 220)">
	<path fill="#444" d="M14,7.999c0-0.326-0.159-0.632-0.427-0.819l-10-7C3.269-0.034,2.869-0.058,2.538,0.112&#10;&#9;C2.207,0.285,2,0.626,2,0.999v14.001c0,0.373,0.207,0.715,0.538,0.887c0.331,0.17,0.73,0.146,1.035-0.068l10-7&#10;&#9;C13.841,8.633,14,8.327,14,8.001C14,8,14,8,14,7.999C14,8,14,8,14,7.999z"/>
</g>
<!-- pause -->
<g transform="translate(44, 264)">
	<path fill="#444" d="M5,1H2C1.4,1,1,1.4,1,2v12c0,0.6,0.4,1,1,1h3c0.6,0,1-0.4,1-1V2C6,1.4,5.6,1,5,1z"/>
	<path fill="#444" d="M14,1h-3c-0.6,0-1,0.4-1,1v12c0,0.6,0.4,1,1,1h3c0.6,0,1-0.4,1-1V2C15,1.4,14.6,1,14,1z"/>
</g>
<!-- download -->
<g transform="translate(44, 308)">
	<path fill="#444" d="M8,12c0.3,0,0.5-0.1,0.7-0.3L14.4,6L13,4.6l-4,4V0H7v8.6l-4-4L1.6,6l5.7,5.7C7.5,11.9,7.7,12,8,12z"/>
	<path fill="#444" d="M14,14H2v-3H0v4c0,0.6,0.4,1,1,1h14c0.6,0,1-0.4,1-1v-4h-2V14z"/>
</g>
<!-- share -->
<g transform="translate(44, 352)">
	<path fill="#444" d="M15,16H1c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1h3v2H2v10h12V9h2v6C16,15.6,15.6,16,15,16z"></path>
	<path fill="#444" d="M10,3c-3.2,0-6,2.5-6,7c1.1-1.7,2.4-3,6-3v3l6-5l-6-5V3z"></path>
</g>
<!-- to fullscreen -->
<g transform="translate(44, 396)">
	<path fill="#444" d="M2,6H0V1c0-0.6,0.4-1,1-1h5v2H2V6z"/>
	<path fill="#444" d="M16,6h-2V2h-4V0h5c0.6,0,1,0.4,1,1V6z"/>
	<path fill="#444" d="M15,16h-5v-2h4v-4h2v5C16,15.6,15.6,16,15,16z"/>
	<path fill="#444" d="M6,16H1c-0.6,0-1-0.4-1-1v-5h2v4h4V16z"/>
</g>
<!-- un fullscreen -->
<g transform="translate(44, 440)">
	<path fill="#444" d="M12,16h-2v-5c0-0.601,0.4-1,1-1h5v2h-4V16z"/>
	<path fill="#444" d="M6,16H4v-4H0v-2h5c0.6,0,1,0.399,1,1V16z"/>
	<path fill="#444" d="M5,6H0V4h4V0h2v5C6,5.6,5.6,6,5,6z"/>
	<path fill="#444" d="M16,6h-5c-0.6,0-1-0.4-1-1V0h2v4h4V6z"/>
</g>
<!-- Facebook -->
<g transform="translate(44, 484)">
	<path fill="#444" d="M6.02293,16L6,9H3V6h3V4c0-2.6992,1.67151-4,4.07938-4c1.15339,0,2.14468,0.08587,2.43356,0.12425v2.82082&#10;&#9;l-1.66998,0.00076c-1.30953,0-1.56309,0.62227-1.56309,1.53541V6H13l-1,3H9.27986v7H6.02293z"/>
</g>
<!-- twitter -->
<g transform="translate(44, 528)">
	<path fill="#444" d="M16,3c-0.6,0.3-1.2,0.4-1.9,0.5c0.7-0.4,1.2-1,1.4-1.8c-0.6,0.4-1.3,0.6-2.1,0.8c-0.6-0.6-1.5-1-2.4-1&#10;&#9;C9.3,1.5,7.8,3,7.8,4.8c0,0.3,0,0.5,0.1,0.7C5.2,5.4,2.7,4.1,1.1,2.1c-0.3,0.5-0.4,1-0.4,1.7c0,1.1,0.6,2.1,1.5,2.7&#10;&#9;c-0.5,0-1-0.2-1.5-0.4c0,0,0,0,0,0c0,1.6,1.1,2.9,2.6,3.2C3,9.4,2.7,9.4,2.4,9.4c-0.2,0-0.4,0-0.6-0.1c0.4,1.3,1.6,2.3,3.1,2.3&#10;&#9;c-1.1,0.9-2.5,1.4-4.1,1.4c-0.3,0-0.5,0-0.8,0c1.5,0.9,3.2,1.5,5,1.5c6,0,9.3-5,9.3-9.3c0-0.1,0-0.3,0-0.4C15,4.3,15.6,3.7,16,3z"/>
</g>
<!-- Google plus -->
<g transform="translate(44, 572)">
	<path fill="#444" d="M8,7v2.4h4.1c-0.2,1-1.2,3-4,3c-2.4,0-4.3-2-4.3-4.4s2-4.4,4.3-4.4&#10;&#9;c1.4,0,2.3,0.6,2.8,1.1l1.9-1.8C11.6,1.7,10,1,8.1,1c-3.9,0-7,3.1-7,7s3.1,7,7,7c4,0,6.7-2.8,6.7-6.8c0-0.5,0-0.8-0.1-1.2H8L8,7z"/>
</g>
<!-- pinterest -->
<g transform="translate(44, 616)">
	<path fill="#444" d="M8,0C3.6,0,0,3.6,0,8c0,3.4,2.1,6.3,5.1,7.4c-0.1-0.6-0.1-1.6,0-2.3c0.1-0.6,0.9-4,0.9-4S5.8,8.7,5.8,8&#10;&#9;C5.8,6.9,6.5,6,7.3,6c0.7,0,1,0.5,1,1.1c0,0.7-0.4,1.7-0.7,2.7c-0.2,0.8,0.4,1.4,1.2,1.4c1.4,0,2.5-1.5,2.5-3.7&#10;&#9;c0-1.9-1.4-3.3-3.3-3.3c-2.3,0-3.6,1.7-3.6,3.5c0,0.7,0.3,1.4,0.6,1.8C5,9.7,5,9.8,5,9.9c-0.1,0.3-0.2,0.8-0.2,0.9&#10;&#9;c0,0.1-0.1,0.2-0.3,0.1c-1-0.5-1.6-1.9-1.6-3.1C2.9,5.3,4.7,3,8.2,3c2.8,0,4.9,2,4.9,4.6c0,2.8-1.7,5-4.2,5c-0.8,0-1.6-0.4-1.8-0.9&#10;&#9;c0,0-0.4,1.5-0.5,1.9c-0.2,0.7-0.7,1.6-1,2.1C6.4,15.9,7.2,16,8,16c4.4,0,8-3.6,8-8C16,3.6,12.4,0,8,0z"/>
</g>
<!-- linkedin -->
<g transform="translate(44, 660)">
	<path fill="#444" d="M15.3,0H0.7C0.3,0,0,0.3,0,0.7v14.7C0,15.7,0.3,16,0.7,16h14.7c0.4,0,0.7-0.3,0.7-0.7V0.7&#10;&#9;C16,0.3,15.7,0,15.3,0z M4.7,13.6H2.4V6h2.4V13.6z M3.6,5C2.8,5,2.2,4.3,2.2,3.6c0-0.8,0.6-1.4,1.4-1.4c0.8,0,1.4,0.6,1.4,1.4&#10;&#9;C4.9,4.3,4.3,5,3.6,5z M13.6,13.6h-2.4V9.9c0-0.9,0-2-1.2-2c-1.2,0-1.4,1-1.4,2v3.8H6.2V6h2.3v1h0c0.3-0.6,1.1-1.2,2.2-1.2&#10;&#9;c2.4,0,2.8,1.6,2.8,3.6V13.6z"/>
</g>
<!-- reddit -->
<g transform="translate(44, 704)">
	<path fill="#444" d="M16,7.9c0-1.1-0.9-1.9-1.9-1.9c-0.5,0-0.9,0.2-1.2,0.4c-1.2-0.7-2.7-1.2-4.3-1.3l0.8-2.6L11.7,3&#10;&#9;c0.1,0.8,0.8,1.5,1.6,1.5c0.9,0,1.6-0.7,1.6-1.6c0-0.9-0.7-1.6-1.6-1.6c-0.6,0-1.2,0.4-1.4,0.9L9.2,1.5C9,1.5,8.8,1.6,8.7,1.8&#10;&#9;l-1,3.3C6,5.1,4.4,5.6,3.1,6.3C2.8,6.1,2.4,5.9,1.9,5.9C0.9,5.9,0,6.8,0,7.9c0,0.7,0.3,1.2,0.8,1.6c0,0.2,0,0.3,0,0.5&#10;&#9;c0,1.3,0.8,2.6,2.2,3.5c1.3,0.9,3.1,1.4,5,1.4c1.9,0,3.7-0.5,5-1.4c1.4-0.9,2.2-2.1,2.2-3.5c0-0.1,0-0.3,0-0.4&#10;&#9;C15.6,9.1,16,8.5,16,7.9z M4.5,9c0-0.6,0.5-1.1,1.1-1.1c0.6,0,1.1,0.5,1.1,1.1s-0.5,1.1-1.1,1.1C5,10.1,4.5,9.6,4.5,9z M10.6,12.2&#10;&#9;c-0.6,0.6-1.4,0.8-2.6,0.8c0,0,0,0,0,0c0,0,0,0,0,0c-1.2,0-2.1-0.3-2.6-0.8c-0.2-0.2-0.2-0.4,0-0.6c0.2-0.2,0.4-0.2,0.6,0&#10;&#9;c0.4,0.4,1,0.6,2,0.6c0,0,0,0,0,0c0,0,0,0,0,0c1,0,1.6-0.2,2-0.6c0.2-0.2,0.4-0.2,0.6,0C10.8,11.8,10.8,12.1,10.6,12.2z M10.4,10.1&#10;&#9;c-0.6,0-1.1-0.5-1.1-1.1c0-0.6,0.5-1.1,1.1-1.1c0.6,0,1.1,0.5,1.1,1.1C11.5,9.6,11,10.1,10.4,10.1z"/>
</g>
<!-- tumblr -->
<g transform="translate(44, 748)">
	<path fill="#444" d="M11.8,12.8c-0.3,0.1-0.9,0.3-1.3,0.3c-1.3,0-1.5-1-1.5-2.1V7h3.5L12,4H9l0-4H6c0,0,0,3.1-3,4v3h2v5&#10;&#9;c0,1.8,1.3,4.1,4.8,4c1.2,0,2.5-0.5,2.8-0.9L11.8,12.8z"/>
</g>
<!-- blogger -->
<g transform="translate(44, 792)">
	<path fill-rule="evenodd" clip-rule="evenodd" fill="#444" d="M15.6,6.2c-0.3-0.1-1.8,0-2.2-0.3c-0.3-0.3-0.3-1.9-0.9-3.1&#10;&#9;C11.8,1.3,10.1,0,8.6,0H5.1C2.3,0,0,2.3,0,5.1V11c0,2.8,2.3,5,5.1,5h5.8c2.8,0,5-2.3,5.1-5l0-4.1C16,6.9,16,6.4,15.6,6.2z M5,4h3&#10;&#9;c0.6,0,1,0.4,1,1c0,0.6-0.4,1-1,1H5C4.4,6,4,5.6,4,5C4,4.4,4.4,4,5,4z M11,12H5c-0.6,0-1-0.4-1-1c0-0.6,0.4-1,1-1h6c0.6,0,1,0.4,1,1&#10;&#9;C12,11.6,11.6,12,11,12z"/>
</g>
<!-- buffer -->
<g transform="translate(44, 836)">
	<path fill="#444" d="M7.6,0.1L0.4,3.5C0.2,3.6,0.2,3.9,0.4,4l7.3,3.3c0.2,0.1,0.5,0.1,0.7,0L15.6,4c0.2-0.1,0.2-0.4,0-0.5&#10;&#9;L8.4,0.1C8.1,0,7.9,0,7.6,0.1z"/>
	<path fill="#444" d="M15.6,7.8l-1.5-0.7c-0.2-0.1-0.5-0.1-0.7,0L8.3,9.4c-0.2,0.1-0.5,0.1-0.7,0L2.6,7.1C2.3,7,2.1,7,1.9,7.1&#10;&#9;L0.4,7.8c-0.2,0.1-0.2,0.4,0,0.5l7.3,3.3c0.2,0.1,0.5,0.1,0.7,0l7.3-3.3C15.8,8.2,15.8,7.9,15.6,7.8z"/>
	<path fill="#444" d="M15.6,12.1l-1.5-0.7c-0.2-0.1-0.5-0.1-0.7,0l-5.1,2.3c-0.2,0.1-0.5,0.1-0.7,0l-5.1-2.3&#10;&#9;c-0.2-0.1-0.5-0.1-0.7,0l-1.5,0.7c-0.2,0.1-0.2,0.4,0,0.5l7.3,3.3c0.2,0.1,0.5,0.1,0.7,0l7.3-3.3C15.8,12.5,15.8,12.2,15.6,12.1z"/>
</g>
<!-- digg -->
<g transform="translate(44, 880)">
	<path fill="#444" d="M3.203,2.809h1.992v9.604H0V5.602h3.204L3.203,2.809L3.203,2.809z M3.203,10.813V7.204H2v3.609H3.203z
		 M5.996,5.601v6.803h2.001V5.601H5.996L5.996,5.601z M5.996,2.809V4.8h2.001V2.809H5.996L5.996,2.809z M8.795,5.601h5.211v9.2
		H8.795v-1.597h3.202v-0.801H8.795V5.601L8.795,5.601z M11.997,10.813V7.204h-1.198v3.609H11.997z M14.805,5.601H20v9.2h-5.195
		v-1.597h3.189v-0.801h-3.189V5.601L14.805,5.601z M17.994,10.813V7.204h-1.198v3.609H17.994z"/>
</g>
<!-- stumbleupon -->
<g transform="translate(44, 924)">
	<path fill="#444" d="M9.4,6.862l1.121,0.623l1.771-0.599V5.685C12.293,3.613,10.562,2,8.5,2C6.447,2,4.708,3.507,4.708,5.663v5.488
	c0,0.498-0.404,0.901-0.901,0.901c-0.497,0-0.899-0.403-0.899-0.901V8.825H0v2.357c0,2.099,1.701,3.801,3.8,3.801
	c2.081,0,3.771-1.671,3.799-3.747V5.815c0-0.497,0.403-0.898,0.901-0.898c0.497,0,0.9,0.401,0.9,0.898V6.862z M14.091,8.826v2.434
	c0,0.498-0.403,0.9-0.899,0.9c-0.497,0-0.898-0.402-0.898-0.9V8.872l-1.771,0.6L9.4,8.847v2.37c0.017,2.08,1.711,3.767,3.8,3.767
	c2.099,0,3.8-1.702,3.8-3.801V8.825L14.091,8.826L14.091,8.826z"/>
</g>
<!-- evernote -->
<g transform="translate(44, 968)">
	<path fill="#444" d="M14.3,2.8c-0.1-0.6-0.5-1-0.8-1.1c-0.4-0.1-1.1-0.3-2.1-0.4c-0.8-0.1-1.7-0.1-2.2-0.1
	c-0.1-0.4-0.4-0.8-0.7-1C7.8,0,6.3,0,6.3,0c-1,0-1.4,0.1-1.8,0.4c0,0-2.9,2.9-2.9,2.9C1,3.9,1.4,5.6,1.4,5.6l0,0
	c0,0,0.5,3.4,0.9,4.3c0.2,0.3,0.3,0.5,0.6,0.6c0.7,0.3,2.4,0.7,3.2,0.8c0.8,0.1,1.3,0.3,1.6-0.3c0,0,0.4-0.6,0.4-2.3
	c0,0,0.1-0.1,0.1,0c0,0.3-0.1,1.6,0.9,1.9c0.4,0.1,1.1,0.2,1.9,0.3c0.7,0.1,1.2,0.4,1.2,2.1c0,1.1-0.2,1.2-1.4,1.2
	c-1,0-1.3,0-1.3-0.7c0-0.6,0.6-0.6,1.1-0.6c0.2,0,0.1-0.2,0.1-0.5c0-0.4,0.2-0.6,0-0.6c-1.6,0-2.5,0-2.5,2c0,1.8,0.7,2.1,2.9,2.1
	c1.8,0,2.4-0.1,3.1-2.3c0.1-0.4,0.5-1.8,0.7-4.1C14.8,8.2,14.5,3.9,14.3,2.8z M10.7,7.6c0.1-0.4,0.2-1,0.9-1c0.7,0,0.8,0.7,0.8,1.2
	C12.1,7.7,11.3,7.4,10.7,7.6z"></path>
</g>

</svg>