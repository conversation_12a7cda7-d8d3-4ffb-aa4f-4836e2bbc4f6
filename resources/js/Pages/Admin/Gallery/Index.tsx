import React, {useState} from 'react';
import PrimaryButton from "@/Components/PrimaryButton";
import {Head, Link} from "@inertiajs/react";
import {useFormik} from "formik";
import {useDropzone} from "react-dropzone";
import AdminLayout from "@/Layouts/AdminLayout";
import {Button} from "@/Components/ui/button";
import axios from "axios";

/**
 * The Index component is used to display a gallery of images with functionality to upload, preview, and delete images.
 * It utilizes form handling, state management, and React Dropzone for file uploads.
 *
 * @param {Object} props - The properties passed to the Index component.
 * @param {Array} props.images - An array of initial images to populate the gallery.
 * @return {JSX.Element} The JSX markup for the Index component, which includes the gallery display, upload modal, and controls.
 */

interface GalleryIndexProps {
  images: any[];
}

function GalleryIndexPage({images} : GalleryIndexProps) {

  const [gallery, setGallery] = useState(images);
  const [isOpen, setIsOpen] = useState(false);

  const uploadGallery = () => {
    setIsOpen(true);
  }

  const [imagesPreview, setImagesPreview] = React.useState([]);

  const {
    acceptedFiles,
    getRootProps: getRootProps,
    getInputProps: getInputProps,
  } = useDropzone({
    maxFiles: 20,
    accept: {
      'image/*': [],
    },
    onDrop: (acceptedFiles) => {

      let files = acceptedFiles.map(file => URL.createObjectURL(file));
      setImagesPreview(files);
      formik.setFieldValue('images', acceptedFiles);
    },
  });




  const formik = useFormik({
    initialValues: {
      images: [],
    },

    onSubmit: values => {
      axios.post(route('admin.gallery.store'), values, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      }).then((response) => {
        formik.setSubmitting(false);
        // add return gallery to state
        setGallery(response.data);

        // close modal
        setIsOpen(false);

        // reset form
        formik.setFieldValue('images', [])
      }).catch((error) => {
        //Set formik errors
        if (error.response.status === 422) {
          formik.setErrors(error.response.data.errors);
        }
        formik.setSubmitting(false);
      });
    },
  });

  const cancelUpload = () => {
    setIsOpen(false);
    setImagesPreview([])

    // formik.setFieldValue( 'images', [])
    formik.resetForm()
  }


  return (

    <AdminLayout>
      <Head title="Gallery" />

      <div className="py-4">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <h2 className="font-semibold text-xl text-gray-800 leading-tight">Gallery</h2>
            <Button onClick={uploadGallery} className="bg-gray-600 hover:bg-gray-800 text-white py-1 px-4 rounded">
              Upload Gallery
            </Button>
          </div>

          {
            isOpen && (
              <div className="bg-white overflow-hidden mt-3 sm:rounded-lg mb-6">
                <div className="">
                  <form onSubmit={formik.handleSubmit}
                    className=" max-w-4xl space-y-8 mx-auto py-8 px-8">
                    <div>
                      <label htmlFor="images" className="block text-sm font-medium text-gray-700 sr-only">Images</label>
                      <div {...getRootProps({ className: 'border-dashed border-2 bg-slate-50 rounded-lg mt-2 py-10 mb-6 px-10' })}>
                        <input {...getInputProps()} />
                        <p className={'text-sm'}>Drag 'n' Images, or click to select files</p>
                      </div>



                    </div>
                    {/*    display preview */}

                    <div className={'mx-auto mt-3 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6 w-full'}>
                      {imagesPreview.map((file, index) => (
                        <div key={index} className=''>
                          <img src={file} className={'object-cover'} alt='' />
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-between">
                      <Button variant={'destructive'} type={'button'} disabled={formik.isSubmitting} onClick={() => cancelUpload()} className="">
                        Cancel
                      </Button>
                      <PrimaryButton disabled={formik.isSubmitting} type="submit" className="">
                        {
                          formik.isSubmitting ? 'Uploading...' : 'Upload'
                        }
                      </PrimaryButton>

                    </div>
                  </form>
                </div>
              </div>
            )
          }


          <div className="overflow-hidden ">
            <div className="grid grid-cols-2 gap-x-4 gap-y-1 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-6 xl:gap-x-1">

              {
                gallery.map((image, index) => (
                  <div key={index}
                    className="max-w-sm bg-white border border-gray-200  dark:bg-gray-800 dark:border-gray-700">
                    <img className=" h-40 w-full object-cover" src={image.url}
                      alt="" loading='lazy'/>

                    <div className="flex justify-between">
                      <Link href={route('admin.gallery.delete', image.id)} method="post">
                        <button type="submit" className="text-xs px-3 py-2 text-red-600">Delete</button>
                      </Link>
                    </div>
                  </div>
                ))
              }

            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}

export default GalleryIndexPage;
