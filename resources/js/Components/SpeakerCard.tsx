import React from "react";
import { <PERSON> } from "@inertiajs/react";
import { SpeakerType } from "@/types/speaker-type";
import { But<PERSON> } from "@/Components/ui/button";
import { Dot } from "lucide-react";

interface SpeakerCardProps {
  speaker: SpeakerType;
}

function SpeakerCard({ speaker }: SpeakerCardProps) {
  const [showActions, setShowActions] = React.useState<boolean>(false);
  return (
    <div
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      className={"border relative rounded-3xl overflow-hidden h-96"}
    >
      <img
        className={"h-96 z-0 w-full object-cover"}
        src={speaker.image}
        alt={speaker.name}
      />
      <div className="absolute bottom-0 z-10 right-0 left-0">
        <div className="w-full px-4 py-4 pt-12 space-y-1 bg-gradient-to-t from-black/90 to-black/0">
          <Link href={route("speakers.show", speaker.slug)}>
            <h3 className="text-white text-lg font-semibold">{speaker.name}</h3>
          </Link>

          {speaker.title && (
            <div className={"flex items-center"}>
              <svg
                className={"mr-2 w-6 h-6"}
                viewBox="0 0 26 26"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19.4724 2.28369C18.349 2.28369 17.2188 2.74268 16.4115 3.52417C16.6917 5.7771 17.4528 7.21528 18.5477 8.12769C19.6341 9.0331 21.0906 9.44019 22.8324 9.51172C23.4882 8.72705 23.8669 7.70093 23.8669 6.67969C23.8669 5.53384 23.3908 4.38169 22.5806 3.57148C21.7703 2.76123 20.6182 2.28369 19.4724 2.28369ZM15.7477 4.35273C15.3176 5.04824 15.0763 5.86572 15.0763 6.67969C15.0763 7.82554 15.5539 8.97769 16.3641 9.78789C17.1744 10.5981 18.3265 11.0757 19.4724 11.0757C20.3845 11.0757 21.3006 10.7732 22.045 10.2411C20.5045 10.0849 19.1336 9.63271 18.0472 8.72744C16.9107 7.78032 16.1159 6.35244 15.7477 4.35278L15.7477 4.35273ZM14.3912 7.65937L11.2433 11.7502C11.3848 12.7072 11.7539 13.4641 12.3221 14.0054C12.893 14.5495 13.6753 14.8901 14.6949 14.9637L18.5675 11.7762C17.5324 11.5894 16.5587 11.0873 15.8118 10.3403C15.0829 9.61147 14.5883 8.66621 14.3911 7.65933L14.3912 7.65937ZM15.1297 10.682H15.1312C15.1399 10.6817 15.1485 10.6817 15.1572 10.682C15.2326 10.6825 15.3063 10.7048 15.3693 10.7463C15.4323 10.7877 15.482 10.8465 15.5124 10.9155C15.5427 10.9846 15.5524 11.061 15.5403 11.1354C15.5281 11.2098 15.4947 11.2792 15.4441 11.3351L13.7686 13.2286C13.7349 13.2679 13.6937 13.3 13.6474 13.3232C13.6012 13.3464 13.5508 13.3602 13.4991 13.3637C13.4475 13.3673 13.3957 13.3606 13.3466 13.3439C13.2976 13.3273 13.2524 13.3011 13.2136 13.2668C13.1748 13.2326 13.1432 13.191 13.1206 13.1444C13.098 13.0978 13.0849 13.0473 13.082 12.9956C13.0792 12.9439 13.0866 12.8922 13.1039 12.8434C13.1211 12.7946 13.1479 12.7497 13.1827 12.7114L14.8581 10.8178C14.892 10.7782 14.9336 10.7458 14.9802 10.7225C15.0268 10.6992 15.0777 10.6854 15.1297 10.682ZM10.6177 12.5635L5.51057 19.1995C5.68084 19.8502 5.86829 20.2222 6.12093 20.4705C6.36653 20.7121 6.72571 20.8836 7.27146 21.0764L13.8785 15.6351C13.0451 15.4588 12.3358 15.0978 11.7835 14.5716C11.2295 14.0437 10.84 13.3593 10.6177 12.5635ZM5.16731 20.4919C4.722 20.7547 4.31917 21.1794 4.03811 21.6836C3.56663 22.5292 3.47561 23.5018 3.92825 24.148C4.25369 24.3598 4.44417 24.3829 4.60423 24.3601C4.69182 24.3476 4.78089 24.3135 4.87884 24.2686C4.72356 23.801 4.57122 23.2466 4.75677 22.6419C4.90471 22.16 5.26082 21.712 5.90276 21.2991C5.7851 21.2185 5.67481 21.1276 5.57317 21.0275C5.41609 20.8731 5.28313 20.6965 5.16726 20.4919L5.16731 20.4919Z"
                  fill="#F15A29"
                />
              </svg>

              <p className={"text-white text-sm font-semibold"}>
                {speaker.title}
              </p>
            </div>
          )}

          {showActions && (
            <>
              <div className="">
                {speaker.key_titles_array
                  .slice(0, 3)
                  .map((key_title, index) => (
                    <div key={index} className={"flex items-center"}>
                      <Dot className={"w-6 h-6 mr-1 text-white"} />

                      <p className={"text-white text-sm"}>{key_title}</p>
                    </div>
                  ))}
              </div>
              <div className={"flex justify-between pt-4 items-center gap-2"}>
                <Link
                  className={
                    "py-2 px-3 bg-white text-mena-100 w-full rounded-2xl text-sm text-center"
                  }
                  href={route("speakers.show", speaker.slug)}
                >
                  See Profile
                </Link>
                <Link
                  className={
                    "hidden py-2 px-3 text-white w-full bg-mena-100 rounded-2xl text-sm text-center"
                  }
                  href={route("speakers.show", speaker.slug)}
                >
                  Explore
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default SpeakerCard;
