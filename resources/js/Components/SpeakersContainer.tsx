import React from "react";
import { motion } from "framer-motion";
import {SpeakerType} from "@/types/speaker-type";

interface SpeakersContainerProps {
  speakers: SpeakerType[]
}

export const SpeakersContainer = ({speakers} : SpeakersContainerProps) => {
  const rows = new Array(150).fill(1);
  const cols = new Array(100).fill(1);
  let colors = [
    "--sky-300",
    "--pink-300",
    "--green-300",
    "--yellow-300",
    "--red-300",
    "--purple-300",
    "--blue-300",
    "--indigo-300",
    "--violet-300",
  ];
  const getRandomColor = () => {
    return colors[Math.floor(Math.random() * colors.length)];
  };

  return (
    <div
      style={{
        transform: `translate(-40%,-60%) skewX(-48deg) skewY(14deg) scale(0.675) rotate(0deg) translateZ(0)`,
      }}
      className="absolute left-1/4 p-4 -top-1/4 flex  -translate-x-1/2 -translate-y-1/2 w-full h-screen z-0 "
    >
     {
  speakers.map((speaker, j) => (
    <motion.div
      whileHover={{
        backgroundColor: `var(${getRandomColor()})`,
        transition: { duration: 0 },
      }}
      animate={{
        transition: { duration: 2 },
      }}
      key={`speaker` + j}
      className="w-60 h-96 border-r border-t border-slate-700 relative"
    >
      <img src={speaker.image} alt={speaker.name} className="h-96 w-60 object-cover" />
    </motion.div>
  ))
}
    </div>
  );
};

