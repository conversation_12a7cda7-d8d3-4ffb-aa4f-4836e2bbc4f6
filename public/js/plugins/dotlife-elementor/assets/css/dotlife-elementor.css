/*
	Begin CSS for global animation
*/

@keyframes tilt {
  0% {
    -webkit-transform: rotateX(-30deg);
    -moz-transform: rotateX(-30deg);
    transform: rotateX(-30deg); }

  25% {
    -webkit-transform: rotateX( 30deg);
    -moz-transform: rotateX( 30deg);
    transform: rotateX( 30deg); }

  50% {
    -webkit-transform: rotateY(-30deg);
    -moz-transform: rotateY(-30deg);
    transform: rotateY(-30deg); }

  75% {
    -webkit-transform: rotateY( 30deg);
    -moz-transform: rotateY( 30deg);
    transform: rotateY( 30deg); }

  100% {
    -webkit-transform: rotateZ( 20deg);
    -moz-transform: rotateZ( 20deg);
    transform: rotateZ( 20deg); } }

@keyframes wave {
  0% {
    -webkit-transform: rotateZ(0deg) translate3d(0,100px,0) rotateZ(0deg);
    -moz-transform: rotateZ(0deg) translate3d(0,100px,0) rotateZ(0deg);
    transform: rotateZ(0deg) translate3d(0,100px,0) rotateZ(0deg); }

  100% {
    -webkit-transform: rotateZ(360deg) translate3d(0,100px,0) rotateZ(-360deg);
    -moz-transform: rotateZ(360deg) translate3d(0,100px,0) rotateZ(-360deg);
    transform: rotateZ(360deg) translate3d(0,100px,0) rotateZ(-360deg); } }

@keyframes swing2 {
  0% {
    -webkit-transform: translate3d(70px,0,0) rotateZ(10deg);
    -moz-transform: translate3d(70px,0,0) rotateZ(10deg);
    transform: translate3d(70px,0,0) rotateZ(10deg); }

  100% {
    -webkit-transform: translate3d(-70px,0,0) rotateZ(-10deg);
    -moz-transform: translate3d(-70px,0,0) rotateZ(-10deg);
    transform: translate3d(-70px,0,0) rotateZ(-10deg); } }
    
@keyframes bounce {
  0% {
    -webkit-transform: translate3d(0,0,0);
    -moz-transform: translate3d(0,100px,0);
    transform: translate3d(0,100px,0); }

  100% {
    -webkit-transform: translate3d(0,0,0);
    -moz-transform: translate3d(0,0,0);
    transform: translate3d(0,0,0); } }
    
@keyframes scale {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    transform: scale(1); }

  100% {
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    transform: scale(2); } }
    
@keyframes spin {
  0% { transform:rotate(0deg) }
  100% { transform:rotate(360deg) }
}
    
.if_swing1 {
  -webkit-animation: swing 4s infinite alternate linear;
  -moz-animation: swing 4s infinite alternate linear;
  animation: swing 4s infinite alternate linear; 
}

.if_swing2 {
  -webkit-animation: swing2 4s 0.1s infinite alternate linear;
  -moz-animation: swing2 4s 0.1s infinite alternate linear;
  animation: swing2 4s 0.1s infinite alternate linear;
}

.if_wave {
  -webkit-animation: wave 8s 0.1s infinite linear;
  -moz-animation: wave 8s 0.1s infinite linear;
  animation: wave 8s 0.1s infinite linear;
}

.if_tilt {
  -webkit-animation: tilt 4s infinite alternate linear;
  -moz-animation: tilt 4s infinite alternate linear;
  animation: tilt 4s infinite alternate linear; 
}

.if_bounce {
  -webkit-animation: bounce 4s infinite alternate linear;
  -moz-animation: bounce 4s infinite alternate linear;
  animation: bounce 4s infinite alternate linear; 
}

.if_scale {
  -webkit-animation: scale 2s infinite alternate linear;
  -moz-animation: scale 2s infinite alternate linear;
  animation: scale 2s infinite alternate linear; 
}

.if_spin {
  -webkit-animation: spin 2s infinite alternate linear;
  -moz-animation: spin 2s infinite alternate linear;
  animation: spin 2s infinite alternate linear; 
}

.no-smooved {
	opacity: 1 !important;
	transform: translate(0px, 0px) !important;
}

html.elementor-html .smoove
{
	opacity: 1 !important;
	transform: translate(0px, 0px) !important;
}

.smoove
{
	opacity: 0;
}

.elementor-widget-image.animation,
.elementor-widget-image.themegoods-image-animation
{
    opacity: 1 !important;
}

#page_content_wrapper .elementor-widget-image.themegoods-image-animation-slide_down:after,
#page_content_wrapper .elementor-widget-image.themegoods-image-animation-slide_up:after,
#page_content_wrapper .elementor-widget-image.themegoods-image-animation-zoom_in:after
{
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background: #fff;
	display: block;
	content: '';
    transform: scaleY(1);
    transform-origin: 50% 100;
    content: '';
    transition: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);
    transform-origin: 0% 100%;
}

#page_content_wrapper .elementor-widget-image.themegoods-image-animation-slide_up:after
{
	transform-origin: 0% 0%;
}

#page_content_wrapper .elementor-widget-image.themegoods-image-animation-zoom_in:after,
#page_content_wrapper .elementor-widget-image.themegoods-image-animation-zoom_out:after
{
	background: transparent !important;
	border: 200px solid #fff;
	transform-origin: 50% 50%;
	opacity: 1;
	transition: all 1s cubic-bezier(0.645, 0.045, 0.355, 1);
}

#page_content_wrapper .elementor-widget-image.themegoods-image-animation-zoom_in .elementor-image,
#page_content_wrapper .elementor-widget-image.themegoods-image-animation-zoom_out .elementor-image
{
	overflow: hidden;
}

#page_content_wrapper .elementor-widget-image.themegoods-image-animation-zoom_in .elementor-image img,
{
	transform: scale(1.7);
	transition: all 1s cubic-bezier(0.645, 0.045, 0.355, 1);
    transform-origin: 50% 50%;
}

#page_content_wrapper .elementor-widget-image.themegoods-image-animation-zoom_out .elementor-image img
{
	transform: scale(0.4);
	transition: all 1s cubic-bezier(0.645, 0.045, 0.355, 1);
    transform-origin: 50% 50%;
}

#page_content_wrapper .elementor-widget-image.smooved.animation:after,
#page_content_wrapper .elementor-widget-image.smooved.themegoods-image-animation-slide_down:after,
#page_content_wrapper .elementor-widget-image.smooved.themegoods-image-animation-slide_up:after
{
	transform: scaleY(0);
}

#page_content_wrapper .elementor-widget-image.smooved.themegoods-image-animation-zoom_in:after,
#page_content_wrapper .elementor-widget-image.smooved.themegoods-image-animation-zoom_out:after
{
	border-width: 0;
}

#page_content_wrapper .elementor-widget-image.smooved.themegoods-image-animation-zoom_in .elementor-image img,
#page_content_wrapper .elementor-widget-image.smooved.themegoods-image-animation-zoom_out .elementor-image img
{
	transform: scale(1);
	opacity: 1;
}

html.elementor-html #page_content_wrapper .elementor-widget-image:after,
{
	display: none;
}

.init-smoove
{
	opacity: 0;
	transform-origin: 50% 50%;
}

html.elementor-html .init-smoove
{
	opacity: 1;
}

.elementor-accordion-item .elementor-tab-title.elementor-active a:hover
{
	color: #fff;
}

.parallax-mirror img
{
	transform-origin: 50% 50%;
    object-position: 50% 50%;
    min-height: 0px;
    max-width: none;
    object-fit: cover;
    will-change: transform;
}

.jarallax {
    position: relative;
    z-index: 0;
    will-change: transform;
}

.jarallax-img {
    position: absolute;
    object-fit: cover;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    will-change: transform;
}


/*
	End CSS for global animation
*/


/*
	Begin CSS for global Elementor styling
*/

body.gallery-horizontal,
body.elementor-page
{
	overflow-x: hidden;
}

.elementor .expand_width .elementor-image img
{
	max-width: 102%;
}

.translate_bottom_rotate_up
{
	transform: translateY(-20%) rotate(5deg);
}

.translate_bottom_rotate_down
{
	transform: translateY(-20%) rotate(-5deg);
}

.translate_left_less
{
    transform: translateX(-8%);
}

.translate_left
{
    transform: translateX(-20%);
}

.translate_left_more
{	
	transform: translateX(-30%);
}

.translate_left_half
{
	transform: translateX(-50%);
}

.translate_right_less
{
    transform: translateX(8%);
}

.translate_right_less_down
{
    transform: translateX(8%);
    transform: translateY(20%);
}

.translate_right_less_down_more
{
    transform: translateX(8%);
    transform: translateY(30%);
}

.translate_right
{
    transform: translateX(20%);
}

.translate_right_more
{
    transform: translateX(30%);
}

.translate_top_less
{
	transform: translateY(8%);
}

.translate_top_little
{
	transform: translateY(15%);
}

.translate_top
{
	transform: translateY(20%);
}

.translate_top_more
{
	transform: translateY(30%);
}

.translate_bottom_less
{
	transform: translateY(-8%);
}

.translate_bottom
{
	transform: translateY(-20%);
}

.translate_bottom_more
{
	transform: translateY(-30%);
}

.hover_up
{
	-webkit-transition: all 0.5s;
	-moz-transition: all 0.5s;
	transition: all 0.5s;
}

.hover_up:hover
{
	box-shadow: 0 30px 65px rgba(0,0,0,0.15);
	transform: translateY(-10px);
}

.elementor-heading-title > div
{
	display: inline;
}

.newsletter_box
{
	width: 80%;
	margin: auto;
}

.newsletter_box .newsletter_notice
{
	display: none;
}

.newsletter_box .input_wrapper
{
	padding: 0 !important;
	display: inline;
	margin-right: 10px;
}

.newsletter_box .input_wrapper input[type=email]
{
	width: calc(100% - 170px);
}

.widget-image-caption.wp-caption-text
{
	opacity: 0.6;
	font-size: 12px;
	padding: 30px 0 30px 0;
	width: 50%;
	display: inline-block;
}

.elementor-toggle .elementor-tab-title:not(.elementor-active)
{
	border-radius: 5px;
	border: 1px solid #d4d4d4;
}

.elementor-toggle .elementor-tab-title.elementor-active
{
	border-radius: 0;
	-webkit-border-top-left-radius: 5px;
	-webkit-border-top-right-radius: 5px;
	-moz-border-radius-topleft: 5px;
	-moz-border-radius-topright: 5px;
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;

	border: 1px solid #d4d4d4;
	border-bottom: 0;
}

.elementor-toggle .elementor-tab-content
{
	border-radius: 5px;
	border: 1px solid #d4d4d4;
}

.elementor-toggle .elementor-tab-content.elementor-active
{
	border-radius: 0;
	-webkit-border-bottom-right-radius: 5px;
	-webkit-border-bottom-left-radius: 5px;
	-moz-border-radius-bottomright: 5px;
	-moz-border-radius-bottomleft: 5px;
	border-bottom-right-radius: 5px;
	border-bottom-left-radius: 5px;

	border: 1px solid #d4d4d4;
	border-top: 0;
}

.verline
{
	margin: 0 12px;
    height: 1px;
    vertical-align: middle;
    width: 80px;
    background-color: #000;
    display: inline-block;
}

.tg_currency
{
	font-size: 18px;
	line-height: 36px;
	vertical-align: top;
}

.width100 .elementor-button
{
	width: 100%;
}

.black_bg label
{
	color: #fff !important;
}

.black_bg input[type=submit]
{
	border-color: #fff !important;
}

.black_bg input[type=text], .black_bg input[type=password], .black_bg input[type=email], .black_bg input[type=url], .black_bg input[type=tel], .black_bg input[type=date], .black_bg textarea, .black_bg input[type=submit]
{
	background: transparent !important;
}

.elementor-widget-image.rotated .elementor-image .wp-caption
{
	position: relative;
}

.elementor-widget-image.rotated .elementor-image .wp-caption .widget-image-caption
{
    z-index: 2;
    transform-origin: left bottom;
    transform: rotate(-90deg);
    position: absolute;
    text-align: left;
    opacity: 1;
}

.elementor-widget-image.rotated.right .elementor-image .wp-caption .widget-image-caption
{
	left: 100%;
	bottom: 120px;
}

.elementor-widget-image.rotated.right.top .elementor-image .wp-caption .widget-image-caption
{
	left: 100%;
	top: 120px;
	bottom: auto;
}

.elementor-widget-image.rotated.left .elementor-image .wp-caption .widget-image-caption
{
	right: 100%;
	bottom: 120px;
	left: 120px;
	vertical-align: top;
}

.elementor-widget-image.rotated.left.top .elementor-image .wp-caption .widget-image-caption
{
	right: 100%;
	top: 120px;
	bottom: auto;
}

.elementor-widget-image.rotated .elementor-image .wp-caption .widget-image-caption .number
{
	font-size: 60px;
    line-height: 1;
    vertical-align: baseline;
    font-weight: 600;
}

.elementor-widget-image.rotated.left .elementor-image .wp-caption .widget-image-caption .number
{
	vertical-align: top;
}

.elementor-section-height-min-height .elementor-container .elementor-row
{
	height: 100%;
}

.fullheight
{
	height: 100%;
}

.white_text
{
	color: #fff;	
}

.white_text input[type=text],
.white_text input[type=password],
.white_text input[type=email],
.white_text input[type=url],
.white_text input[type=tel],
.white_text input[type=date],
.white_text textarea,
.white_text select
{
	background: transparent;
	color: #fff;
	border-color: #fff;
}

/*.elementor-section-stretched > .elementor-container 
{
    max-width: none !important;
}

.elementor-section-stretched
{
	z-index: 2;
}*/

body.elementor-fullscreen.elementor-editor-active:hover .header_style_wrapper,
body.tg_menu_transparent.elementor-editor-active:hover .header_style_wrapper,
body.elementor-editor-active:hover .header_style_wrapper
{
	display: none;
}

body.elementor-fullscreen
{
	overflow: hidden;
	width: 100%;
	height: 100vh;
	opacity: 1;
	transition: opacity 0.3s;
}

body.elementor-fullscreen #page_content_wrapper
{
	padding: 0 !important;
	width: 100%;
	max-width: none;
}

body.elementor-fullscreen #page_content_wrapper .elementor-section.elementor-section-stretched
{
	left: 0 !important;
}

body.elementor-fullscreen #page_content_wrapper .elementor-section.elementor-section-boxed > .elementor-container
{
	max-width: none;
}

body.elementor-fullscreen #toTop
{
	display: none !important;
}

body.loading
{
	opacity: 0;
}

body.elementor-fullscreen.elementor-editor-active .elementor.elementor-edit-mode .elementor-editor-widget-settings,
body.elementor-fullscreen.elementor-editor-active .elementor.elementor-edit-mode .elementor-element>.elementor-element-overlay
{
	z-index: 9999;
}

body.elementor-fullscreen #page_caption,
body.elementor-fullscreen #footer_wrapper,
body.elementor-fullscreen.elementor-editor-active .elementor.elementor-edit-mode .elementor-editor-element-duplicate
{
	display: none;
}

.elementor-editor-preview .elementor-widget-empty.elementor-widget-dotlife-gallery-fullscreen,
.elementor-editor-preview .elementor-widget-empty.elementor-widget-dotlife-slider-vertical-parallax
{
	display: block;
}

body.elementor-fullscreen.elementor-editor-active .elementor.elementor-edit-mode .elementor-editor-element-settings,
body.elementor-editor-active.tg_menu_transparent .elementor.elementor-edit-mode .elementor-editor-element-settings
{
	top: 27px;
}

body.elementor-editor-active .header_style_wrapper
{
	z-index: 3;
}

body.elementor-fullscreen.elementor-editor-preview .elementor-widget-empty
{
	display: block;
}

.leftmenu.elementor-editor-active .elementor.elementor-edit-mode .elementor-editor-section-settings
{
	top: 26px;
}

.icon-scroll,
.icon-scroll:before {
  position: absolute;
  left: 50%;
}

.icon-scroll {
  width: 20px;
  height: 35px;
  margin-left: -10px;
  position: fixed;
  bottom: 40px;
  margin-top: -35px;
  box-shadow: inset 0 0 0 3px #fff;
  border-radius: 25px;
  z-index: 99;
}

.icon-scroll:before {
  content: '';
  width: 4px;
  height: 4px;
  background: #fff;
  margin-left: -2px;
  top: 4px;
  border-radius: 4px;
  -webkit-animation-duration: 1.5s;
  animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-name: scroll;
  animation-name: scroll;
}

@-webkit-keyframes scroll {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(23px);
    transform: translateY(23px);
  }
}

@keyframes scroll {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(23px);
    transform: translateY(23px);
  }
}

.elementor-section .elementor-container .tg-container
{
	display: block;
}

/*
	End CSS for global Elementor styling
*/

.tg-gallery-lightbox img
{
	display: block;
}

/*
	Begin CSS for global theme styling
*/

.tg_one_cols
{
	width: 100%;
	clear: both;
}

.tg_two_cols
{
	width: 48%;
	float: left;
}

.tg_two_cols.last
{
	width: 48%;
	float: right;
}

.tg_two_cols:not(.filterable):nth-child(2n+1)
{
	clear: both;
}

.tg_three_cols
{
	width: 30.66%;
	margin-right: 4%;
	float: left;
}

.tg_three_cols.last
{
	width: 30.66%;
	margin-right: 0;
	float: right;
}

.tg_three_cols:not(.filterable):nth-child(3n+1)
{
	clear: both;
}

.tg_four_cols
{
	width: 22.75%;
	margin-right: 3%;
	float: left;
}

.tg_four_cols.last
{
	width: 22.75%;
	margin-right: 0;
	float: right;
}

.tg_four_cols:not(.filterable):nth-child(4n+1)
{
	clear: both;
}

.tg_five_cols
{
	width: 17.6%;
	margin-right: 3%;
	float: left;
}

.tg_five_cols.last
{
	width: 17.6%;
	margin-right: 0;
	float: right;
}

.tg_five_cols:not(.filterable):nth-child(5n+1)
{
	clear: both;
}

/*
	End CSS for global theme styling
*/


/*
	Begin portfolio filterable	
*/
.portfolio_filter_wrapper
{
	margin: auto;
	margin-bottom: 40px;
	text-align: center;
}

.portfolio_filter_wrapper a.filter_tag_btn
{
	display: inline-block;
	margin: 0 10px 0 10px;
}

.portfolio_filter_wrapper a.filter_tag_btn:hover,
.portfolio_filter_wrapper a.filter_tag_btn.active
{
	border-bottom: 2px solid #000;
}

.tile.scale-anm,
.gallery_grid_content_wrapper.do_masonry .gallery_grid_item.tile.scale-anm
{
  	transform: scale(1);
  	opacity: 1;
}

.tile,
.gallery_grid_content_wrapper.do_masonry .gallery_grid_item.tile
{ 
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transition: all 350ms ease;
    transition: all 350ms ease;
    opacity: 1;
}

/*
	End portfolio filterable	
*/


/*
	Begin CSS for blog-posts	
*/

.post_header_title
{
	clear: both;
}

.post_img
{
	position: relative;
	height: auto;
	line-height: 0;
	width: 100%;
	margin-bottom: 20px;
}

.post_img img
{
	max-width: 100%;
	height: auto !important;
}

@-webkit-keyframes lazy_color_change {
	from { background-color: #cccccc; }
	to { background-color: #f0f0f0; }
}
@-moz-keyframes lazy_color_change {
	from { background-color: #cccccc; }
	to { background-color: #f0f0f0; }
}
@-o-keyframes lazy_color_change {
	from { background-color: #cccccc; }
	to { background-color: #f0f0f0; }
}
@keyframes lazy_color_change {
	from { background-color: #cccccc; }
	to { background-color: #f0f0f0; }
}

.post_img_hover.lazy
{
	-webkit-animation: lazy_color_change 1s infinite alternate;
	-moz-animation: lazy_color_change 1s infinite alternate;
	-ms-animation: lazy_color_change 1s infinite alternate;
	-o-animation: lazy_color_change 1s infinite alternate;
	animation: lazy_color_change 1s infinite alternate;
}

.post_img_hover
{
  	position: relative;
  	display: inline-block;
  	width: 100%;
}

.post_img_hover img
{
	width: 100%;
	height: auto;
}
  
.post_img_hover:not(.lazy)
 {
  background: #222222;
  background: -moz-linear-gradient(90deg, #222222 0%, #444444 100%, #666666 100%);
  background: -webkit-linear-gradient(90deg, #222222 0%, #444444 100%, #666666 100%);
  background: linear-gradient(90deg, #222222 0%, #444444 100%, #666666 100%);
}

.blog-posts-classic .post_img_hover
{
	min-height: 300px;
}

.post_img_hover img,
.post_img_hover:before,
.post_img_hover:after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.post_img_hover img {
  max-width: 100%;
  backface-visibility: hidden;
  vertical-align: top;
}

.post_img_hover:before,
.post_img_hover:after {
  content: '';
  background-color: #fff;
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  opacity: 0;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.post_img_hover:before {
  width: 60px;
  height: 1px;
  left: 100%;
}

.post_img_hover:after {
  height: 60px;
  width: 1px;
  top: 0%;
}

.post_img_hover a 
{
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.post_img_hover:hover img,
.post_img_hover.hover img 
{
  zoom: 1;
  filter: alpha(opacity=30);
  -webkit-opacity: 0.3;
  opacity: 0.3;
}

.post_img_hover:hover:before,
.post_img_hover.hover:before,
.post_img_hover:hover:after,
.post_img_hover.hover:after 
{
  opacity: 1;
  top: 50%;
  left: 50%;
}

.post_img_hover .post_type_icon,
.blog_post_content_wrapper.layout_grid_no_space .post_content_wrapper .post_type_icon,
.blog_post_content_wrapper.layout_metro_no_space .post_content_wrapper .post_type_icon
{
	position: absolute;
	right: 20px;
    bottom: -25px;
	z-index: 2;
	display: inline-block;
	border-radius: 50px;
	line-height: 62px;
	width: 60px;
	height: 60px;
	text-align: center;
	box-shadow: 0 10px 40px rgba(0,0,0,0.15);
	
	-ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
    
    transition: all .21s cubic-bezier(.5,.5,.4,.9);
}

.blog_post_content_wrapper.layout_grid_no_space .post_content_wrapper .post_type_icon,
.blog_post_content_wrapper.layout_metro_no_space .post_content_wrapper .post_type_icon
{
	bottom: -100px;
	top: auto;
}

.blog_post_content_wrapper.layout_grid_no_space .post_detail,
.blog_post_content_wrapper.layout_metro_no_space .post_detail
{
	color: #fff;
}

.post_related h3
{
	text-align: center;
	margin-bottom: 20px;
}

.post_related .post_img_hover .post_type_icon
{
	width: 50px;
	height: 50px;
	right: 20px;
	bottom: -20px;
	line-height: 50px;
}

.post_img_hover:hover .post_type_icon
{
	-ms-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -o-transform: scale(1.2);
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
}

.post_img_hover .post_type_icon i
{
	color: #fff;
	font-size: 17px;
}

.post_info_cat
{
	font-size: 12px;
	margin-top: 20px;
	margin-bottom: 5px;
	font-weight: 300;
}

body.elementor-page .blog-posts-classic.type-post 
{
    margin-bottom: 60px;
}

.post_header h5
{
	font-size: 24px;
}

.post_header h6
{
	font-size: 20px;
}

.post_header h5 a, .post_header h6 a
{
	color: #222222;
}

.post_attribute a
{
	position: relative;
	padding: 3px 0 3px 0;
}

.post_attribute a:before
{
	opacity: 0.2;
}

.post_excerpt
{
	width: 100%;
	margin: auto;
}

.post_content_wrapper.text_left
{
	text-align: left;
}

.post_content_wrapper.text_center
{
	text-align: center;
}

.post_content_wrapper.text_right
{
	text-align: right;
}

.blog_post_content_wrapper.layout_grid .post_img,
.blog_post_content_wrapper.layout_masonry .post_img,
.blog_post_content_wrapper.layout_metro .post_img,
.blog_post_content_wrapper.layout_classic .post_img
{
	margin-bottom: 0;
}

.blog_post_content_wrapper.layout_grid .post_content_wrapper,
.blog_post_content_wrapper.layout_masonry .post_content_wrapper,
.blog_post_content_wrapper.layout_metro .post_content_wrapper,
.blog_post_content_wrapper.layout_classic .post_content_wrapper
{
	padding: 30px;
	background: #ffffff;
}

.type-post.blog-posts-grid
{
	width: calc(33.33% - 30px);
	margin-right: 45px;
	margin-bottom: 45px;
	box-sizing: border-box;
	float: left;
}

.type-post.blog-posts-grid:nth-child(3n)
{
	float: right;
	margin-right: 0;
}

.type-post.blog-posts-grid:nth-child(3n+1)
{
	clear: both;
}

.blog_post_content_wrapper.layout_grid .post_img_hover
{
	min-height: 180px;
}

.blog_post_content_wrapper.layout_list .post_img_hover {
	border-radius: 5px;
}

#page_content_wrapper .inner .sidebar_content.page_content .blog-posts-grid .post_img_hover
{
	min-height: 130px;
}

.blog-posts-grid_no_space .post_header h5
{
	font-size: 22px;
}

.blog-posts-grid_no_space .post_header h5,
.blog-posts-grid .post_header h5, 
.blog-posts-masonry .post_header h5
{
	font-size: 20px;
}

.blog-posts-grid .post_wrapper .post_header_wrapper p,
#page_content_wrapper .blog-posts-grid .post_wrapper .post_header_wrapper p
{
	padding-top: 10px;
	padding-bottom: 20px;
	clear: both;
}

.type-post.blog-posts-grid_no_space,
.type-post.blog-posts-metro_no_space
{
	width: 25%;
	margin: 0;
	box-sizing: border-box;
	float: left;
	background-position: center center;
	background-repeat: no-repeat;
	height: 550px;
	position: relative;
	transform-style: preserve-3d;
	overflow: visible;
}

.type-post.blog-posts-metro_no_space
{
	height: 600px;
}

.type-post.blog-posts-metro_no_space.large_grid
{
	width: 50%;
}

.type-post.blog-posts-metro_no_space.large_grid .post_header h5
{
	font-size: 28px;
}

.type-post.blog-posts-metro_no_space .post_header h5
{
	font-size: 20px;
}

.type-post.blog-posts-grid_no_space:hover,
.type-post.blog-posts-metro_no_space:hover
{
	z-index: 3;
}

@media not all and (min-resolution:.001dpcm) { @media {
    .type-post.blog-posts-grid_no_space:hover,
	.type-post.blog-posts-metro_no_space:hover
	{
		z-index: 1;
	}
	
	.type-post.blog-posts-grid_no_space.blog-tilt,
	.type-post.blog-posts-metro_no_space.blog-tilt
	{
		transform: none !important;
	}
}}

.type-post.blog-posts-grid_no_space:nth-child(4n+1)
{
	clear: both;
}

.type-post.blog-posts-grid_no_space .bg_overlay,
.type-post.blog-posts-metro_no_space .bg_overlay
{
	background-color: rgba(0,0,0,0.3);
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.type-post.blog-posts-grid_no_space:hover .bg_overlay,
.type-post.blog-posts-metro_no_space:hover .bg_overlay
{
	background-color: rgba(0,0,0,0.2);
}

.type-post.blog-posts-grid_no_space .post_wrapper,
.type-post.blog-posts-metro_no_space .post_wrapper
{
	display: table;
	width: 100%;
	height: 100%;
}

.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper
{
	display: table-cell;
	vertical-align: middle;
}

.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper .post_header,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper .post_header
{
	box-sizing: border-box;
	padding: 40px;
	position: relative;
	transform: translateZ(20px);
}

.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper .post_header,
.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper .post_header .post_detail.single_post .post_info_cat,
.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper .post_header .post_detail.single_post .post_info_cat a,
.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper .post_header .post_header_title h5 a,
.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper .post_header .post_button_wrapper a,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper .post_header,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper .post_header .post_detail.single_post .post_info_cat,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper .post_header .post_detail.single_post .post_info_cat a,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper .post_header .post_header_title h5 a,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper .post_header .post_button_wrapper a
{
	color: #fff;
}

.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper .post_header .post_button_wrapper .post_attribute,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper .post_header .post_button_wrapper .post_attribute
{
	opacity: 0.7;
}

.type-post.blog-posts-grid_no_space .post_wrapper .post_content_wrapper .post_header .post_button_wrapper .post_attribute a:before,
.type-post.blog-posts-metro_no_space .post_wrapper .post_content_wrapper .post_header .post_button_wrapper .post_attribute a:before
{
	background-color: #fff;
}

.pagination.blog-posts-grid_no_space,
.pagination_detail.blog-posts-grid_no_space,
.pagination.blog-posts-metro_no_space,
.pagination_detail.blog-posts-metro_no_space
{
	margin-top: 60px;
}

.elementor-section-stretched .pagination.blog-posts-grid_no_space,
.elementor-section-stretched .pagination.blog-posts-metro_no_space
{
	padding-left: 90px;
}

.pagination_detail.blog-posts-grid_no_space,
.pagination_detail.blog-posts-metro_no_space
{
	padding-right: 90px;
}

.type-post.blog-posts-masonry
{
	width: calc(33.33% - 30px);
	padding-bottom: 45px;
	box-sizing: border-box;
	float: left;
	
	-webkit-transform: translateX(-40px);
    transform: translateX(-40px);
	-webkit-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	opacity: 0;
}

.type-post.blog-posts-masonry.is-showing
{
	-webkit-transform: translateX(0px);
    transform: translateX(0px);
	opacity: 1;
}

.type-post.blog-posts-list .post_wrapper,
.type-post.blog-posts-list_circle .post_wrapper
{
	width: 100%;
	float: left;
}

.layout_list .type-post.blog-posts-list:first-child,
.layout_list_circle .type-post.blog-posts-list_circle:first-child
{
	margin-top: 0;
}

.layout_list .type-post.blog-posts-list,
.layout_list_circle .type-post.blog-posts-list_circle
{
	margin-top: 30px;
}

.type-post.blog-posts-list .post_img
{
	width: 40%;
	margin-right: 30px;
	float: left;
}

.type-post.blog-posts-list_circle .post_img
{
	width: 30%;
	margin-right: 50px;
	float: left;
}

.type-post.blog-posts-list .post_content_wrapper
{
	width: calc(60% - 30px);
	float: left;
	clear: none;
}

.type-post.blog-posts-list_circle .post_content_wrapper
{
	width: calc(70% - 50px);
	float: left;
	clear: none;
}

.blog-posts-list .post_img_hover
{
	min-height: 280px;
}

.blog-posts-list_circle .post_img_hover
{
	border-radius: 50%;
	min-height: 220px;
}

.blog-posts-list_circle .post_img_hover img
{
	border-radius: 50%;
}

.blog-posts-list_circle .post_img_hover .post_type_icon
{
	bottom: -10px;
}

.pagination.blog-posts-list,
.pagination_detail.blog-posts-list,
.pagination.blog-posts-list_circle,
.pagination_detail.blog-posts-list_circle
{
	margin-top: 60px;
}

.blog-posts-list .post_header h5,
.blog-posts-list_circle .post_header h5
{
	font-size: 22px;
}

.post_metro_left_wrapper,
.post_metro_right_wrapper
{
	width: calc(50% - 20px);
	float: left;
}

.post_metro_right_wrapper
{
	float: right;
}

.post_metro_left_wrapper .post_header h5
{
	font-size: 28px;
}

.post_metro_right_wrapper .post_header h5
{
	font-size: 18px;
}

.post_metro_right_wrapper .blog-posts-metro
{
	width: calc(50% - 20px);
	float: left;
	box-sizing: border-box;
	padding-bottom: 40px;
	
	-webkit-transform: translateX(-40px);
    transform: translateX(-40px);
	-webkit-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	opacity: 0;
}

.post_metro_right_wrapper .blog-posts-metro.is-showing
{
	-webkit-transform: translateX(0px);
    transform: translateX(0px);
	opacity: 1;
}

.post_metro_right_wrapper .blog-posts-metro:nth-child(2n)
{
	float: right;
}

/*
	End CSS for blog-posts	
*/

/*
	Begin CSS for gallery-grid, masonry, justified
*/

.gallery_grid_content_wrapper img
{
	display: block;	
}

.gallery_grid_content_wrapper .tg_one_cols,
.gallery_grid_content_wrapper .tg_two_cols,
.gallery_grid_content_wrapper .tg_three_cols,
.gallery_grid_content_wrapper .tg_four_cols,
.gallery_grid_content_wrapper .tg_five_cols
{
	position: relative;
}

.gallery_grid_content_wrapper .tg_one_cols
{
	margin-top: 40px;
}

.gallery_grid_content_wrapper .tg_one_cols:first-child
{
	margin-top: 0;
}

.gallery_grid_content_wrapper .tg_two_cols
{
	width: calc(50% - 20px);
	margin-right: 40px;
	margin-bottom: 40px;
}

.gallery_grid_content_wrapper .tg_two_cols.last
{
	width: calc(50% - 20px);
	margin-right: 0;
	margin-bottom: 40px;
}

.gallery_grid_content_wrapper.do_masonry .tg_two_cols
{
	margin-right: 0;
}

.gallery_grid_content_wrapper .tg_three_cols
{
	width: calc(33.33% - 20px);
	margin-right: 30px;
	margin-bottom: 30px;
}

.gallery_grid_content_wrapper.do_masonry .tg_three_cols
{
	margin-right: 0;
}

.gallery_grid_content_wrapper .tg_three_cols.last
{
	width: calc(33.33% - 20px);
	margin-right: 0;
	margin-bottom: 30px;
}

.gallery_grid_content_wrapper .tg_four_cols
{
	width: calc(25% - 22.5px);
	margin-right: 30px;
	margin-bottom: 30px;
}

.gallery_grid_content_wrapper.do_masonry .tg_four_cols
{
	margin-right: 0;
}

.gallery_grid_content_wrapper .tg_four_cols.last
{
	width: calc(25% - 22.5px);
	margin-right: 0;
	margin-bottom: 30px;
}

.gallery_grid_content_wrapper .tg_five_cols
{
	width: calc(20% - 16px);
	margin-right: 20px;
	margin-bottom: 20px;
}

.gallery_grid_content_wrapper .tg_five_cols.last
{
	width: calc(20% - 16px);
	margin-right: 0;
	margin-bottom: 20px;
}

.gallery_grid_content_wrapper.do_masonry .tg_five_cols
{
	margin-right: 0;
}

.gallery_grid_content_wrapper.has_no_space .tg_one_cols,
.gallery_grid_content_wrapper.has_no_space .tg_two_cols,
.gallery_grid_content_wrapper.has_no_space .tg_three_cols,
.gallery_grid_content_wrapper.has_no_space .tg_four_cols,
.gallery_grid_content_wrapper.has_no_space .tg_five_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_one_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_two_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_three_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_four_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_five_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_one_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_two_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_three_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_four_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_five_cols
{
	margin: 0;
}

.gallery_grid_content_wrapper.has_no_space .tg_one_cols:hover,
.gallery_grid_content_wrapper.has_no_space .tg_two_cols:hover,
.gallery_grid_content_wrapper.has_no_space .tg_three_cols:hover,
.gallery_grid_content_wrapper.has_no_space .tg_four_cols:hover,
.gallery_grid_content_wrapper.has_no_space .tg_five_cols:hover,
.gallery_grid_content_wrapper.do_justified .entry:hover
{
	position: relative;
	z-index: 2;
}

@media not all and (min-resolution:.001dpcm) { @media {
    .gallery_grid_content_wrapper.has_no_space .tg_one_cols:hover,
	.gallery_grid_content_wrapper.has_no_space .tg_two_cols:hover,
	.gallery_grid_content_wrapper.has_no_space .tg_three_cols:hover,
	.gallery_grid_content_wrapper.has_no_space .tg_four_cols:hover,
	.gallery_grid_content_wrapper.has_no_space .tg_five_cols:hover,
	.gallery_grid_content_wrapper.do_justified .entry:hover
	{
		z-index: 1;
	}
}}

.gallery_grid_content_wrapper.has_no_space img,
.portfolio_grid_content_wrapper.has_no_space img,
.portfolio_classic_content_wrapper.has_no_space img
{
	max-width: none;
	width: 100%;
}

.gallery_grid_content_wrapper.has_no_space .tg_two_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_two_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_two_cols
{
	width: 50%;
	margin: 0;
}

.gallery_grid_content_wrapper.has_no_space .tg_three_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_three_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_three_cols
{
	width: 33.33%;
	margin: 0;
}

.gallery_grid_content_wrapper.has_no_space .tg_four_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_four_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_four_cols
{
	width: 25%;
	margin: 0;
}

.gallery_grid_content_wrapper.has_no_space .tg_five_cols,
.portfolio_grid_content_wrapper.has_no_space .tg_five_cols,
.portfolio_classic_content_wrapper.has_no_space .tg_five_cols
{
	width: 20%;
	margin: 0;
}

.gallery_grid_content_wrapper .gallery_grid_item
{
	overflow: visible;
	border-style: solid;
}

@media not all and (min-resolution:.001dpcm) { @media {
    .gallery_grid_content_wrapper .gallery_grid_item.gallery-grid-tilt 
	{
		transform: none !important;
	}
}}

.gallery_grid_content_wrapper.do_masonry .gallery_grid_item
{
	-webkit-transform: translateX(-40px);
    transform: translateX(-40px);
	-webkit-transition: opacity 0.3s ease-in-out;
	transition: opacity 0.3s ease-in-out;
	opacity: 0;
}

.gallery_grid_content_wrapper .gallery_grid_item.hide
{
	opacity: 0;
	display: none;
}

.gallery_grid_content_wrapper .gallery_grid_item.is-showing
{
	-webkit-transform: translateX(0px);
    transform: translateX(0px);
	opacity: 1;
}

.gallery_grid_content_wrapper .tg_gallery_grid_title
{
	box-sizing: border-box;
	padding: 20px 30px 20px 30px;
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	color: #fff;
	opacity: 0;
	z-index: 2;
	
	-webkit-transform: translateY(40px);
    transform: translateY(40px);
	-webkit-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.gallery_grid_content_wrapper .gallery_grid_item:hover .tg_gallery_grid_title
{
	-webkit-transform: translateY(0px);
    transform: translateY(0px);
	opacity: 1;
}

.gallery_grid_content_wrapper .tg_four_cols .tg_gallery_grid_title,
.gallery_grid_content_wrapper .tg_five_cols .tg_gallery_grid_title
{
	padding: 100px 15px 10px 15px;
}

.gallery_grid_content_wrapper .gallery_grid_item .bg_overlay
{
	background-color: rgba(0,0,0,0.2);
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	z-index: 1;
	
	-ms-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -o-transform: scale(0.8);
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
    
	-webkit-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.gallery_grid_content_wrapper .gallery_grid_item:hover .bg_overlay
{
	-ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
	opacity: 1;
}

.gallery_grid_content_wrapper .gallery_grid_item.lazy
{
	-webkit-animation: lazy_color_change 1s infinite alternate;
	-moz-animation: lazy_color_change 1s infinite alternate;
	-ms-animation: lazy_color_change 1s infinite alternate;
	-o-animation: lazy_color_change 1s infinite alternate;
	animation: lazy_color_change 1s infinite alternate;
}

.gallery_grid_content_wrapper .gallery_grid_item.tg_one_cols.lazy
{
	min-height: 550px;
}

.gallery_grid_content_wrapper .gallery_grid_item.tg_one_cols
{
	width: auto;
	text-align: center;
}

.gallery_grid_content_wrapper .gallery_grid_item.tg_one_cols .bg_overlay
{
	display: none;
}

.gallery_grid_content_wrapper .gallery_grid_item.tg_one_cols img
{
	margin: auto;
}

.gallery_grid_content_wrapper .gallery_grid_item.tg_two_cols.lazy
{
	min-height: 250px;
}

.gallery_grid_content_wrapper .gallery_grid_item.tg_three_cols.lazy
{
	min-height: 200px;
}

.gallery_grid_content_wrapper .gallery_grid_item.tg_four_cols.lazy
{
	min-height: 150px;
}

.gallery_grid_content_wrapper .gallery_grid_item.tg_five_cols.lazy
{
	min-height: 100px;
}

.gallery_grid_content_wrapper.do_justified
{
	-webkit-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.gallery_grid_content_wrapper.do_justified .entry-visible
{
	-webkit-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.has_no_space .portfolio_classic_content
{
	box-sizing: border-box;
}

/*
	End CSS for gallery-grid, masonry, justified
*/

/*
	Begin CSS for gallery-fullscreen
*/

.tg_gallery_fullscreen_content .tg_gallery_fullscreen_description {
	width: 50%;
	margin-top: 5px;
}

.swiper-container {
  width: 100%;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
}

.swiper-slide {
  cursor: pointer;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.tg_fullscreen_gallery {
  height: 100%;
  width: 100%;
}

.gallery-thumbs {
  display: none;
}

.tg_fullscreen_gallery_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
}

body.leftmenu .tg_fullscreen_gallery_wrapper
{
	width: calc(100% - 350px);
	left: 350px;
}

.tg_fullscreen_gallery_wrapper .tg_fullscreen_gallery .swiper-slide {
  cursor: default;
  background-size: contain;
}

.tg_fullscreen_gallery_wrapper .tg_fullscreen_gallery {
  height: 100%;
}

.swiper-button-next.hide,
.swiper-button-prev.hide {
	display: none;
}

.swiper-button-next.hover,
.swiper-button-prev.hover {
	opacity: 0;
}

.swiper-container:hover .swiper-button-next.hover,
.swiper-container:hover .swiper-button-prev.hover {
	opacity: 1;
}

.swiper-button-next, .swiper-button-prev {
	background-image: none !important;
	transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s!important;
    -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s!important;
}

.swiper-button-next {
	right: 50px;
}

.swiper-button-prev {
	left: 50px;
}

.swiper-button-next:hover {
	right: 40px;
}

.swiper-button-prev:hover {
	left: 40px;
}

.swiper-button-next i, .swiper-button-prev i {
	font-size: 60px;
	color: #fff;	
}

.swiper-container:hover .swiper-button-next.hover {
	right: 60px;
}

.swiper-container:hover .swiper-button-prev.hover {
	left: 60px;
}

.tg_gallery_fullscreen_content
{
	color: #fff;
	position: absolute;
	bottom: 40px;
	left: 50px;
}

.tg_gallery_fullscreen_content .tg_gallery_fullscreen_caption
{
	font-size: 11px;
	letter-spacing: 3px;
	text-transform: uppercase;
}

.tg_gallery_fullscreen_content .tg_gallery_fullscreen_title
{
	font-size: 20px;
}

/*
	End CSS for gallery-fullscreen
*/

/*
	Begin CSS for slider-vertical-parallax
*/

.tg_parallax_slide_container {
	width: 100%;
	height: 100vh;
	position: fixed;
	z-index: 2;
	background: #000;
}

body.leftmenu .tg_parallax_slide_container
{
	width: calc(100% - 350px);
	left: 350px;
}

.tg_parallax_slide_background {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  overflow: hidden;
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  height: 130vh;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  -webkit-transform: translateY(20vh);
  -ms-transform: translateY(20vh);
  transform: translateY(20vh);
  -webkit-transition: all 1.4s cubic-bezier(0.22, 0.44, 0, 1);
  transition: all 1.4s cubic-bezier(0.22, 0.44, 0, 1);
}

body.leftmenu .tg_parallax_slide_background {
	width: calc(100% - 350px);
	left: 350px;
}

body.leftmenu .icon-scroll {
	left: calc(50% + 175px);
}

.tg_parallax_slide_background:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
}

.tg_parallax_slide_background:first-child {
  -webkit-transform: translateY(-10vh);
  -ms-transform: translateY(-10vh);
  transform: translateY(-10vh);
}

.tg_parallax_slide_background:first-child .tg_parallax_slide_content_wrapper {
  -webkit-transform: translateY(10vh);
  -ms-transform: translateY(10vh);
  transform: translateY(10vh);
}

.tg_parallax_slide_content_wrapper {
  height: 100vh;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  -webkit-flex-flow: column nowrap;
  -ms-flex-flow: column nowrap;
  flex-flow: column nowrap;
  color: #fff;
  -webkit-transform: translateY(40vh);
  -ms-transform: translateY(40vh);
  transform: translateY(40vh);
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: all 1.9s cubic-bezier(0.22, 0.44, 0, 1);
  transition: all 1.9s cubic-bezier(0.22, 0.44, 0, 1);
}

.tg_parallax_slide_content_title {
  	padding: 0 10% 0 10% !important;
}

.tg_parallax_slide_content_title h2 {
	font-size: 120px;
	text-transform: uppercase;
}

.tg_parallax_slide_content_subtitle {
	font-size: 18px;
	margin-top: 20px;
}

.tg_parallax_slide_background .tg_parallax_slide_link
{
	position: absolute;
	width: 100%;
	height: 100%;
	display: block;
	z-index: 99999;
}

/*
	End CSS for slider-vertical-parallax
*/

/*
	Begin CSS for gallery-horizontal
*/

.tg_horizontal_gallery_cell {
  overflow: hidden;
}

#page_content_wrapper .tg_horizontal_gallery_wrapper ol.flickity-page-dots
{
	margin-left: 0;
}

.tg_horizontal_gallery_cell img {
  display: block;
  width: auto;
  max-width: none !important;
}

/* position dots up a bit */
.tg_horizontal_gallery_wrapper .flickity-page-dots {
  bottom: -40px;
}
/* dots are lines */
.tg_horizontal_gallery_wrapper .flickity-page-dots .dot {
  height: 4px;
  width: 40px;
  margin: 0;
  border-radius: 0;
}

.tg_horizontal_gallery_wrapper .flickity-prev-next-button svg {
	width: 40%;
	height: 40%;
}

.tg_horizontal_gallery_wrapper .flickity-prev-next-button svg {
	left: 30%;
    top: 30%;
}

.tg_horizontal_gallery_wrapper .flickity-prev-next-button.next,
.tg_horizontal_gallery_wrapper .flickity-prev-next-button.previous {
	transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s!important;
    -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s!important;
    opacity: 0.75;
}

.tg_horizontal_gallery_wrapper .flickity-prev-next-button.next {
	right: 40px;
}

.tg_horizontal_gallery_wrapper .flickity-prev-next-button.next:hover {
	right: 30px;
	opacity: 1;
}

.tg_horizontal_gallery_wrapper .flickity-prev-next-button.previous {
	left: 40px;
}

.tg_horizontal_gallery_wrapper .flickity-prev-next-button.previous:hover {
	left: 30px;
	opacity: 1;
}

.tg_horizontal_gallery_wrapper .flickity-prev-next-button:disabled {
	opacity: 0.3;
}

/* fade in image when loaded */
.tg_horizontal_gallery_cell_img {
  transition: opacity 0.4s;
  opacity: 0;
}

.tg_horizontal_gallery_cell_img.flickity-lazyloaded,
.tg_horizontal_gallery_cell_img.flickity-lazyerror {
  opacity: 1;
}

/*
	End CSS for gallery-horizontal
*/

/*
	Begin CSS for slider-horizontal
*/

.tg_slider_gallery_cell {
  overflow: hidden;
}

.tg_horizontal_slider_wrapper .flickity-prev-next-button svg {
	width: 40%;
	height: 40%;
}

.tg_horizontal_slider_wrapper .flickity-prev-next-button svg {
	left: 25%;
    top: 30%;
}

.tg_horizontal_slider_wrapper .flickity-prev-next-button.next,
.tg_horizontal_slider_wrapper .flickity-prev-next-button.previous {
	transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s!important;
    -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s!important;
    opacity: 0.75;
}

.tg_horizontal_slider_wrapper .flickity-prev-next-button.next {
	right: 0;
}

.tg_horizontal_slider_wrapper .flickity-prev-next-button.next:hover {
	right: -5px;
	opacity: 1;
}

.tg_horizontal_slider_wrapper .flickity-prev-next-button.previous {
	left: 0;
}

.tg_horizontal_slider_wrapper .flickity-prev-next-button.previous:hover {
	left: -5px;
	opacity: 1;
}

.tg_horizontal_slider_wrapper .tg_horizontal_slider_cell {
	width: 100%;
	height: 100%;
	float: left;
}

.tg_horizontal_slider_wrapper .tg_horizontal_slider_content {
	width: 30%;
	height: 100%;
	padding: 40px;
	box-sizing: border-box;
	float: left;
}

.tg_horizontal_slider_bg {
	width: 70%;
	height: 100%;
	padding: 40px;
	float: left;
}

.tg_horizontal_slider_wrapper .tg_horizontal_slider_content .tg_horizontal_slider_content_wrap {
	display: table;
	width: 100%;
	height: 100%;
}

.tg_horizontal_slider_bg_one_cols {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
}

.tg_horizontal_slider_bg_two_cols {
	width: calc(50% - 20px);
	float: left;
	height: 100%;
	box-sizing: border-box;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
}

.tg_horizontal_slider_bg_two_cols.last {
	float: right;
}

.tg_horizontal_slider_bg_two_rows {
	width: 100%;
	margin-bottom: 40px;
	height: calc(50% - 20px);
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
}

.tg_horizontal_slider_bg_two_rows.last {
	margin-bottom: 0;
}

.tg_horizontal_slider_wrapper .tg_horizontal_slider_content .tg_horizontal_slider_content_wrap .tg_horizontal_slider_content_cell {
	display: table-cell;
	vertical-align: middle;
}

.tg_horizontal_slide_content_title h2
{
	font-size: 40px;
}

.tg_horizontal_slide_content_desc
{
	padding: 20px 0 20px 0;
}

.tg_horizontal_slider_wrapper .flickity-prev-next-button:disabled {
	opacity: 0.3;
}

.tg_horizontal_slide_content_link
{
	font-size: 13px;
	letter-spacing: 2px;
	text-transform: uppercase;
	border-bottom: 1px solid #222;
	
	-webkit-transition: all 0.4s ease;
	transition: all 0.4s ease;
}

.tg_horizontal_slide_content_link:hover {
	padding-bottom: 2px;
	border-bottom-color: transparent !important;
}

/* position dots up a bit */
.tg_horizontal_slider_wrapper .flickity-page-dots {
  bottom: -50px;
  margin-left: 0 !important;
}
/* dots are lines */
.tg_horizontal_slider_wrapper .flickity-page-dots .dot {
  height: 4px;
  width: 40px;
  margin: 0;
  border-radius: 0;
}

/*
	End CSS for slider-horizontal
*/

/*
	Begin CSS for slider-animated-frame
*/

/* Fade effect */
.tg_animated_frame_slider_wrapper.slideshow {
	width: 100%;
	height: 100vh;
	background: #000;
	top: 0;
	left: 0;
	z-index: 2;
}
body.leftmenu .tg_animated_frame_slider_wrapper.slideshow
{
	width: calc(100% - 350px);
	left: 350px;
}
.tg_animated_frame_slider_wrapper.slideshow .slides {
	position: absolute;
	width: 100%;
	height: 100%;
	text-align: center;
}

.tg_animated_frame_slider_wrapper.slideshow .slides .slide {
	position: absolute;
	width: 100%;
	height: 100%;
	overflow: hidden;
	opacity: 0;
	pointer-events: none;
	display: flex;
	flex-direction: column;
	align-content: center;
	justify-content: center;
	align-items: center;
}

.tg_animated_frame_slider_wrapper.slideshow .slides .slide--current {
	opacity: 1;
	pointer-events: auto;
}

.tg_animated_frame_slider_wrapper.slideshow .slides .slide__img {
	position: absolute;
	top: -200px;
	left: -200px;
	width: calc(100% + 400px);
	height: calc(100% + 400px);
	background-size: cover;
	background-position: 50% 50%;
}

.tg_animated_frame_slider_wrapper.slideshow .slides .slide--current .slide__img {
	opacity: 0.8;
}

.tg_animated_frame_slider_wrapper.slideshow .slides .slide__content {
	position: relative;
	z-index: 2;
}

.tg_animated_frame_slider_wrapper.slideshow .slidenav {
	position: absolute;
	width: 300px;
	margin-left: -150px;
	left: 50%;
	bottom: 0;
	text-align: center;
	padding: 2em;
	z-index: 2;
	color: #fff;
}

.tg_animated_frame_slider_wrapper.slideshow .slidenav__item {
	border: 0;
	background: none;
	color: #fff;
	position: relative;
	font-size: 32px;
    opacity: 0.75;
    -webkit-transition: all 0.4s ease;
	transition: all 0.4s ease;
	cursor: pointer;
}

.tg_animated_frame_slider_wrapper.slideshow .slidenav__item:focus {
	outline: none;
}

.tg_animated_frame_slider_wrapper.slideshow .slidenav__item:hover {
	opacity: 1;
}

.tg_animated_frame_slider_wrapper.slideshow .shape {
	position: absolute;
	width: 100%;
	height: 100%;
	fill: #fff;
	top: 0;
	pointer-events: none;
}

.tg_animated_frame_slider_wrapper.slideshow .slides .slide__title {
	position: relative;
	font-size: 140px;
	line-height: 1.2;
	text-transform: uppercase;
	margin: 0;
	cursor: default;
	color: #fff;
}

.tg_animated_frame_slider_wrapper.slideshow .slides .slide__desc {
	position: relative;
	font-size: 22px;
	margin: 0 0 2em 0;
	cursor: default;
	color: #fff;
	padding: 0 !important;
	text-align: center;
}

.tg_animated_frame_slider_wrapper.slideshow .slides .slide__link {
	position: relative;
	font-size: 1em;
	font-weight: bold;
	padding: 0.5em 2em;
	display: inline-block;
	color: #000;
	background: #fff;
	transition: color 0.3s, background 0.3s;
	border: 2px solid #ffffff;
}

/*
	End CSS for slider-animated-frame
*/

/*
	Begin CSS for slider-3d-room
*/

body.elementor-fullscreen.room {
	background: #cecece;
	height: 100vh;
	overflow: hidden;
}

.tg_room_slider_wrapper {
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	z-index: 2;
	background: #fff;
}

body.leftmenu .tg_room_slider_wrapper
{
	width: calc(100% - 350px);
	left: 350px;
}

.tg_room_slider_wrapper .hidden {
	position: absolute;
	overflow: hidden;
	width: 0;
	height: 0;
	pointer-events: none;
}

/* Icons */
.tg_room_slider_wrapper .icon {
	display: block;
	width: 1.5em;
	height: 1.5em;
	margin: 0 auto;
	fill: currentColor;
}

.tg_room_slider_wrapper .btn {
	position: relative;
	margin: 0;
	padding: 0;
	border: 0;
	background: none;
}

.tg_room_slider_wrapper .btn--nav {
	font-size: 2em;
	pointer-events: auto;
	z-index: 3;
	cursor: pointer;
	opacity: 0.75;
	
	transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s!important;
    -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s!important;
}

.tg_room_slider_wrapper .btn--nav:focus {
	outline: none;
}

.tg_room_slider_wrapper .btn--nav:hover {
	opacity: 1;
}

.tg_room_slider_wrapper .btn--nav-right {
	float: right;
}

.tg_room_slider_wrapper .nav-icon--right {
	transform: scale3d(-1,-1,1);
}

.tg_room_slider_wrapper .nav__triangle,
.tg_room_slider_wrapper .nav__line {
	transition: transform 0.3s;
	fill: currentColor;
}

.tg_room_slider_wrapper .btn--nav:hover .nav__triangle {
	transform: translate3d(-54px,0,0);
}

.tg_room_slider_wrapper .btn--nav:hover .nav__line {
	transform: translate3d(17px,0,0);
}

/* two seats on each side for free space */

.tg_room_slider_wrapper .container {
	position: relative;
	overflow: hidden;
	width: 100%;
	height: 100vh;
	perspective: 2000px;
}

.tg_room_slider_wrapper .scroller {
	height: 100%;
	transform-style: preserve-3d;
}

.tg_room_slider_wrapper .room {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 100vw;
	height: 100vh;
	margin: -50vh 0 0 -50vw;
	pointer-events: none;
	opacity: 0;
	transform-style: preserve-3d;
}

.tg_room_slider_wrapper .room--current {
	pointer-events: auto;
	opacity: 1;
}

.tg_room_slider_wrapper .room__side {
	position: absolute;
	display: flex;
	justify-content: center;
	align-items: center;
	transform-style: preserve-3d;
}

.tg_room_slider_wrapper .room__side--left,
.tg_room_slider_wrapper .room__side--right {
	width: 4000px; /* depth */
	height: 100vh;
	background: #dbdbdb;
}

.tg_room_slider_wrapper .room__side--back {
	width: 100vw;
	height: 100vh;
	background: #e9e9e9;
	box-shadow: 0 0 0 2px #e9e9e9;
	transform: translate3d(0, 0, -4000px) rotate3d(1,0,0,0.1deg) rotate3d(1,0,0,0deg);
	/* Rotation due to rendering bug in Chrome when loader slides up (images seem cut off) */
}

.tg_room_slider_wrapper .room__side--right {
	right: 0;
	justify-content: flex-end;
	transform: rotate3d(0, 1, 0, -90.03deg);
	transform-origin: 100% 50%;
}

.tg_room_slider_wrapper .room__side--left {
	justify-content: flex-start;
	transform: rotate3d(0, 1, 0, 90deg);
	transform-origin: 0 50%;
}

.tg_room_slider_wrapper .room__side--bottom {
	width: 100vw; /* depth */
	height: 4000px;
	background: #d0d0d0;
	transform: rotate3d(1, 0, 0, 90deg) translate3d(0, -4000px, 0);
	transform-origin: 50% 0%;
}

.tg_room_slider_wrapper .room__side--bottom {
	top: 100%;
}

/* Inner elements */
#page_content_wrapper .inner .sidebar_content .tg_room_slider_wrapper .room__img {
	flex: none;
	max-width: 40%;
    max-height: 60%;
	margin: 0 5%;
	transform: translate3d(0,0,10px);
	backface-visibility: hidden;
}

/* Content */
.tg_room_slider_wrapper .bg_overlay {
	background: rgba(0,0,0,0.2);
	position: fixed;
	width: 100%;
	height: 100%;
	z-index: 2;
}

.tg_room_slider_wrapper *,
.tg_room_slider_wrapper *::after,
.tg_room_slider_wrapper *::before {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.tg_room_slider_wrapper .content {
	position: absolute;
	top: 0;
	left: 0;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100vh;
	padding: 2vw;
}

/* Slides */
.tg_room_slider_wrapper .slides {
	position: relative;
	flex: 1;
}

.tg_room_slider_wrapper .slide {
	position: absolute;
	left: 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	width: 40%;
	height: 100%;
	margin: 25vh 0 0 30vw;
	pointer-events: none;
	opacity: 0;
	z-index: 3;
	text-align: center;
}

body.leftmenu .tg_room_slider_wrapper .slide {
	width: 70%;
	margin: 7vh 0 0 10vw;
}

.tg_room_slider_wrapper .slide--current {
	pointer-events: auto;
	opacity: 1;
}

.tg_room_slider_wrapper .slide__name {
	font-size: 120px;
	line-height: 1;
	color: #fff;
}

.tg_room_slider_wrapper .slide__title,
.tg_room_slider_wrapper .slide__date {
	width: 100%;
    box-sizing: border-box;
}

.tg_room_slider_wrapper .slide__date {
	margin-top: 1em;
}

.tg_room_slider_wrapper .slide__title {
	font-weight: normal;
	margin: 1em 0 0 0;
	color: #fff;
}

@media screen and (max-width: 50em) {
	.tg_room_slider_wrapper .slide {
		width: 100%;
		margin: 7vh 0 0 0;
	}
	.tg_room_slider_wrapper .slide__title {
		font-size: 1.5em;
	}
	.tg_room_slider_wrapper .slide__date {
		font-size: 0.65em;
	}
	.tg_room_slider_wrapper .nav {
		position: absolute;
		top: 7em;
		left: 0;
		width: 100%;
		padding: 1em;
	}
	.tg_room_slider_wrapper .nav__triangle {
		transform: translate3d(-54px,0,0);
	}
	.tg_room_slider_wrapper .nav__line {
		transform: translate3d(17px,0,0);
	}
	.tg_room_slider_wrapper .btn--nav:hover {
		color: currentColor;
	}
	.tg_room_slider_wrapper .info {
		font-size: 0.95em;
		width: 100vw;
	}
}


/*
	End CSS for slider-3d-room
*/


/*
	Begin CSS for dotlife-slider-multi-layouts
*/

svg.hidden {
	position: absolute;
	overflow: hidden;
	width: 0;
	height: 0;
	pointer-events: none;
}

.tg_multi_layouts_slider_wrapper .icon {
	display: block;
	width: 1.5em;
	height: 1.5em;
	margin: 0 auto;
	fill: currentColor;
}

.tg_multi_layouts_slider_wrapper .btn {
	display: inline-block;
	margin: 0;
	padding: 0;
	cursor: pointer;
	pointer-events: auto;
	color: #222;
	border: none;
	background: none;
}

.tg_multi_layouts_slider_wrapper .btn:focus {
	outline: none;
}

.tg_multi_layouts_slider_wrapper .btn--arrow {
	font-size: 1.5em;
	display: block;
	
	-webkit-transition: all 0.2s ease-in-out;
	transition: all 0.2s ease-in-out;
}

.tg_multi_layouts_slider_wrapper .btn--arrow:nth-child(2) {
	margin: 0 0 0 0.5em;
}

.tg_multi_layouts_slider_wrapper .btn--arrow:nth-child(1):hover {
	-webkit-transform: translateX(-20px);
    transform: translateX(-20px);
}

.tg_multi_layouts_slider_wrapper .btn--arrow:nth-child(2):hover {
	-webkit-transform: translateX(20px);
    transform: translateX(20px);
}

.tg_multi_layouts_slider_wrapper .btn--arrow .icon {
	height: 0.5em;
}

.tg_multi_layouts_slider_wrapper.slideshow {
	position: relative;
	width: 100%;
	max-width: 1600px;
}

.tg_multi_layouts_slider_wrapper.slideshow:focus {
	outline: none;
}

.js .tg_multi_layouts_slider_wrapper.slideshow::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 70px;
	height: 70px;
	margin: -35px 0 0 -35px;
	pointer-events: none;
	border: 10px solid rgba(255, 255, 255, 0.1);
	border-top-color: #212121;
	border-radius: 50%;
	-webkit-transition: opacity 0.3s;
	transition: opacity 0.3s;
	-webkit-animation: rotateLoader 0.8s linear infinite forwards;
	animation: rotateLoader 0.8s linear infinite forwards;
}

@-webkit-keyframes rotateLoader {
	to {
		-webkit-transform: rotate3d(360deg);
		transform: rotate3d(360deg);
	}
}

@keyframes rotateLoader {
	to {
		-webkit-transform: rotate3d(0, 0, 1, 360deg);
		transform: rotate3d(0, 0, 1, 360deg);
	}
}

.js .tg_multi_layouts_slider_wrapper.slideshow--loaded::after {
	opacity: 0;
}

.js .tg_multi_layouts_slider_wrapper.slideshow {
	height: 100vh; /* define height here */
	max-height: 1000px;
	pointer-events: none;
}

.tg_multi_layouts_slider_wrapper .slide {
	position: relative;
	width: 100%;
	margin: 0 0 1em 0;
	padding: 2em 2em;
}

.js .tg_multi_layouts_slider_wrapper .slide {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	margin: 0;
	pointer-events: none;
	opacity: 0;
}

.js .tg_multi_layouts_slider_wrapper .slide--current {
	pointer-events: auto;
	opacity: 1;
}

.tg_multi_layouts_slider_wrapper .slide-imgwrap {
	position: relative;
	width: calc(100% - 6em);
	height: 90%;
	margin: 0 auto;
	opacity: 0.5;
	
	-webkit-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tg_multi_layouts_slider_wrapper .slide-imgwrap:hover {
	opacity: 1 !important;
}

.no-js .tg_multi_layouts_slider_wrapper .slide-imgwrap {
	height: 400px;
}

.tg_multi_layouts_slider_wrapper .slide__img-inner {
	position: absolute;
	width: 100%;
	height: 100%;
	-webkit-transform: rotateZ(0deg);
	transform: rotateZ(0deg);
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	will-change: tranform, opacity;
	background-position: 50% 50%;
	background-size: cover;
}

.tg_multi_layouts_slider_wrapper .slide__title {
	position: absolute;
	bottom: 0;
	left: 0;
	width: calc(100% - 11em);
	max-width: 800px;
	padding: 2em;
}

.js .tg_multi_layouts_slider_wrapper .slide__title {
	pointer-events: none;
	opacity: 0;
}

.js .tg_multi_layouts_slider_wrapper .slide--current .slide__title {
	pointer-events: auto;
	opacity: 1;
}

.tg_multi_layouts_slider_wrapper .slide__title-main {
	font-size: 6em;
	line-height: 1;
	display: inline-block;
	margin: 0;
}

.tg_multi_layouts_slider_wrapper .slide__title-sub {
	font-size: 1.15em;
	line-height: 1.5;
	margin: 0;
}

.tg_multi_layouts_slider_wrapper .slide__title-sub a {
	border-bottom: 1px solid;
}


.tg_multi_layouts_slider_wrapper .slideshow__nav {
	position: absolute;
	pointer-events: none;
}

.no-js .tg_multi_layouts_slider_wrapper .slideshow__nav {
	display: none;
}

.tg_multi_layouts_slider_wrapper .slideshow__nav--arrows {
	font-size: 4em;
	right: 0.3em;
	bottom: 2em;
}

/* Media query for smaller screens (general elements) */

@media screen and (max-width: 45em) {
	.tg_multi_layouts_slider_wrapper .slideshow {
		padding-top: 7em;
	}
	.js .tg_multi_layouts_slider_wrapper .slideshow {
		height: calc(100vh - 60px);
	}
	.tg_multi_layouts_slider_wrapper .slide {
		padding: 6em 0;
	}
	.no-js .tg_multi_layouts_slider_wrapper .slide {
		padding: 4em 0 0;
	}
	.tg_multi_layouts_slider_wrapper .slide-imgwrap {
		width: calc(100% - 1em);
	}
	.tg_multi_layouts_slider_wrapper .slide__title {
		width: calc(100% - 2em);
		margin: 0 0 4em 0;
		padding: 1.5em;
	}
	.tg_multi_layouts_slider_wrapper .slide__title-main {
		font-size: 2em;
	}
	.tg_multi_layouts_slider_wrapper .slide__title-sub {
		font-size: 0.75em;
	}
}

/* Individual layouts */

/* Layout 1: 3 grid images */

.tg_multi_layouts_slider_wrapper .slide--layout-1 .slide__img {
	position: absolute;
	width: calc(50% - 1em);
}

.tg_multi_layouts_slider_wrapper .slide--layout-1 .slide__img:first-child {
	left: 0.5em;
	height: 100%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-1 .slide__img:nth-child(n+2) {
	left: calc(50% + 0.5em);
	height: calc(50% - 0.5em);
}

.tg_multi_layouts_slider_wrapper .slide--layout-1 .slide__img:nth-child(3) {
	top: calc(50% + 0.5em);
}


/* Layout 2: polaroid stack for 5 images */

.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img {
	position: absolute;
	top: 50%;
	left: 50%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:not(:last-child) {
	width: 300px;
	height: 300px;
}

.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:first-child {
	-webkit-transform: translate3d(-50%,-50%,0) translate3d(-60%,-40%,0) rotate(-16deg);
	transform: translate3d(-50%,-50%,0) translate3d(-60%,-40%,0) rotate(-16deg);
}

.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:nth-child(2) {
	-webkit-transform: translate3d(-50%,-50%,0) translate3d(60%,-40%,0) rotate(10deg);
	transform: translate3d(-50%,-50%,0) translate3d(60%,-40%,0) rotate(10deg);
}

.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:nth-child(3) {
	-webkit-transform: translate3d(-50%,-50%,0) translate3d(60%,40%,0) rotate(-15deg);
	transform: translate3d(-50%,-50%,0) translate3d(60%,40%,0) rotate(-15deg);
}

.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:nth-child(4) {
	-webkit-transform: translate3d(-50%,-50%,0) translate3d(-60%,40%,0) rotate(10deg);
	transform: translate3d(-50%,-50%,0) translate3d(-60%,40%,0) rotate(10deg);
}

.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:last-child {
	-webkit-transform: translate3d(-50%,-50%,0);
	transform: translate3d(-50%,-50%,0);
	width: 450px;
	height: 400px;
}

.tg_multi_layouts_slider_wrapper .slide__img-caption {
	position: absolute;
	width: 100%;
	top: 100%;
	text-align: center;
	margin: 0;
	padding: 1em;
	font-size: 1.25em;
	font-weight: normal;
}

@media screen and (max-width: 45em) {
	.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:not(:last-child) {
		width: 100px;
		height: 100px;
	}
	.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:last-child .slide__img-inner {
		border-bottom-width: 40px;
	}
	.tg_multi_layouts_slider_wrapper .slide--layout-2 .slide__img:last-child {
		width: 160px;
		height: 160px;
	}
	.tg_multi_layouts_slider_wrapper .slide__img-caption {
		font-size: 0.75em;
	}
}

/* Layout 3: card stack with 7 images */

.tg_multi_layouts_slider_wrapper .slide--layout-3 .slide__img {
	position: absolute;
	width: 450px;
	height: 450px;
	top: 40%;
	left: 50%;
	-webkit-transform: translate3d(-50%,-50%,0) translate3d(-50%,0,0) rotate(-10deg);
	transform: translate3d(-50%,-50%,0) translate3d(-50%,0,0) rotate(-10deg);
}

@media screen and (max-width: 45em) {
	.tg_multi_layouts_slider_wrapper .slide--layout-3 .slide__img {
		width: 200px;
		height: 200px;
	}
}


.tg_multi_layouts_slider_wrapper .slide--layout-3 .slide__img-inner {
	-webkit-transform-origin: 50% 200%;
	transform-origin: 50% 200%;
}

/* Layout 4: asymmetrical with 4 images */

.tg_multi_layouts_slider_wrapper .slide--layout-4 .slide__img {
	position: absolute;
}

.tg_multi_layouts_slider_wrapper .slide--layout-4 .slide__img:nth-child(2) {
	top: 15%;
	left: 15%;
	width: 30%;
	height: 50%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-4 .slide__img:first-child {
	top: 40%;
	left: 5%;
	width: 80%;
	height: 60%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-4 .slide__img:nth-child(3) {
	top: 0;
	left: 40%;
	width: 40%;
	height: 60%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-4 .slide__img:nth-child(4) {
	top: 50%;
	left: 75%;
	width: 22.5%;
	height: 42.5%;
}


/* Layout 5: grid with 20 images */

.tg_multi_layouts_slider_wrapper .slide--layout-5 .slide-imgwrap {
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-align-content: center;
	-ms-flex-line-pack: center;
	align-content: center;
	padding: 8% 0;
	width: 80%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-5 .slide__img {
	position: relative;
	width: calc((100% / 6) - 10px);
	padding-bottom: calc((100% / 6) - 10px);
	min-height: 150px;
	margin: 5px;
}

@media screen and (max-width: 45em) {
	.tg_multi_layouts_slider_wrapper .slide--layout-5 .slide__img {
		min-height: 50px;
	}
}


/* Layout 6: 3 round images */

.tg_multi_layouts_slider_wrapper .slide--layout-6 .slide__img {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 45%;
	padding-bottom: 45%;
	-webkit-transform: translate3d(-50%,-50%,0);
	transform: translate3d(-50%,-50%,0);
}

.tg_multi_layouts_slider_wrapper .slide--layout-6 .slide__img-inner {
	border-radius: 50%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-6 .slide__img:first-child {
	-webkit-transform: translate3d(0%,-40%,0) scale(0.6);
	transform: translate3d(0%,-40%,0) scale(0.6);
}

.tg_multi_layouts_slider_wrapper .slide--layout-6 .slide__img:nth-child(3) {
	-webkit-transform: translate3d(-100%,-60%,0) scale(0.75);
	transform: translate3d(-100%,-60%,0) scale(0.75);
}

/* Layout 7: 3 rhomboid images */

.tg_multi_layouts_slider_wrapper .slide--layout-7 .slide__img {
	position: absolute;
	top: 10%;
	width: 40%;
	height: 80%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-7 .slide__img:nth-child(2) {
	left: 30%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-7 .slide__img:nth-child(3) {
	left: 60%;
}

.tg_multi_layouts_slider_wrapper .slide--layout-7 .slide__img-inner {
	-webkit-clip-path: polygon(0% 100%, 30% 0%, 100% 0%, 70% 100%);
	clip-path: polygon(0% 100%, 30% 0%, 100% 0%, 70% 100%);
	-webkit-clip-path: url('#polygon-clip-rhomboid');
	clip-path: url('../index.html#polygon-clip-rhomboid'); /* Firefox needs this path */
}

/*
	End CSS for dotlife-slider-multi-layouts
*/

/*
	Begin CSS for dotlife-slider-velo
*/

.tg_velo_slide_container.velo-slides {
  z-index: 8;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #111;
}
.tg_velo_slide_container.velo-slides[data-velo-slider="on"] {
  overflow: hidden;
}

.tg_velo_slide_container .velo-slide {
  height: 100vh;
  z-index: 4;
}
@media (min-width: 54em) and (max-width: 65em) {
  .tg_velo_slide_container .velo-slide {
    font-size: 80%;
  }
}
@media (min-width: 54em) and (min-height: 0) and (max-height: 45em) {
  .tg_velo_slide_container .velo-slide {
    font-size: 70%;
  }
}
.tg_velo_slide_container[data-velo-slider="on"] .velo-slide {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.tg_velo_slide_container .velo-slide.is-active {
  z-index: 8;
}
.tg_velo_slide_container .velo-slide__bg {
  z-index: 7;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  color: #111;
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  background-color: #111;
  overflow: hidden;
}
.tg_velo_slide_container .velo-slide__bg:after {
  z-index: 0;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0.4;
  background: #111;
}
.tg_velo_slide_container .velo-slide__figure {
  z-index: 0;
  position: relative;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-transition: -webkit-transform 0.5s ease;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}
.tg_velo_slide_container .is-hovering .velo-slide__figure {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  -webkit-transition: -webkit-transform 0.5s ease;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}
.tg_velo_slide_container .velo-slide__vid-wrap {
  z-index: 5;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-transition: -webkit-transform 0.5s ease;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}
.tg_velo_slide_container .velo-slide__vid-wrap:after {
  z-index: 0;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0.4;
  background: #111;
}
.tg_velo_slide_container .is-hovering .velo-slide__vid-wrap {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  -webkit-transition: -webkit-transform 0.5s ease;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}
.tg_velo_slide_container .velo-slide__vid {
  z-index: -1;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: auto;
  min-width: 100%;
  max-width: none;
  height: auto;
  min-height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.tg_velo_slide_container .velo-slide__header {
  z-index: 9;
  position: relative;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  overflow-y: hidden;
  padding: 4%;
}
.tg_velo_slide_container .velo-slide__pretitle {
  color: #fff;
  max-width: 22em;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .velo-slide__pretitle {
    margin-left: 7%;
  }
}
.tg_velo_slide_container .velo-slide__title {
  margin-bottom: 0.1em;
  line-height: 1.1;
  color: #fff;
  letter-spacing: -0.025em;
  font-weight: 700;
  font-size: 2.25em;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .velo-slide__title {
    font-size: 5em;
  }
}
@media (min-width: 65em) {
  .tg_velo_slide_container .velo-slide__title {
    font-size: 5em;
  }
}
@media (min-width: 91em) {
  .tg_velo_slide_container .velo-slide__title {
    font-size: 5em;
  }
}
.tg_velo_slide_container .velo-slide__text {
  color: #fff;
  max-width: 30em;
  display: block;
  margin: 0;
}
.tg_velo_slide_container .velo-slide__btn {
  opacity: 0;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .velo-slide__btn {
    margin-left: 1vw;
  }
}
.tg_velo_slide_container .is-active .velo-slide__btn {
  opacity: 1;
}
.tg_velo_slide_container .velo-slide__btn > a > span {
  opacity: 0;
  overflow-y: hidden;
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
}
.tg_velo_slide_container .is-active .velo-slide__btn > a > span {
  opacity: 1;
  z-index: 9999;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  -webkit-transition: opacity 0.8s ease, -webkit-transform 0.4s ease;
  transition: opacity 0.8s ease, -webkit-transform 0.4s ease;
  transition: transform 0.4s ease, opacity 0.8s ease;
  transition: transform 0.4s ease, opacity 0.8s ease, -webkit-transform 0.4s ease;
}

.tg_velo_slide_container .oh {
  display: block;
  overflow-y: hidden;
  padding: 0.02em 0;
}

.tg_velo_slide_container .oh span {
  display: inline-block;
  -webkit-transform: translate3d(0, 140%, 0);
          transform: translate3d(0, 140%, 0);
  opacity: 0;
  -webkit-transition: opacity 0.8s ease, -webkit-transform 0.4s ease;
  transition: opacity 0.8s ease, -webkit-transform 0.4s ease;
  transition: transform 0.4s ease, opacity 0.8s ease;
  transition: transform 0.4s ease, opacity 0.8s ease, -webkit-transform 0.4s ease;
}

.tg_velo_slide_container .is-active .oh span {
  -webkit-transform: translate3d(0, 0%, 0);
          transform: translate3d(0, 0%, 0);
  opacity: 1;
  -webkit-transition: opacity 0.1s ease, -webkit-transform 0.6s cubic-bezier(0.77, 0, 0.175, 1);
  transition: opacity 0.1s ease, -webkit-transform 0.6s cubic-bezier(0.77, 0, 0.175, 1);
  transition: transform 0.6s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.1s ease;
  transition: transform 0.6s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.1s ease, -webkit-transform 0.6s cubic-bezier(0.77, 0, 0.175, 1);
}

.tg_velo_slide_container .is-active .oh:nth-of-type(2n) span {
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}

.tg_velo_slide_container .border, .tg_velo_slide_container .border:before, .tg_velo_slide_container .border:after, .tg_velo_slide_container .border span {
  z-index: 91;
  position: fixed;
  background-color: currentColor;
  -webkit-transition: 0.35s ease-in-out;
  transition: 0.35s ease-in-out;
}
.tg_velo_slide_container .is-hovering .border,
.tg_velo_slide_container .is-hovering .border:before,
.tg_velo_slide_container .is-hovering .border:after,
.tg_velo_slide_container .is-hovering .border span {
  -webkit-transition: 0.5s ease-in-out;
  transition: 0.5s ease-in-out;
}
.tg_velo_slide_container .velocity-animating .border, .tg_velo_slide_container .velocity-animating .border:before, .tg_velo_slide_container .velocity-animating .border:after, .tg_velo_slide_container .velocity-animating .border span {
  -webkit-transition: 0.5s cubic-bezier(0.77, 0, 0.175, 1);
  transition: 0.5s cubic-bezier(0.77, 0, 0.175, 1);
}

.tg_velo_slide_container .border {
  top: 0;
  left: 0;
  width: 100%;
  height: 0vw;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .border {
    height: 10vw;
  }
}
@media (min-width: 91em) {
  .tg_velo_slide_container .border {
    height: 7vw;
  }
}
@media (min-width: 115em) {
  .tg_velo_slide_container .border {
    height: 7vw;
  }
}
.tg_velo_slide_container .is-hovering .border {
  height: 0vw;
}
.tg_velo_slide_container .velocity-animating .border {
  height: 8vw;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .velocity-animating .border {
    height: 12vw;
  }
}

.tg_velo_slide_container .border:before {
  content: '';
  bottom: 0;
  left: 0;
  width: 0vw;
  height: 100%;
  width: 0vw;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .border:before {
    width: 10vw;
  }
}
@media (min-width: 91em) {
  .tg_velo_slide_container .border:before {
    width: 7vw;
  }
}
@media (min-width: 115em) {
  .tg_velo_slide_container .border:before {
    width: 7vw;
  }
}
.tg_velo_slide_container .is-hovering .border:before {
  width: 0vw;
}
.tg_velo_slide_container .velocity-animating .border:before {
  width: 8vw;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .velocity-animating .border:before {
    width: 12vw;
  }
}

.tg_velo_slide_container .border:after {
  content: '';
  bottom: 0;
  right: 0;
  height: 100%;
  width: 0vw;
  width: 0vw;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .border:after {
    width: 10vw;
  }
}
@media (min-width: 91em) {
  .tg_velo_slide_container .border:after {
    width: 7vw;
  }
}
@media (min-width: 115em) {
  .tg_velo_slide_container .border:after {
    width: 7vw;
  }
}
.tg_velo_slide_container .is-hovering .border:after {
  width: 0vw;
}
.tg_velo_slide_container .velocity-animating .border:after {
  width: 8vw;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .velocity-animating .border:after {
    width: 12vw;
  }
}

.tg_velo_slide_container .border span {
  bottom: 0;
  left: 0;
  height: 0vw;
  width: 100%;
  height: 0vw;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .border span {
    height: 10vw;
  }
}
@media (min-width: 91em) {
  .tg_velo_slide_container .border span {
    height: 7vw;
  }
}
@media (min-width: 115em) {
  .tg_velo_slide_container .border span {
    height: 7vw;
  }
}
.tg_velo_slide_container .is-hovering .border span {
  height: 0vw;
}
.tg_velo_slide_container .velocity-animating .border span {
  height: 8vw;
}
@media (min-width: 54em) {
  .tg_velo_slide_container .velocity-animating .border span {
    height: 12vw;
  }
}

.velo-slides-nav {
  /* lateral navigation */
  position: fixed;
  z-index: 91;
  right: 3%;
  bottom: 1em;
}
@media (min-width: 54em) {
  .velo-slides-nav {
    top: 50%;
    bottom: auto;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
  }
}
.velo-slides-nav__list {
  list-style: none;
}
.velo-slides-nav li:first-child {
  margin-bottom: 0.25em;
}
.velo-slides-nav a {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  -webkit-transition: color 0.5s ease, text-shadow 0.5s ease;
  transition: color 0.5s ease, text-shadow 0.5s ease;
}
.velo-slides-nav a:hover {
  color: white;
  text-shadow: 0px 0px 6px rgba(255, 255, 255, 0.6);
  -webkit-transition: color 0.5s ease, text-shadow 0.5s ease;
  transition: color 0.5s ease, text-shadow 0.5s ease;
}
.velo-slides-nav a.inactive {
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.2s 0s, visibility 0s 0.2s;
  transition: opacity 0.2s 0s, visibility 0s 0.2s;
}
.velo-slides-nav i {
  font-size: 2.2em;
}
@media (min-width: 54em) {
  .velo-slides-nav i {
    font-size: 1.5em;
  }
}

@font-face {
  font-family: 'ssicons';
  src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype");
  font-weight: normal;
  font-style: normal;
}
[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'ssicons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-down-chev:before {
  content: "\e900";
}

.icon-right-chev:before {
  content: "\e901";
}

.icon-up-chev:before {
  content: "\e902";
}

.icon-left-chev:before {
  content: "\e903";
}

.icon-up-arrow:before {
  content: "\e904";
}

.icon-down-arrow:before {
  content: "\e905";
}

.icon-left-arrow:before {
  content: "\e906";
}

.icon-right-arrow:before {
  content: "\e907";
}

.tg_velo_slide_container .btn-draw {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: auto;
  margin: 0;
  padding: 0;
  background: transparent;
  border: 0;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  -webkit-appearance: none;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

.tg_velo_slide_container .btn-draw {
  color: #00ffc8;
  margin-left: 2.3em;
}
.tg_velo_slide_container .btn-draw:after {
  content: '';
  top: 0;
  right: 0;
  height: 1px;
  width: 100%;
  background-color: #00ffc8;
}
.tg_velo_slide_container .btn-draw:before {
  content: '';
  position: absolute;
  top: 50%;
  left: -2.4em;
  height: 1px;
  width: 3em;
  background-color: #00ffc8;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}
.tg_velo_slide_container .btn-draw .btn-draw__text {
  position: relative;
  display: block;
  padding: 0.7555em 2.29em;
  line-height: 1.5;
  -webkit-transition: -webkit-transform 0.5s ease;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}
.tg_velo_slide_container .btn-draw .btn-draw__text:before, .tg_velo_slide_container .btn-draw .btn-draw__text:after {
  content: '';
  position: absolute;
  height: 1px;
  width: 0;
  background-color: #00ffc8;
}
.tg_velo_slide_container .btn-draw .btn-draw__text:before {
  top: 0;
  left: 0;
  -webkit-transition: width 0.15s 0.45s cubic-bezier(0.77, 0, 0.175, 1);
  transition: width 0.15s 0.45s cubic-bezier(0.77, 0, 0.175, 1);
}
.tg_velo_slide_container .btn-draw .btn-draw__text:after {
  bottom: 0;
  right: 0;
  -webkit-transition: width 0.15s 0.15s cubic-bezier(0.77, 0, 0.175, 1);
  transition: width 0.15s 0.15s cubic-bezier(0.77, 0, 0.175, 1);
}
.tg_velo_slide_container .btn-draw .btn-draw__text > span:before, .tg_velo_slide_container .btn-draw .btn-draw__text > span:after {
  content: '';
  position: absolute;
  height: 0;
  width: 1px;
  background-color: #00ffc8;
  -webkit-transition: all 0.2s cubic-bezier(0.2, 0.3, 0.25, 0.9);
  transition: all 0.2s cubic-bezier(0.2, 0.3, 0.25, 0.9);
}
.tg_velo_slide_container .btn-draw .btn-draw__text > span:before {
  left: 0;
  bottom: 0;
  -webkit-transition: height 0.15s 0 cubic-bezier(0.77, 0, 0.175, 1);
  transition: height 0.15s 0 cubic-bezier(0.77, 0, 0.175, 1);
}
.tg_velo_slide_container .btn-draw .btn-draw__text > span:after {
  right: 0;
  top: 0;
  -webkit-transition: height 0.15s 0.3s cubic-bezier(0.77, 0, 0.175, 1);
  transition: height 0.15s 0.3s cubic-bezier(0.77, 0, 0.175, 1);
}
@media (hover) {
  .tg_velo_slide_container .btn-draw:hover, .tg_velo_slide_container a:hover .btn-draw {
    cursor: pointer;
    color: #00ffc8;
  }
  .tg_velo_slide_container .btn-draw:hover:before, a:hover .btn-draw:before {
    width: 0;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
  }
  .tg_velo_slide_container .btn-draw:hover .btn-draw__text, a:hover .btn-draw .btn-draw__text {
    -webkit-transform: translateX(-2.2em);
            transform: translateX(-2.2em);
    -webkit-transition: width 1s ease, -webkit-transform 0.5s ease;
    transition: width 1s ease, -webkit-transform 0.5s ease;
    transition: transform 0.5s ease, width 1s ease;
    transition: transform 0.5s ease, width 1s ease, -webkit-transform 0.5s ease;
  }
  .tg_velo_slide_container .btn-draw:hover .btn-draw__text:before, .tg_velo_slide_container a:hover .btn-draw .btn-draw__text:before {
    width: 100%;
    max-width: 100%;
    -webkit-transition: width 0.15s cubic-bezier(0.77, 0, 0.175, 1);
    transition: width 0.15s cubic-bezier(0.77, 0, 0.175, 1);
  }
  .tg_velo_slide_container .btn-draw:hover .btn-draw__text:after, .tg_velo_slide_container a:hover .btn-draw .btn-draw__text:after {
    width: 100%;
    -webkit-transition: width 0.15s 0.3s cubic-bezier(0.77, 0, 0.175, 1);
    transition: width 0.15s 0.3s cubic-bezier(0.77, 0, 0.175, 1);
  }
  .tg_velo_slide_container .btn-draw:hover .btn-draw__text > span:before, .tg_velo_slide_container a:hover .btn-draw .btn-draw__text > span:before {
    left: 0;
    height: 100%;
    -webkit-transition: height 0.15s 0.45s cubic-bezier(0.77, 0, 0.175, 1);
    transition: height 0.15s 0.45s cubic-bezier(0.77, 0, 0.175, 1);
  }
  .tg_velo_slide_container .btn-draw:hover .btn-draw__text > span:after, .tg_velo_slide_container a:hover .btn-draw .btn-draw__text > span:after {
    right: 0;
    height: 100%;
    -webkit-transition: height 0.15s 0.15s cubic-bezier(0.77, 0, 0.175, 1);
    transition: height 0.15s 0.15s cubic-bezier(0.77, 0, 0.175, 1);
  }
}

/*
	End CSS for dotlife-slider-velo
*/

/*
	Begin CSS for dotlife-slider-popout
*/

.tg_popout_slide_container.slider {
  position: fixed;
  height: 100vh;
  width: 100vw;
  left: 0;
  top: 0;
  background: #777;
  overflow: hidden;
  z-index: 2;
}
body.leftmenu .tg_popout_slide_container.slider
{
	width: calc(100% - 350px);
	left: 350px;
}
.tg_popout_slide_container .slider__wrap {
  position: absolute;
  width: 100vw;
  height: 100vh;
  -webkit-transform: translateX(100vw);
          transform: translateX(100vw);
  top: 0%;
  left: 0;
  right: auto;
  overflow: hidden;
  -webkit-transition: -webkit-transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: -webkit-transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -webkit-transform-origin: 0% 50%;
          transform-origin: 0% 50%;
  -webkit-transition-delay: 450ms;
          transition-delay: 450ms;
  opacity: 0;
}
.tg_popout_slide_container .slider__wrap--hacked {
  opacity: 1;
}
.tg_popout_slide_container .slider__back {
  position: absolute;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: none;
  -webkit-transition: -webkit-filter 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: -webkit-filter 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: filter 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: filter 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-filter 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.tg_popout_slide_container .slider__inner {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0%;
  background-size: cover;
  background-position: center;
  background-repeat: none;
  -webkit-transition: opacity 450ms step-end, -webkit-transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-box-shadow 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: opacity 450ms step-end, -webkit-transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-box-shadow 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), box-shadow 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), opacity 450ms step-end;
  transition: transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), box-shadow 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), opacity 450ms step-end, -webkit-transform 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-box-shadow 450ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  opacity: 0;
  -webkit-box-shadow: 0 3vh 3vh rgba(0, 0, 0, 0);
          box-shadow: 0 3vh 3vh rgba(0, 0, 0, 0);
  padding: 15%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.tg_popout_slide_container .slider__content {
  position: relative;
  top: 50%;
  width: auto;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: white;
  opacity: 0;
  -webkit-transition: opacity 450ms;
  transition: opacity 450ms;
}
.tg_popout_slide_container .slider__content h1 {
  font-size: 9vh;
  line-height: 1.1;
  margin-bottom: 0.75vh;
  pointer-events: none;
  text-shadow: 0 0.375vh 0.75vh rgba(0, 0, 0, 0.1);
  color: #fff;
}
.tg_popout_slide_container .slider__content a.go-to-next {
  cursor: pointer;
  font-size: 2.4vh;
  letter-spacing: 0.3vh;
  font-weight: 100;
  position: relative;
  color: #fff;
  float: right;
  margin-right: 15vh;
}
.tg_popout_slide_container .slider__content a.go-to-next:after {
  content: '';
  display: block;
  width: 9vh;
  background: white;
  height: 1px;
  position: absolute;
  top: 55%;
  left: 6vh;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  -webkit-transform-origin: 0% 50%;
          transform-origin: 0% 50%;
  -webkit-transition: -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.tg_popout_slide_container .slider__content a.go-to-next:before {
  content: '';
  border-top: 1px solid white;
  border-right: 1px solid white;
  display: block;
  width: 1vh;
  height: 1vh;
  -webkit-transform: translateX(0) translateY(-50%) rotate(45deg);
          transform: translateX(0) translateY(-50%) rotate(45deg);
  position: absolute;
  font-weight: 100;
  top: 57%;
  left: 15vh;
  -webkit-transition: -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.tg_popout_slide_container .slider__content a.go-to-next:hover:after {
  -webkit-transform: scaleX(1.5);
          transform: scaleX(1.5);
  -webkit-transition: -webkit-transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: -webkit-transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.tg_popout_slide_container .slider__content a.go-to-next:hover:before {
  -webkit-transform: translateX(6vh) translateY(-50%) rotate(45deg);
          transform: translateX(6vh) translateY(-50%) rotate(45deg);
  -webkit-transition: -webkit-transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: -webkit-transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-transform 1200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.tg_popout_slide_container .slider__slide {
  position: absolute;
  left: 0;
  height: 100%;
  width: 100%;
  -webkit-transition: -webkit-transform 600ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: -webkit-transform 600ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 600ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 600ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-transform 600ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -webkit-transition-delay: 600ms;
          transition-delay: 600ms;
  pointer-events: none;
  z-index: 0;
}
.tg_popout_slide_container .slider__slide--active {
  -webkit-transform: translatex(0%);
          transform: translatex(0%);
  z-index: 2;
}
.tg_popout_slide_container .slider__slide--active .slider__wrap {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-transform-origin: 100% 50%;
          transform-origin: 100% 50%;
  opacity: 1;
  -webkit-animation: none;
          animation: none;
}
.tg_popout_slide_container .slider__slide--active .slider__back {
  -webkit-filter: blur(1.5vh);
          filter: blur(1.5vh);
  -webkit-transition: -webkit-filter 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: -webkit-filter 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: filter 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: filter 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-filter 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -webkit-transition-delay: 900ms !important;
          transition-delay: 900ms !important;
}
@-moz-document url-prefix() { 
  .tg_popout_slide_container .slider__slide--active .slider__back {
     filter: blur(0);
  }
}
.tg_popout_slide_container .slider__slide--active .slider__inner {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-box-shadow: 0 1vh 6vh rgba(0, 0, 0, 0.2);
          box-shadow: 0 1vh 6vh rgba(0, 0, 0, 0.2);
  pointer-events: auto;
  opacity: 1;
  -webkit-transition: opacity 1ms step-end, -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-box-shadow 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: opacity 1ms step-end, -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-box-shadow 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition: transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), box-shadow 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), opacity 1ms step-end;
  transition: transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), box-shadow 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), opacity 1ms step-end, -webkit-transform 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86), -webkit-box-shadow 900ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -webkit-transition-delay: 900ms;
          transition-delay: 900ms;
}
.tg_popout_slide_container .slider__slide--active .slider__content {
  opacity: 1;
  -webkit-transition-delay: 1350ms;
          transition-delay: 1350ms;
}
.tg_popout_slide_container .slider__content .slider__desc {
	width: 70%;
	font-size: 18px;
}
.tg_popout_slide_container .slider__slide:not(.slider__slide--active) .slider__wrap {
  -webkit-animation-name: hack;
          animation-name: hack;
  -webkit-animation-duration: 900ms;
          animation-duration: 900ms;
  -webkit-animation-delay: 450ms;
          animation-delay: 450ms;
  -webkit-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
          animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
@-webkit-keyframes hack {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 1;
  }
  50% {
    -webkit-transform: translateX(-100vw);
            transform: translateX(-100vw);
    opacity: 1;
  }
  51% {
    -webkit-transform: translateX(-100vw);
            transform: translateX(-100vw);
    opacity: 0;
  }
  52% {
    -webkit-transform: translateX(100vw);
            transform: translateX(100vw);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(100vw);
            transform: translateX(100vw);
    opacity: 1;
  }
}
@keyframes hack {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 1;
  }
  50% {
    -webkit-transform: translateX(-100vw);
            transform: translateX(-100vw);
    opacity: 1;
  }
  51% {
    -webkit-transform: translateX(-100vw);
            transform: translateX(-100vw);
    opacity: 0;
  }
  52% {
    -webkit-transform: translateX(100vw);
            transform: translateX(100vw);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(100vw);
            transform: translateX(100vw);
    opacity: 1;
  }
}

.tg_popout_slide_container .slider__link
{
	width: 100%;
	height: 100%;
	position: relative;
	display: block;
}

.tg_popout_slide_container .tg_popout_slide_link
{
	margin: 20px 0 0 0;
	display: inline-block;
	color: #fff;
}

.tg_popout_slide_container .tg_popout_slide_link
{
	font-size: 13px;
	letter-spacing: 2px;
	text-transform: uppercase;
	border-bottom: 1px solid #fff;
	
	-webkit-transition: all 0.4s ease;
	transition: all 0.4s ease;
}

.tg_popout_slide_container .tg_popout_slide_link:hover {
	border-bottom-color: transparent !important;
}

/*
	End CSS for dotlife-slider-popout
*/

/*
	Begin CSS for dotlife-skewed-popout
*/

.tg_skewed_slide_container {
  overflow: hidden;
  width: 100%;
  height: 100vh;
}

.tg_skewed_slide_container .slider-pages {
  overflow: hidden;
  position: relative;
  height: 100vh;
}

.tg_skewed_slide_container .slider-page {
  position: absolute;
  top: 0;
  width: 50%;
  height: 100vh;
  -webkit-transition: -webkit-transform 800ms;
  transition: -webkit-transform 800ms;
  transition: transform 800ms;
  transition: transform 800ms, -webkit-transform 800ms;
}

.tg_skewed_slide_container .slider-page--skew {
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 140%;
  height: 100%;
  background-color: #f9f9f9;
  -webkit-transform: skewX(-18deg);
          transform: skewX(-18deg);
}

.tg_skewed_slide_container .slider-page--left {
  left: 0;
  -webkit-transform: translateX(-32.5vh) translateY(100%) translateZ(0);
          transform: translateX(-32.5vh) translateY(100%) translateZ(0);
}
.tg_skewed_slide_container .slider-page--left .slider-page--skew {
  left: -40%;
}
.tg_skewed_slide_container .slider-page--left .slider-page__content {
  -webkit-transform-origin: 70% 0;
          transform-origin: 70% 0;
}

.tg_skewed_slide_container .slider-page--right {
  left: 50%;
  -webkit-transform: translateX(32.5vh) translateY(-100%) translateZ(0);
          transform: translateX(32.5vh) translateY(-100%) translateZ(0);
}
.tg_skewed_slide_container .slider-page--right .slider-page--skew {
  right: -40%;
}
.tg_skewed_slide_container .slider-page--right .slider-page__content {
  -webkit-transform-origin: 0 100%;
          transform-origin: 0 100%;
}

.tg_skewed_slide_container .slider-page__content {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column wrap;
          flex-flow: column wrap;
  text-align: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0 32% 0 32%;
  background-size: cover;
  background-position: center center;
  -webkit-transform: skewX(18deg);
          transform: skewX(18deg);
  -webkit-transition: -webkit-transform 800ms;
  transition: -webkit-transform 800ms;
  transition: transform 800ms;
  transition: transform 800ms, -webkit-transform 800ms;
}

body.leftmenu .tg_skewed_slide_container .slider-page__content
{
	padding: 0 20% 0 40%;
}

.tg_skewed_slide_container .slider-page__title--big {
  font-size: 60px;
}

.tg_skewed_slide_container .slider-page__link {
  color: #80a1c1;
}
.tg_skewed_slide_container .slider-page__link:hover, .tg_skewed_slide_container .slider-page__link:focus {
  color: #6386a9;
  text-decoration: none;
}

/***********************
 *	Project JS Styles
 **********************/
.tg_skewed_slide_container .js-scrolling__page {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
}

.tg_skewed_slide_container .js-scrolling--active .slider-page {
  -webkit-transform: translateX(0) translateY(0) translateZ(0);
          transform: translateX(0) translateY(0) translateZ(0);
}

.tg_skewed_slide_container .js-scrolling--inactive .slider-page__content {
  -webkit-transform: skewX(18deg) scale(0.9);
          transform: skewX(18deg) scale(0.9);
}

.tg_skewed_slide_container .tg_skewed_slide_content_link
{
	font-size: 13px;
	letter-spacing: 2px;
	text-transform: uppercase;
	border-bottom: 1px solid #222;
	margin-top: 30px;
	
	-webkit-transition: all 0.4s ease;
	transition: all 0.4s ease;
}

.tg_skewed_slide_container .tg_skewed_slide_content_link:hover {
	padding-bottom: 2px;
	border-bottom-color: transparent !important;
}

/*
	End CSS for dotlife-skewed-popout
*/

/*
	Begin CSS for dotlife-clip-path
*/

.tg_clip_path_slide_container.slider {
  position: relative;
  height: 100vh;
}
body.leftmenu .tg_clip_path_slide_container .slide__content
{
	left: calc(5% + 160px);
	bottom: 30px;
}
.tg_clip_path_slide_container .slider__slides {
  z-index: 1;
  position: relative;
  height: 100%;
}
.tg_clip_path_slide_container .slider__control {
  z-index: 2;
  position: absolute;
  top: 50%;
  left: 5%;
  width: 60px;
  height: 60px;
  margin-left: -30px;
  margin-top: -30px;
  border-radius: 50%;
  background: #fff;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  cursor: pointer;
  
  -ms-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -o-transform: scale(0.8);
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
}
.tg_clip_path_slide_container .slider__control--right {
  left: 95%;
}
.tg_clip_path_slide_container .slider__control:hover {
  -ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
}
.tg_clip_path_slide_container .slider__control-line {
  position: absolute;
  left: 23px;
  top: 50%;
  width: 3px;
  height: 14px;
  -webkit-transform-origin: 50% 0;
          transform-origin: 50% 0;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
.tg_clip_path_slide_container .slider__control-line:nth-child(2) {
  -webkit-transform: translateY(1px) rotate(-135deg);
          transform: translateY(1px) rotate(-135deg);
}
.tg_clip_path_slide_container .slider__control--right .slider__control-line {
  left: 37px;
  -webkit-transform-origin: 1px 0;
          transform-origin: 1px 0;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.tg_clip_path_slide_container .slider__control--right .slider__control-line:nth-child(2) {
  -webkit-transform: translateY(1px) rotate(135deg);
          transform: translateY(1px) rotate(135deg);
}
.tg_clip_path_slide_container .slider__control-line:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #e2e2e2;
  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
}
.tg_clip_path_slide_container .slider__control.a--rotation .slider__control-line:after {
  -webkit-animation: arrowLineRotation 0.49s;
          animation: arrowLineRotation 0.49s;
}
.tg_clip_path_slide_container .slider__control.a--rotation .slider__control-line:nth-child(1):after {
  -webkit-animation: arrowLineRotationRev 0.49s;
          animation: arrowLineRotationRev 0.49s;
}

@-webkit-keyframes arrowLineRotation {
  to {
    -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
  }
}

@keyframes arrowLineRotation {
  to {
    -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
  }
}
@-webkit-keyframes arrowLineRotationRev {
  to {
    -webkit-transform: rotate(-180deg);
            transform: rotate(-180deg);
  }
}
@keyframes arrowLineRotationRev {
  to {
    -webkit-transform: rotate(-180deg);
            transform: rotate(-180deg);
  }
}
.tg_clip_path_slide_container .slide {
  overflow: hidden;
  position: absolute;
  left: 50%;
  top: 50%;
  width: 150vw;
  height: 150vh;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-transition: -webkit-clip-path 0s 0.91s;
  transition: -webkit-clip-path 0s 0.91s;
  transition: clip-path 0s 0.91s;
  transition: clip-path 0s 0.91s, -webkit-clip-path 0s 0.91s;
  -webkit-clip-path: circle(30px at 120vw 50%);
          clip-path: circle(30px at 120vw 50%);
}
.tg_clip_path_slide_container .slide.s--prev {
  -webkit-clip-path: circle(30px at 30vw 50%);
          clip-path: circle(30px at 30vw 50%);
}
.tg_clip_path_slide_container .slide.s--active {
  z-index: 1;
  -webkit-transition: -webkit-clip-path 1.3s;
  transition: -webkit-clip-path 1.3s;
  transition: clip-path 1.3s;
  transition: clip-path 1.3s, -webkit-clip-path 1.3s;
  -webkit-clip-path: circle(120vmax at 120vw 50%);
          clip-path: circle(120vmax at 120vw 50%);
}
.slide.s--active {
  -webkit-clip-path: circle(120vmax at 30vw 50%);
          clip-path: circle(120vmax at 30vw 50%);
}
.tg_clip_path_slide_container .slide__inner {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 100vw;
  height: 100vh;
  margin-left: -50vw;
  margin-top: -50vh;
  background-size: cover;
  background-position: center center;
}
.tg_clip_path_slide_container .slide__inner:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
}
.tg_clip_path_slide_container .slide__content {
  position: absolute;
  left: 40px;
  bottom: 40px;
  max-width: 50%;
  color: #fff;
}
.tg_clip_path_slide_container .slide__heading {
  font-size: 60px;
  color: #fff;
}
.tg_clip_path_slide_container .slide__text {
  font-size: 18px;
}
.tg_clip_path_slide_container .slide__text a {
  color: #fff;
  border-bottom: 1px solid #fff;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.tg_clip_path_slide_container .slide__text a:hover {
	padding-bottom: 2px;
	border-bottom-color: transparent !important;
}

/*
	End CSS for dotlife-clip-path
*/

/*
	Begin CSS for dotlife-gallery-preview
*/

.tg_fullscreen_gallery_preview_wrapper.slider-wraper {
  width: 100vw;
  height: 100vh;
}

.tg_fullscreen_gallery_preview_wrapper .slider--item {
  width: 100vw;
  height: 100vh;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.tg_fullscreen_gallery_preview_wrapper .slick-slide {
  position: relative;
  outline: none;
  z-index: 1;
}
.tg_fullscreen_gallery_preview_wrapper .slick-slide.slick-current {
  z-index: 2;
}

.tg_fullscreen_gallery_preview_wrapper .slick-arrow {
  position: absolute;
  width: 150px;
  height: 100%;
  border: none;
  outline: none;
  background-color: rgba(255, 255, 255, 0);
  text-indent: -9999px;
  z-index: 2;
  cursor: pointer;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow:before {
  content: "";
  position: absolute;
  display: block;
  width: 42px;
  height: 42px;
  top: 50%;
  margin-top: -21px;
  -webkit-transition: all, 0.3s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  -khtml-transition: all, 0.3s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  -moz-transition: all, 0.3s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  -ms-transition: all, 0.3s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  -o-transition: all, 0.3s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  transition: all, 0.3s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow:after {
  content: "";
  position: absolute;
  display: block;
  height: 2px;
  width: 160px;
  top: 50%;
  background-color: white;
  -webkit-transition: all, 0.4s 0.1s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  -khtml-transition: all, 0.4s 0.1s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  -moz-transition: all, 0.4s 0.1s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  -ms-transition: all, 0.4s 0.1s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  -o-transition: all, 0.4s 0.1s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  transition: all, 0.4s 0.1s, cubic-bezier(0.55, 0, 0.1, 1) 1s ease-in-out;
  opacity: 0;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-prev {
  left: 0;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-prev:before {
  border-top: 2px solid white;
  border-left: 2px solid white;
  left: 50px;
  -webkit-transform: rotate(-45deg);
  -khtml-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-prev:after {
  left: 300px;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-prev:hover:before {
  left: 120px;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-prev:hover:after {
  left: 113px;
  opacity: 1;
  width: 80px;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-next {
  right: 0;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-next:before {
  border-bottom: 2px solid white;
  border-right: 2px solid white;
  right: 50px;
  -webkit-transform: rotate(-45deg);
  -khtml-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-next:after {
  right: 300px;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-next:hover:before {
  right: 120px;
}
.tg_fullscreen_gallery_preview_wrapper .slick-arrow.slick-next:hover:after {
  right: 113px;
  opacity: 1;
  width: 80px;
}

.tg_fullscreen_gallery_preview_wrapper .slick-dots {
  padding: 0;
  margin: 0;
  width: auto;
  position: absolute;
  bottom: 30px;
  right: 40px;
  z-index: 9;
  text-align: center;
}
.tg_fullscreen_gallery_preview_wrapper .slick-dots li {
  display: inline-block;
  vertical-align: middle;
  margin: 0 3px;
}
.tg_fullscreen_gallery_preview_wrapper .slick-dots li.slick-active button {
  background-color: white;
}
.tg_fullscreen_gallery_preview_wrapper .slick-dots li.slick-active button:hover {
  background-color: white;
}
.tg_fullscreen_gallery_preview_wrapper .slick-dots li button {
  outline: none;
  display: block;
  width: 14px;
  height: 14px;
  border: 1px solid white;
  background-color: rgba(255, 255, 255, 0);
  text-indent: -9999px;
  border-radius: 100%;
  cursor: pointer;
  -webkit-transition: all, 0.2s, ease-in 1s ease-in-out;
  -khtml-transition: all, 0.2s, ease-in 1s ease-in-out;
  -moz-transition: all, 0.2s, ease-in 1s ease-in-out;
  -ms-transition: all, 0.2s, ease-in 1s ease-in-out;
  -o-transition: all, 0.2s, ease-in 1s ease-in-out;
  transition: all, 0.2s, ease-in 1s ease-in-out;
}
.tg_fullscreen_gallery_preview_wrapper .slick-dots li button:hover {
  background-color: white;
}


/*
	End CSS for dotlife-gallery-preview
*/

/*
	Begin CSS for dotlife-split-slick
*/

.slick-list,.slick-slider,.slick-track{position:relative;display:block}.slick-loading .slick-slide,.slick-loading .slick-track{visibility:hidden}.slick-slider{box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;-ms-touch-action:pan-y;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list{overflow:hidden;margin:0;padding:0}.slick-list:focus{outline:0}.slick-list.dragging{cursor:pointer;cursor:hand}.slick-slider .slick-list,.slick-slider .slick-track{-webkit-transform:translate3d(0,0,0);-moz-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);-o-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.slick-track{top:0;left:0}.slick-track:after,.slick-track:before{display:table;content:''}.slick-track:after{clear:both}.slick-slide{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide{float:right}.slick-slide img{display:block}.slick-slide.slick-loading img{display:none}.slick-slide.dragging img{pointer-events:none}.slick-initialized .slick-slide{display:block}.slick-vertical .slick-slide{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden{display:none}

.slick-dots,.slick-next,.slick-prev{position:absolute;display:block;padding:0}.slick-dots li button:before,.slick-next:before,.slick-prev:before{font-family:slick;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.slick-next,.slick-prev{font-size:0;line-height:0;top:50%;width:20px;height:20px;-webkit-transform:translate(0,-50%);-ms-transform:translate(0,-50%);transform:translate(0,-50%);cursor:pointer;color:transparent;border:none;outline:0;background:0 0}.slick-next:focus,.slick-next:hover,.slick-prev:focus,.slick-prev:hover{color:transparent;outline:0;background:0 0}.slick-next:focus:before,.slick-next:hover:before,.slick-prev:focus:before,.slick-prev:hover:before{opacity:1}.slick-next.slick-disabled:before,.slick-prev.slick-disabled:before{opacity:.25}.slick-next:before,.slick-prev:before{font-size:20px;line-height:1;opacity:.75;color:#fff}.slick-prev{left:-25px}[dir=rtl] .slick-prev{right:-25px;left:auto}.slick-prev:before{content:'â†'}.slick-next:before,[dir=rtl] .slick-prev:before{content:'â†’'}.slick-next{right:-25px}[dir=rtl] .slick-next{right:auto;left:-25px}[dir=rtl] .slick-next:before{content:'â†'}.slick-dotted.slick-slider{margin-bottom:30px}.slick-dots{bottom:-25px;width:100%;margin:0;list-style:none;text-align:center}.slick-dots li{position:relative;display:inline-block;width:20px;height:20px;margin:0 5px;padding:0;cursor:pointer}.slick-dots li button{font-size:0;line-height:0;display:block;width:20px;height:20px;padding:5px;cursor:pointer;color:transparent;border:0;outline:0;background:0 0}.slick-dots li button:focus,.slick-dots li button:hover{outline:0}.slick-dots li button:focus:before,.slick-dots li button:hover:before{opacity:1}.slick-dots li button:before{font-size:6px;line-height:20px;position:absolute;top:0;left:0;width:20px;height:20px;content:'â€¢';text-align:center;opacity:.25;color:#000}.slick-dots li.slick-active button:before{opacity:.75;color:#000}

.tg_split_slick_slide_container.split-slideshow {
	position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

.tg_split_slick_slide_container.split-slideshow .bg_overlay
{
	background-color: rgba(0,0,0,0.3);
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.tg_split_slick_slide_container .slideshow {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
.tg_split_slick_slide_container .slideshow .slider {
  width: 100vw;
  height: 100vw;
  z-index: 2;
}
.tg_split_slick_slide_container .slideshow .slider * {
  outline: none;
}
.tg_split_slick_slide_container .slideshow .slider .item {
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
  border: none;
}
.tg_split_slick_slide_container .slideshow .slider .item .text {
  display: none;
}
#page_content_wrapper .inner .sidebar_content .tg_split_slick_slide_container .slideshow .slider .item img {
  min-width: 101%;
  min-height: 101%;
  max-width: none;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
body.leftmenu #page_content_wrapper .inner .sidebar_content .tg_split_slick_slide_container .slideshow .slider .item img
{
	left: calc(50% - 175px);
	min-width: calc(101% - 350px);
}
.tg_split_slick_slide_container .slideshow .slick-dots {
  position: fixed;
  z-index: 100;
  width: 40px;
  height: auto;
  bottom: auto;
  bottom: 0;
  right: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  left: auto;
  color: #fff;
  display: block;
}
.tg_split_slick_slide_container .slideshow .slick-dots li {
  display: block;
  width: 100%;
  height: auto;
}
.tg_split_slick_slide_container .slideshow .slick-dots li button {
  position: relative;
  width: 20px;
  height: 15px;
  text-align: center;
}
.tg_split_slick_slide_container .slideshow .slick-dots li button:before {
  content: '';
  background: #fff;
  color: #fff;
  height: 2px;
  width: 20px;
  border-radius: 0;
  position: absolute;
  top: 50%;
  right: 0;
  left: auto;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  opacity: 0.6;
}
.tg_split_slick_slide_container .slideshow .slick-dots li.slick-active button:before {
  width: 40px;
  opacity: 1;
}
.tg_split_slick_slide_container .slideshow.slideshow-right {
  left: 0;
  z-index: 1;
  width: 50vw;
  pointer-events: none;
}
.tg_split_slick_slide_container .slideshow.slideshow-right .slider {
  left: 0;
  position: absolute;
}

.tg_split_slick_slide_container .slideshow-text {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  z-index: 100;
  font-size: 80px;
  width: 100vw;
  text-align: center;
  color: #fff;
  pointer-events: none;
  text-transform: uppercase;
  letter-spacing: 20px;
  line-height: 0.8;
}
body.leftmenu .tg_split_slick_slide_container .slideshow-text {
	left: calc(50% - 175px);
}
@media (max-width: 767px) {
  .tg_split_slick_slide_container .slideshow-text {
    font-size: 40px;
  }
}

.tg_split_slick_slide_link {
	position: absolute;
	width: 100%;
	height: 100%;
	display: block;
	z-index: 2;
}

/*
	End CSS for dotlife-split-slick
*/

/*
	Begin CSS for dotlife-transitions
*/

.tg_transitions_slide_container {
  height: 100vh;
  width: 100vw;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2;
}

body.leftmenu .tg_transitions_slide_container
{
	width: calc(100% - 350px);
	left: 350px;
}

.tg_transitions_slide_container .bg_overlay {
	background-color: rgba(0,0,0,0.2);
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}

.tg_transitions_slide_container .swiper-container {
  height: 100%;
  width: 100%;
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}

.tg_transitions_slide_container .swiper-image {
  width: 50%;
  height: 100%;
}
.tg_transitions_slide_container .swiper-image-inner {
  background-size: cover;
  background-position: center center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
  background-color: hsla(0, 0%, 0%, 0.2);
  background-blend-mode: overlay;
}
.tg_transitions_slide_container .swiper-image-inner.swiper-image-left {
  padding: 0 4rem 0 4rem;
}
.tg_transitions_slide_container .swiper-image-left, .swiper-image-inner.swiper-image-right {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
  -webkit-transition: all 1s linear;
  transition: all 1s linear;
  -webkit-transition-delay: 1s;
          transition-delay: 1s;
}
.tg_transitions_slide_container .swiper-slide.swiper-slide-active .swiper-image-left {
  -webkit-filter: grayscale(0%);
          filter: grayscale(0%);
}
.tg_transitions_slide_container .swiper-slide.swiper-slide-active .swiper-image-right {
  -webkit-filter: grayscale(0%);
          filter: grayscale(0%);
}
.tg_transitions_slide_container .swiper-image-left h1 {
  color: #fff;
  -ms-flex-item-align: start;
      align-self: flex-start;
  margin: auto;
  font-size: 4.5rem;
  line-height: 1;
  -webkit-transition: all .8s cubic-bezier(0.215, 0.61, 0.355, 1) 1.1s;
  transition: all .8s cubic-bezier(0.215, 0.61, 0.355, 1) 1.1s;
  -webkit-transform: translate3d(-20%, 0, 0);
          transform: translate3d(-20%, 0, 0);
  opacity: 0;
}
.tg_transitions_slide_container h1 + p {
  font-size: 14px;
  letter-spacing: 2px;
  margin: 0;
  line-height: 1;
  margin-bottom: auto;
  -ms-flex-item-align: end;
      align-self: flex-end;
  text-transform: uppercase;
  -webkit-transition: all .8s cubic-bezier(0.215, 0.61, 0.355, 1) 1.3s;
  transition: all .8s cubic-bezier(0.215, 0.61, 0.355, 1) 1.3s;
  -webkit-transform: translate3d(-20%, 0, 0);
          transform: translate3d(-20%, 0, 0);
  opacity: 0;
  font-weight: 500;
  color: #fff;
  padding-right: 8rem;
}
.tg_transitions_slide_container p.paragraph {
  margin: 0;
  color: #fff;
  width: 100%;
  max-width: 350px;
  font-size: 1.2rem;
  opacity: 0;
  -webkit-transition: all .6s cubic-bezier(0.215, 0.61, 0.355, 1) 1.4s;
  transition: all .6s cubic-bezier(0.215, 0.61, 0.355, 1) 1.4s;
  -webkit-transform: translate3d(-20%, 0, 0);
          transform: translate3d(-20%, 0, 0);
}

.tg_transitions_slide_container .swiper-slide.swiper-slide-active h1,
.tg_transitions_slide_container .swiper-slide.swiper-slide-active p.paragraph,
.tg_transitions_slide_container .swiper-slide.swiper-slide-active h1 span.emphasis,
.tg_transitions_slide_container .swiper-slide.swiper-slide-active h1 + p {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  opacity: 1;
}

.tg_transitions_slide_container .swiper-container {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.tg_transitions_slide_container .swiper-container-no-flexbox .swiper-slide {
  float: left;
}
.tg_transitions_slide_container .swiper-container-vertical > .swiper-wrapper {
  -webkit-box-orient: vertical;
  -ms-flex-direction: column;
  flex-direction: column;
}
.tg_transitions_slide_container .swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
.tg_transitions_slide_container .swiper-container-android .swiper-slide, .swiper-wrapper {
  -webkit-transform: translate3d(0px, 0, 0);
  transform: translate3d(0px, 0, 0);
}
.tg_transitions_slide_container .swiper-container-multirow > .swiper-wrapper {
  -webkit-box-lines: multiple;
  -moz-box-lines: multiple;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.tg_transitions_slide_container .swiper-container-free-mode > .swiper-wrapper {
  -webkit-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
  margin: 0 auto;
}
.tg_transitions_slide_container .swiper-slide {
  -webkit-flex-shrink: 0;
  -ms-flex: 0 0 auto;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #fff;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  
}
/* Auto Height */
.tg_transitions_slide_container .swiper-container-autoheight, .tg_transitions_slide_container .swiper-container-autoheight .swiper-slide {
  height: auto;
}
.tg_transitions_slide_container .swiper-container-autoheight .swiper-wrapper {
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-transition-property: -webkit-transform, height;
  -webkit-transition-property: height, -webkit-transform;
  transition-property: height, -webkit-transform;
  transition-property: transform, height;
  transition-property: transform, height, -webkit-transform;
}
/* a11y */
.tg_transitions_slide_container .swiper-container .swiper-notification {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  z-index: -1000;
}
/* IE10 Windows Phone 8 Fixes */
.tg_transitions_slide_container .swiper-wp8-horizontal {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}
.tg_transitions_slide_container .swiper-wp8-vertical {
  -ms-touch-action: pan-x;
  touch-action: pan-x;
}
/* Arrows */
.tg_transitions_slide_container .swiper-button-prev, .tg_transitions_slide_container .swiper-button-next {
  position: absolute;
  top: 50%;
  width: 27px;
  height: 44px;
  margin-top: -22px;
  z-index: 10;
  cursor: pointer;
  background-size: 27px 44px;
  background-position: center;
  background-repeat: no-repeat;
}
.tg_transitions_slide_container .swiper-button-prev.swiper-button-disabled,
.tg_transitions_slide_container .swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}
/* Pagination Styles */
.tg_transitions_slide_container .swiper-pagination {
  position: absolute;
  text-align: center;
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.tg_transitions_slide_container .swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}
/* Common Styles */
.tg_transitions_slide_container .swiper-pagination-fraction,
.tg_transitions_slide_container .swiper-pagination-custom,
.tg_transitions_slide_container .swiper-container-horizontal > .swiper-pagination-bullets {
  bottom: 10px;
  left: 0;
  width: 100%;
}
/* Bullets */
.tg_transitions_slide_container .swiper-pagination-bullet {
  width: 14px;
  height: 14px;
  display: inline-block;
  background: #fff;
  opacity: 0.4;
}
.tg_transitions_slide_container button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}
.tg_transitions_slide_container .swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}
.tg_transitions_slide_container .swiper-pagination-white .swiper-pagination-bullet {
  background: #fff;
}
.tg_transitions_slide_container .swiper-pagination-bullet-active {
  opacity: 1;
  background: #fff;
}
.tg_transitions_slide_container .swiper-pagination-white .swiper-pagination-bullet-active {
  background: #fff;
}
.tg_transitions_slide_container .swiper-pagination-black .swiper-pagination-bullet-active {
  background: #000;
}
.tg_transitions_slide_container .swiper-container-vertical > .swiper-pagination-bullets {
  right: 15px;
  bottom: 15px;
  top: 47%;
}
.tg_transitions_slide_container .swiper-container-vertical
  > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 5px 0;
  display: block;
  border-radius: 50px;
}
.tg_transitions_slide_container .swiper-container-horizontal
  > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 5px;
}
/* Progress */
.tg_transitions_slide_container .swiper-pagination-progress {
  background: rgba(0, 0, 0, 0.25);
  position: absolute;
}
.tg_transitions_slide_container .swiper-pagination-progress .swiper-pagination-progressbar {
  background: #007aff;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: left top;
  transform-origin: left top;
}
.tg_transitions_slide_container .swiper-container-rtl .swiper-pagination-progress .swiper-pagination-progressbar {
  -webkit-transform-origin: right top;
  transform-origin: right top;
}
.tg_transitions_slide_container .swiper-container-horizontal > .swiper-pagination-progress {
  width: 100%;
  height: 4px;
  left: 0;
  top: 0;
}
.tg_transitions_slide_container .swiper-container-vertical > .swiper-pagination-progress {
  width: 4px;
  height: 100%;
  left: 0;
  top: 0;
}
.tg_transitions_slide_container .swiper-pagination-progress.swiper-pagination-white {
  background: rgba(255, 255, 255, 0.5);
}
.tg_transitions_slide_container .swiper-pagination-progress.swiper-pagination-white .swiper-pagination-progressbar {
  background: #fff;
}
.tg_transitions_slide_container .swiper-pagination-progress.swiper-pagination-black .swiper-pagination-progressbar {
  background: #000;
}
/* 3D Container */
.tg_transitions_slide_container .swiper-container-3d {
  -webkit-perspective: 1200px;
  -o-perspective: 1200px;
  perspective: 1200px;
}
.tg_transitions_slide_container .swiper-container-3d .swiper-wrapper,
.tg_transitions_slide_container .swiper-container-3d .swiper-slide,
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-left,
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-right,
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-top,
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-bottom,
.tg_transitions_slide_container .swiper-container-3d .swiper-cube-shadow {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-left,
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-right,
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-top,
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-left {
  /* Safari 4+, Chrome */
  /* Chrome 10+, Safari 5.1+, iOS 5+ */
  /* Firefox 3.6-15 */
  /* Opera 11.10-12.00 */
  background-image: -webkit-gradient(
    linear,
    right top, left top,
    from(rgba(0, 0, 0, 0.5)),
    to(rgba(0, 0, 0, 0))
  );
  background-image: linear-gradient(
    to left,
    rgba(0, 0, 0, 0.5),
    rgba(0, 0, 0, 0)
  );
  /* Firefox 16+, IE10, Opera 12.50+ */
}
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-right {
  /* Safari 4+, Chrome */
  /* Chrome 10+, Safari 5.1+, iOS 5+ */
  /* Firefox 3.6-15 */
  /* Opera 11.10-12.00 */
  background-image: -webkit-gradient(
    linear,
    left top, right top,
    from(rgba(0, 0, 0, 0.5)),
    to(rgba(0, 0, 0, 0))
  );
  background-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.5),
    rgba(0, 0, 0, 0)
  );
  /* Firefox 16+, IE10, Opera 12.50+ */
}
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-top {
  /* Safari 4+, Chrome */
  /* Chrome 10+, Safari 5.1+, iOS 5+ */
  /* Firefox 3.6-15 */
  /* Opera 11.10-12.00 */
  background-image: -webkit-gradient(
    linear,
    left bottom, left top,
    from(rgba(0, 0, 0, 0.5)),
    to(rgba(0, 0, 0, 0))
  );
  background-image: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.5),
    rgba(0, 0, 0, 0)
  );
  /* Firefox 16+, IE10, Opera 12.50+ */
}
.tg_transitions_slide_container .swiper-container-3d .swiper-slide-shadow-bottom {
  /* Safari 4+, Chrome */
  /* Chrome 10+, Safari 5.1+, iOS 5+ */
  /* Firefox 3.6-15 */
  /* Opera 11.10-12.00 */
  background-image: -webkit-gradient(
    linear,
    left top, left bottom,
    from(rgba(0, 0, 0, 0.5)),
    to(rgba(0, 0, 0, 0))
  );
  background-image: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.5),
    rgba(0, 0, 0, 0)
  );
  /* Firefox 16+, IE10, Opera 12.50+ */
}
/* Coverflow */
.tg_transitions_slide_container .swiper-container-coverflow .swiper-wrapper,
.tg_transitions_slide_container .swiper-container-flip .swiper-wrapper {
  /* Windows 8 IE 10 fix */
  -ms-perspective: 1200px;
}
/* Cube + Flip */
.tg_transitions_slide_container .swiper-container-cube, .swiper-container-flip {
  overflow: visible;
}
.tg_transitions_slide_container .swiper-container-cube .swiper-slide, .swiper-container-flip .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
}
.tg_transitions_slide_container .swiper-container-cube .swiper-slide .swiper-slide,
.tg_transitions_slide_container .swiper-container-flip .swiper-slide .swiper-slide {
  pointer-events: none;
}
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-active,
.tg_transitions_slide_container .swiper-container-flip .swiper-slide-active,
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-active .swiper-slide-active,
.tg_transitions_slide_container .swiper-container-flip .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-shadow-top,
.tg_transitions_slide_container .swiper-container-flip .swiper-slide-shadow-top,
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-shadow-bottom,
.tg_transitions_slide_container .swiper-container-flip .swiper-slide-shadow-bottom,
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-shadow-left,
.tg_transitions_slide_container .swiper-container-flip .swiper-slide-shadow-left,
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-shadow-right,
.tg_transitions_slide_container .swiper-container-flip .swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
/* Cube */
.tg_transitions_slide_container .swiper-container-cube .swiper-slide {
  visibility: hidden;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  width: 100%;
  height: 100%;
}
.tg_transitions_slide_container .swiper-container-cube.swiper-container-rtl .swiper-slide {
  -webkit-transform-origin: 100% 0;
  transform-origin: 100% 0;
}
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-active,
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-next,
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-prev,
.tg_transitions_slide_container .swiper-container-cube .swiper-slide-next + .swiper-slide {
  pointer-events: auto;
  visibility: visible;
}
.tg_transitions_slide_container .swiper-container-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.6;
  -webkit-filter: blur(50px);
  filter: blur(50px);
  z-index: 0;
}
/* Fade */
.tg_transitions_slide_container .swiper-container-fade.swiper-container-free-mode .swiper-slide {
  -webkit-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}
.tg_transitions_slide_container .swiper-container-fade .swiper-slide {
  pointer-events: none;
  -webkit-transition-property: opacity;
  transition-property: opacity;
}
.tg_transitions_slide_container .swiper-container-fade .swiper-slide .swiper-slide {
  pointer-events: none;
}
.tg_transitions_slide_container .swiper-container-fade .swiper-slide-active,
.tg_transitions_slide_container .swiper-container-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.tg_transitions_slide_container .swiper-zoom-container {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
}
.tg_transitions_slide_container .swiper-zoom-container > img,
.tg_transitions_slide_container .swiper-zoom-container > svg,
.tg_transitions_slide_container .swiper-zoom-container > canvas {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
/* Preloader */
.tg_transitions_slide_container .swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
  -webkit-animation: swiper-preloader-spin 1s steps(12, end) infinite;
  animation: swiper-preloader-spin 1s steps(12, end) infinite;
}
.tg_transitions_slide_container .swiper-lazy-preloader:after {
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%236c6c6c'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
  background-position: 50%;
  background-size: 100%;
  background-repeat: no-repeat;
}
.tg_transitions_slide_container .swiper-lazy-preloader-white:after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%23fff'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
@-webkit-keyframes swiper-preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes swiper-preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

.tg_transitions_slide_content_link
{
	font-size: 13px;
	letter-spacing: 2px;
	text-transform: uppercase;
	border-bottom: 1px solid #fff;
	color: #fff;
	display: inline-block;
	margin-top: 30px;
	
	-webkit-transition: all 0.4s ease;
	transition: all 0.4s ease;
}

.tg_transitions_slide_content_link:hover {
	border-bottom-color: transparent !important;
}

/*
	End CSS for dotlife-transitions
*/

/*
	Begin CSS for dotlife-album-grid
*/

.gallery_grid_content_wrapper.album_grid .tilter {
	display: block;
	position: relative;
	color: #fff;
	flex: none;
	perspective: 1000px;
}

.gallery_grid_content_wrapper.album_grid .tilter * {
	pointer-events: none;
}

.gallery_grid_content_wrapper.album_grid .tilter:hover,
.gallery_grid_content_wrapper.album_grid .tilter:focus {
	color: #fff;
	outline: none;
}

.gallery_grid_content_wrapper.album_grid .tilter__figure,
.gallery_grid_content_wrapper.album_grid .tilter__deco,
.gallery_grid_content_wrapper.album_grid .tilter__caption {
	will-change: transform;
}

.gallery_grid_content_wrapper.album_grid .tilter__figure,
.gallery_grid_content_wrapper.album_grid .tilter__image {
	margin: 0;
	width: 100%;
	height: 100%;
	display: block;
}

.gallery_grid_content_wrapper.album_grid .tilter__figure > * {
	transform: translateZ(0px); /* Force correct stacking order */
}

.gallery_grid_content_wrapper.album_grid .smooth .tilter__figure,
.gallery_grid_content_wrapper.album_grid .smooth .tilter__deco--overlay,
.gallery_grid_content_wrapper.album_grid .smooth .tilter__deco--lines,
.gallery_grid_content_wrapper.album_grid .smooth .tilter__deco--shine div,
.gallery_grid_content_wrapper.album_grid .smooth .tilter__caption {
	transition: transform 0.2s ease-out;
}

.gallery_grid_content_wrapper.album_grid .tilter__figure {
	position: relative;
}

.gallery_grid_content_wrapper.album_grid .tilter__figure::before {
	content: '';
	position: absolute;
	width: 90%;
	height: 90%;
	top: 5%;
	left: 5%;
}

.gallery_grid_content_wrapper.album_grid .tilter__deco {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.gallery_grid_content_wrapper.album_grid .tilter__deco--shine div {
	position: absolute;
	width: 200%;
	height: 200%;
	top: -50%;
	left: -50%;
	background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.5) 0%, rgba(255, 255, 255, 0.25) 50%, transparent 100%);
}

.gallery_grid_content_wrapper.album_grid .tilter__deco--lines {
	fill: none;
	stroke: #fff;
	stroke-width: 1.5px;
}

.gallery_grid_content_wrapper.album_grid .tilter__caption {
	position: absolute;
	bottom: 0;
	width: 100%;
	padding: 3em;
}

.gallery_grid_content_wrapper.album_grid .tg_two_cols .tilter__caption {
	padding: 4em;
}

.gallery_grid_content_wrapper.album_grid .tg_four_cols .tilter__caption {
	padding: 2.5em;
}

.gallery_grid_content_wrapper.album_grid .tg_five_cols .tilter__caption {
	padding: 2em;
}

.gallery_grid_content_wrapper.album_grid .tilter__title {
	margin: 0;
	font-size: 1.6em;
	line-height: 1;
	color: #fff;
}

.gallery_grid_content_wrapper.album_grid .tilter__description {
	margin: 5px 0 0 0;
	padding: 0 !important;
	font-size: 0.6em;
	text-transform: uppercase;
	letter-spacing: 2px;
}

.gallery_grid_content_wrapper.album_grid .tilter__deco--overlay {
	background: rgba(0,0,0,0.2);
}

/* Individual styles */

.gallery_grid_content_wrapper.album_grid .tilter--3 .tilter__caption {
	padding: 2em;
	text-align: right;
	text-shadow: 0.1em 0.8em 1em rgba(0,0,0,0.35);
}

@media screen and (min-width: 30em) {
	.gallery_grid_content_wrapper.album_grid .tilter--4 .tilter__deco--lines {
		transform: scale3d(0.8,0.8,1);
		transition: transform 0.4s;
	}
	.gallery_grid_content_wrapper.album_grid .tilter--4:hover .tilter__deco--lines {
		transform: scale3d(1,1,1);
	}
	.gallery_grid_content_wrapper.album_grid .tilter--4 .tilter__title,
	.gallery_grid_content_wrapper.album_grid .tilter--4 .tilter__description {
		transform: translate3d(0,80px,0);
		opacity: 0;
		transition: transform 0.4s, opacity 0.4s;
	}
	.gallery_grid_content_wrapper.album_grid .tilter--4:hover .tilter__description {
		transition-delay: 0.1s;
	}
	.gallery_grid_content_wrapper.album_grid .tilter--4:hover .tilter__title,
	.gallery_grid_content_wrapper.album_grid .tilter--4:hover .tilter__description {
		transform: translate3d(0,0,0);
		opacity: 1;
	}
}

/* Example 5 (line animating) */
.gallery_grid_content_wrapper.album_grid .tilter--5 .tilter__deco--lines path {
	stroke-dasharray: 1270;
	stroke-dashoffset: 1270;
	transition: stroke-dashoffset 0.7s;
}

.gallery_grid_content_wrapper.album_grid .tilter--5:hover .tilter__deco--lines path {
	stroke-dashoffset: 0;
}

.gallery_grid_content_wrapper.album_grid .tilter--5 .tilter__figure::before {
	box-shadow: none;
}

.gallery_grid_content_wrapper.album_grid .tilter--6 .tilter__deco--lines {
	stroke: #000;
	stroke-width: 6px;
	top: -40px;
	left: -40px;
}

.gallery_grid_content_wrapper.album_grid .tilter--6 .tilter__caption {
	padding: 0 4em 5.5em 1.5em;
}

.gallery_grid_content_wrapper.album_grid .tilter--6 .tilter__figure::before {
	box-shadow: none;
}

.gallery_grid_content_wrapper.album_grid .tilter--7 .tilter__deco--lines {
	stroke-width: 20px;
	transform: scale3d(0.9,0.9,1);
	opacity: 0;
	transition: transform 0.3s, opacity 0.3s;
}

.gallery_grid_content_wrapper.album_grid .tilter--7:hover .tilter__deco--lines {
	opacity: 1;
	transform: scale3d(1,1,1);
}

.gallery_grid_content_wrapper.album_grid .tilter--7 .tilter__figure::before {
	box-shadow: none;
}

/* Example 8 (different line) */
.gallery_grid_content_wrapper.album_grid .tilter--8 {
	perspective: none;
}

.gallery_grid_content_wrapper.album_grid .tilter--8 .tilter__figure {
	transform-style: flat;
}

.gallery_grid_content_wrapper.album_grid .tilter--8 .tilter__deco--lines {
	stroke-width: 6px;
}

.gallery_grid_content_wrapper.album_grid .tilter--8 .tilter__figure::before {
	box-shadow: none;
}

.gallery_grid_content_wrapper .gallery_grid_item.tilter
{
	overflow: visible;
}

/*
	End CSS for dotlife-album-grid
*/

/*
	Begin CSS for dotlife-slider-property-clip
*/

.tg_slider_property_clip_wrapper.intro {
	position: relative;
	width: 100%;
	clear: both;
}
.tg_slider_property_clip_wrapper.intro .content {
	float: right;
	height: 100%;
	width: 50%;
	padding: 3rem 5rem 3rem 5rem;
	display: table;
}
.tg_slider_property_clip_wrapper.intro .content.left {
	float: left;
}
.tg_slider_property_clip_wrapper.intro .content > div {
	display: table-cell;
	vertical-align: middle;
}
.tg_slider_property_clip_wrapper.intro .content span {
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 2px;
  display: inline-block;
  text-transform: uppercase;
  font-family: sans-serif;
  margin-bottom: 20px;
}
.tg_slider_property_clip_wrapper.intro .content > div h1 {
	font-size: 70px;
	line-height: 1.2;
}
.tg_slider_property_clip_wrapper.intro .content > div p {
 
}
.tg_slider_property_clip_wrapper.intro .slider {
	float: left;
	position: relative;
	width: 50%;
	height: 100%;
}
.tg_slider_property_clip_wrapper.intro .slider.right {
	float: right;
}
.tg_slider_property_clip_wrapper.intro .slider li {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	-webkit-transition: clip .7s ease-in-out, z-index 0s .7s;
	transition: clip .7s ease-in-out, z-index 0s .7s;
	clip: rect(0, 100vw, 100vh, 100vw);
}
.tg_slider_property_clip_wrapper.intro li.current {
	z-index: 1;
	clip: rect(0, 100vw, 100vh, 0);
}
.tg_slider_property_clip_wrapper.intro li.prev {
	clip: rect(0, 0, 100vh, 0);
}
.tg_slider_property_clip_wrapper.intro .slider nav {
	position: absolute;
	bottom: 5%;
	left: 0;
	right: 0;
	text-align: center;
	z-index: 10;
}
.tg_slider_property_clip_wrapper.intro nav a {
	display: inline-block;
	border-radius: 50%;
	width: 0.8rem;
	height: 0.8rem;
  min-width: 12px;
  min-height: 12px;
	background: #fff;
	margin: 0 0.5rem;
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s;
}
.tg_slider_property_clip_wrapper.intro nav a.current_dot {
	-webkit-transform: scale(1.4);
	        transform: scale(1.4);
}
@media screen and (max-width: 700px) {
	.tg_slider_property_clip_wrapper.intro .content {
		width: 100%;
		height: 30%;
	}
	.tg_slider_property_clip_wrapper.intro .slider {
		width: 100%;
		height: 70%;
	}
}

/*
	End CSS for dotlife-slider-property-clip
*/

/*
	Begin CSS for dotlife-slider-slice
*/

.tg_slice_slide_container .slides-nav {
  z-index: 99;
  position: absolute;
  right: -5%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
  color: #222;
}
@media (min-width: 54em) {
  .tg_slice_slide_container .slides-nav {
    right: 2%;
  }
}
.tg_slice_slide_container .slides-nav__nav {
  position: relative;
  right: 0;
  display: block;
  font-size: 1em;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  -webkit-transform-origin: center;
          transform-origin: center;
}
.tg_slice_slide_container .slides-nav button {
  position: relative;
  display: inline-block;
  padding: 0.35em;
  font-size: 16px;
  margin: 0;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: transparent;
  border: 0;
  overflow-x: hidden;
  -webkit-transition: color 0.5s ease;
  transition: color 0.5s ease;
}
.tg_slice_slide_container .slides-nav button:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  height: 1px;
  width: 0;
  background: #222;
  -webkit-transition: width 0.4s ease;
  transition: width 0.4s ease;
}
.tg_slice_slide_container .slides-nav button:hover {
  cursor: pointer;
  color: rgba(17, 17, 17, 0.75);
  -webkit-transition: color 0.5s ease;
  transition: color 0.5s ease;
}
.tg_slice_slide_container .slides-nav button:hover:after {
  width: 100%;
  -webkit-transition: width 0.4s ease;
  transition: width 0.4s ease;
}
.tg_slice_slide_container .slides-nav button:focus {
  outline: 0;
}
.is-sliding .tg_slice_slide_container .slides-nav {
  pointer-events: none;
}

.tg_slice_slide_container.slides {
  position: relative;
  display: block;
  height: 100%;
  width: 100%;
  background: #fff;
  -webkit-transition: background 1s cubic-bezier(0.99, 1, 0.92, 1);
  transition: background 1s cubic-bezier(0.99, 1, 0.92, 1);
}
.is-sliding .tg_slice_slide_container .slides {
  background: #ededed;
  -webkit-transition: background 0.3s cubic-bezier(0.99, 1, 0.92, 1);
  transition: background 0.3s cubic-bezier(0.99, 1, 0.92, 1);
}

.tg_slice_slide_container .slide {
  z-index: -1;
  padding: 0;
  position: absolute;
  width: 99.9%;
  height: 99.9%;
  -webkit-transition: z-index 1s ease;
  transition: z-index 1s ease;
}
.tg_slice_slide_container .slide.is-active {
  z-index: 19;
  -webkit-transition: z-index 1s ease;
  transition: z-index 1s ease;
}
.tg_slice_slide_container .slide__content {
  position: relative;
  margin: 0 auto;
  height: 80%;
  width: 80%;
  top: 10%;
}
.tg_slice_slide_container .slide__header {
  z-index: 9;
  position: relative;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
  -webkit-transform: translateX(5%);
          transform: translateX(5%);
}
@media (min-width: 54em) {
  .tg_slice_slide_container .slide__header {
    -webkit-transform: translateX(-5%);
            transform: translateX(-5%);
  }
}
.tg_slice_slide_container .slide__title {
  font-size: 2.5em;
  font-weight: 700;
  color: #222;
  width: 70%;
  overflow-y: hidden;
}
@media (min-width: 54em) {
  .tg_slice_slide_container .slide__title {
    font-size: 5em;
  }
}
.tg_slice_slide_container .slide__title .title-line {
  display: block;
  overflow-y: hidden;
}
.tg_slice_slide_container .slide__title .title-line span {
  display: inline-block;
  -webkit-transform: translate3d(0, 140%, 0);
          transform: translate3d(0, 140%, 0);
  opacity: 0;
  -webkit-transition: opacity 0.8s ease, -webkit-transform 0.4s ease;
  transition: opacity 0.8s ease, -webkit-transform 0.4s ease;
  transition: transform 0.4s ease, opacity 0.8s ease;
  transition: transform 0.4s ease, opacity 0.8s ease, -webkit-transform 0.4s ease;
}
.tg_slice_slide_container .slide__title .title-line span:nth-child(1) {
  -webkit-transition-delay: 0.15s;
          transition-delay: 0.15s;
}
.tg_slice_slide_container .slide__title .title-line span:nth-child(2) {
  -webkit-transition-delay: 0.3s;
          transition-delay: 0.3s;
}
.tg_slice_slide_container .is-active .slide__title .title-line span {
  -webkit-transform: translate3d(0, 0%, 0);
          transform: translate3d(0, 0%, 0);
  opacity: 1;
  -webkit-transition: opacity 0.1s ease, -webkit-transform 0.6s cubic-bezier(0.77, 0, 0.175, 1);
  transition: opacity 0.1s ease, -webkit-transform 0.6s cubic-bezier(0.77, 0, 0.175, 1);
  transition: transform 0.6s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.1s ease;
  transition: transform 0.6s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.1s ease, -webkit-transform 0.6s cubic-bezier(0.77, 0, 0.175, 1);
}
.tg_slice_slide_container .is-active .slide__title .title-line:nth-of-type(2n) span {
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}
.tg_slice_slide_container .slide__figure {
  z-index: 7;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  height: 100%;
  width: 100.2%;
  -webkit-transition: -webkit-transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  transition: -webkit-transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), -webkit-transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}
.is-sliding .tg_slice_slide_container .slide__figure {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-transition: -webkit-transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  transition: -webkit-transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), -webkit-transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}
.tg_slice_slide_container .slide__img {
  position: relative;
  display: block;
  background-size: cover;
  background-position: 50%;
  -webkit-backface-visibility: hidden;
  height: 0%;
  width: 100%;
  -webkit-filter: grayscale(0%);
          filter: grayscale(0%);
  -webkit-transition: height 1s 1.4s cubic-bezier(0.19, 1, 0.22, 1), -webkit-filter 0.4s 0.1s ease;
  transition: height 1s 1.4s cubic-bezier(0.19, 1, 0.22, 1), -webkit-filter 0.4s 0.1s ease;
  transition: height 1s 1.4s cubic-bezier(0.19, 1, 0.22, 1), filter 0.4s 0.1s ease;
  transition: height 1s 1.4s cubic-bezier(0.19, 1, 0.22, 1), filter 0.4s 0.1s ease, -webkit-filter 0.4s 0.1s ease;
}
.tg_slice_slide_container .is-active .slide__img {
  height: 100%;
  opacity: 1;
  -webkit-transition: height 0.5s 0.3s cubic-bezier(0.77, 0, 0.175, 1), -webkit-filter 0.4s 0.1s ease;
  transition: height 0.5s 0.3s cubic-bezier(0.77, 0, 0.175, 1), -webkit-filter 0.4s 0.1s ease;
  transition: height 0.5s 0.3s cubic-bezier(0.77, 0, 0.175, 1), filter 0.4s 0.1s ease;
  transition: height 0.5s 0.3s cubic-bezier(0.77, 0, 0.175, 1), filter 0.4s 0.1s ease, -webkit-filter 0.4s 0.1s ease;
}
.is-sliding .tg_slice_slide_container .slide__img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}

.tg_slice_slide_container .tg_slice_slide_link {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: block;
}

.tg_slider_property_clip_wrapper.intro .content a {
  color: #222;
  border-bottom: 1px solid #222;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.tg_slider_property_clip_wrapper.intro .content a:hover {
	border-bottom-color: transparent !important;
}

/*
	End CSS for dotlife-slider-slice
*/

/*
	Begin CSS for dotlife-slider-flip
*/

.tg_flip_slide_container {
  height: 100vh;
}

@media only screen and (min-width: 1200px) {
  .tg_flip_slide_container .container {
    max-width: 100%;
	width: 100%;
	padding: 0;
  }
}

.tg_flip_slide_container .container {
  height: 100%;
}
.tg_flip_slide_container .container .gallery {
  list-style-type: none;
  height: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  margin: 0 !important;
}
.tg_flip_slide_container .container .gallery li {
  float: left;
  height: 100%;
  width: 550px;
}
.tg_flip_slide_container .container .gallery .flip {
  height: 100%;
  width: 100%;
  position: relative;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  transform-style: preserve-3d;

  -webkit-perspective: 2000px;
  -moz-perspective: 2000px;
  perspective: 2000px;
}
.tg_flip_slide_container .container .gallery li:hover .front-side {
  opacity: 0;
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
.tg_flip_slide_container .container .gallery li:hover .back-side {
  opacity: 1;
  -webkit-transform: rotateY(0deg);
  -moz-transform: rotateY(0deg);
  transform: rotateY(0deg);
}
.tg_flip_slide_container .container .gallery .front-side,
.tg_flip_slide_container .container .gallery .back-side {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility:  hidden;
  -moz-backface-visibility:  hidden;
  backface-visibility:  hidden;

  -webkit-transition: transform 0.4s ease-out, opacity 0.3s ease-out;
  -moz-transition: transform 0.4s ease-out, opacity 0.3s ease-out;
  transition: transform 0.4s ease-out, opacity 0.3s ease-out;
}
.tg_flip_slide_container .container .gallery .front-side {
  opacity: 1;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 50% 50%;

  -webkit-transform: rotateY(0deg);
  -moz-transform: rotateY(0deg);
  transform: rotateY(0deg);
}
.tg_flip_slide_container .container .gallery .back-side {
  opacity: 0;
  -webkit-transform: rotateY(-180deg);
  -moz-transform: rotateY(-180deg);
  transform: rotateY(-180deg);
}
.tg_flip_slide_container .container .gallery .back-side > a {
  display: block;
  width: 100%;
  height: 100%;
}

.tg_flip_slide_container .container .gallery .content {
  text-align: center;
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  transform: translateY(-50%);
  padding: 60px;
}

.tg_flip_slide_container .container .gallery li:hover .text {
  opacity: 1;
  visibility: visible;
  max-height: 2000px;
}
.tg_flip_slide_container .container .gallery .text {
  opacity: 0;
  visibility: hidden;
  max-height: 0;
}
.tg_flip_slide_container .container .gallery .content h2 {
  position: relative;
  font-size: 36px;
  margin-bottom: 25px;
}
.tg_flip_slide_container .container .gallery .content h2:after {
  content: '';
  position: absolute;
  width: 50px;
  height: 3px;
  bottom: -10px;
  left: 50%;
  margin-left: -25px;
}
.tg_flip_slide_container .container .gallery .content p {
  margin: 10px 0;
}

.tg_flip_slide_container .tg_flip_slide_content_link
{
	font-size: 13px;
	letter-spacing: 2px;
	text-transform: uppercase;
	border-bottom: 1px solid #222;
	margin-top: 30px;
	cursor: pointer;
	display: inline-block;
	
	-webkit-transition: all 0.4s ease;
	transition: all 0.4s ease;
}

.tg_flip_slide_container .tg_flip_slide_content_link:hover {
	padding-bottom: 2px;
	border-bottom-color: transparent !important;
}

/*
	End CSS for dotlife-slider-flip
*/

/*
	Begin CSS for dotlife-slider-split-carousel
*/

.js-transitions-disabled * {
  -webkit-transition: none !important;
  transition: none !important;
}
body.elementor-fullscreen .tg_split_carousel_slider_wrapper.carousel {
	height: 100vh !important;
}
.tg_split_carousel_slider_wrapper.carousel {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-perspective: 50vw;
          perspective: 50vw;
  -webkit-perspective-origin: 50% 50%;
          perspective-origin: 50% 50%;
}
.tg_split_carousel_slider_wrapper.carousel .carousel__control {
  position: absolute;
  height: 160px;
  width: 40px;
  background: #fff;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  z-index: 1;
}
.tg_split_carousel_slider_wrapper.carousel .carousel__control a {
  position: relative;
  display: block;
  width: 100%;
  padding-top: 75%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.tg_split_carousel_slider_wrapper.carousel .carousel__control a:first-child {
  margin-top: 15px;
}
.tg_split_carousel_slider_wrapper.carousel .carousel__control a:before {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 50%;
  padding-top: 25%;
  width: 25%;
  opacity: 0.5;
  content: '';
  display: block;
  margin-top: -12.5%;
}
.tg_split_carousel_slider_wrapper.carousel .carousel__control a.active:before {
	opacity: 1;
}
.tg_split_carousel_slider_wrapper.carousel .carousel__stage {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
  -webkit-transform: translateZ(-50vh);
          transform: translateZ(-50vh);
}
.tg_split_carousel_slider_wrapper.carousel .spinner {
  position: absolute;
  width: 50%;
  height: 100vh;
  top: 0;
  left: 0;
  right: auto;
  bottom: 0;
  margin: auto;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
  -webkit-transition: -webkit-transform 1s;
  transition: -webkit-transform 1s;
  transition: transform 1s;
  transition: transform 1s, -webkit-transform 1s;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-transform-origin: 50% 50%;
          transform-origin: 50% 50%;
  -webkit-transform: rotateX(0);
          transform: rotateX(0);
}
.tg_split_carousel_slider_wrapper.carousel .js-spin-fwd .spinner {
  -webkit-transform: rotateX(-90deg);
          transform: rotateX(-90deg);
}
.tg_split_carousel_slider_wrapper.carousel .js-spin-bwd .spinner {
  -webkit-transform: rotateX(90deg);
          transform: rotateX(90deg);
}
.tg_split_carousel_slider_wrapper.carousel .js-spin-fwd .spinner--right {
  -webkit-transform: rotateX(90deg);
          transform: rotateX(90deg);
}
.tg_split_carousel_slider_wrapper.carousel .js-spin-bwd .spinner--right {
  -webkit-transform: rotateX(-90deg);
          transform: rotateX(-90deg);
}
.tg_split_carousel_slider_wrapper.carousel .spinner--right {
  right: 0;
  left: auto;
}
.tg_split_carousel_slider_wrapper.carousel .spinner__face {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.tg_split_carousel_slider_wrapper.carousel .spinner__face.js-next {
  display: block;
  -webkit-transform: rotateX(90deg) translateZ(50vh);
          transform: rotateX(90deg) translateZ(50vh);
}
.tg_split_carousel_slider_wrapper.carousel .spinner--right .spinner__face.js-next {
  -webkit-transform: rotateX(270deg) translateZ(50vh);
          transform: rotateX(270deg) translateZ(50vh);
}
.tg_split_carousel_slider_wrapper.carousel .js-spin-bwd .spinner__face.js-next {
  -webkit-transform: rotateX(-90deg) translateZ(50vh);
          transform: rotateX(-90deg) translateZ(50vh);
}
.tg_split_carousel_slider_wrapper.carousel .js-spin-bwd .spinner--right .spinner__face.js-next {
  -webkit-transform: rotateX(-270deg) translateZ(50vh);
          transform: rotateX(-270deg) translateZ(50vh);
}
.tg_split_carousel_slider_wrapper.carousel .js-active {
  display: block;
  -webkit-transform: translateZ(50vh);
          transform: translateZ(50vh);
}
.tg_split_carousel_slider_wrapper.carousel .content {
  position: absolute;
  width: 200%;
  height: 100%;
  left: 0;
}
.tg_split_carousel_slider_wrapper.carousel .spinner--right .content {
  left: -100%;
}
.tg_split_carousel_slider_wrapper.carousel .content__left,
.tg_split_carousel_slider_wrapper.carousel .content__right {
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  height: 100%;
}
.tg_split_carousel_slider_wrapper.carousel .content__right {
  right: 0;
  left: auto;
}
.tg_split_carousel_slider_wrapper.carousel .content__left {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}
.tg_split_carousel_slider_wrapper.carousel .content__left:after {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.1);
}
.tg_split_carousel_slider_wrapper.carousel .content__left h1 {
  position: absolute;
  top: 50%;
  margin-top: -3vw;
  text-align: center;
  font-size: 5vw;
  height: 10vw;
  opacity: 1;
  color: #fff;
  width: 100%;
  letter-spacing: 0.15em;
  line-height: 0.6;
}
.tg_split_carousel_slider_wrapper.carousel .content__left span {
  font-size: 1vw;
  font-weight: 300;
  letter-spacing: 0.2em;
  opacity: 0.9;
}
.tg_split_carousel_slider_wrapper.carousel .content__right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.tg_split_carousel_slider_wrapper.carousel .content__right .content__main {
  position: absolute;
  text-align: left;
  padding: 0 8vw;
  margin: 0;
}
.tg_split_carousel_slider_wrapper.carousel .content__right .content__main p:last-child {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  font-size: 0.85em;
}
.tg_split_carousel_slider_wrapper.carousel .content__right .content__index {
  font-size: 30vh;
  position: absolute;
  right: -1vh;
  opacity: 0.04;
}

.tg_split_carousel_slider_wrapper.carousel .tg_split_carousel_slide_content_link
{
	font-size: 13px;
	letter-spacing: 2px;
	text-transform: uppercase;
	border-bottom: 1px solid #222;
	margin-top: 30px;
	cursor: pointer;
	display: inline-block;
	
	-webkit-transition: all 0.4s ease;
	transition: all 0.4s ease;
}

.tg_split_carousel_slider_wrapper.carousel .tg_split_carousel_slide_content_link:hover {
	border-bottom-color: transparent !important;
}

/*
	End CSS for dotlife-slider-split-carousel
*/


/*
	Begin CSS for dotlife-horizontal-timeline
*/

.cd-horizontal-timeline {
  opacity: 0;
  margin: 2em auto;
  -webkit-transition: opacity 0.2s;
  -moz-transition: opacity 0.2s;
  transition: opacity 0.2s;
}
.cd-horizontal-timeline::before {
  /* never visible - this is used in jQuery to check the current MQ */
  content: 'mobile';
  display: none;
}
.cd-horizontal-timeline.loaded {
  /* show the timeline after events position has been set (using JavaScript) */
  opacity: 1;
}
.cd-horizontal-timeline .timeline {
  position: relative;
  height: 100px;
  margin: 0 auto;
}
.cd-horizontal-timeline .events-wrapper {
  position: relative;
  height: 100%;
  margin: 0 40px;
  overflow: hidden;
}
.cd-horizontal-timeline .events-wrapper ol,
.cd-horizontal-timeline .events-content ol {
	margin-left: 0 !important;
	list-style: none;
}
.cd-horizontal-timeline .events-wrapper::after, .cd-horizontal-timeline .events-wrapper::before {
  /* these are used to create a shadow effect at the sides of the timeline */
  content: '';
  position: absolute;
  z-index: 2;
  top: 0;
  height: 100%;
  width: 20px;
}
.cd-horizontal-timeline .events {
  /* this is the grey line/timeline */
  position: absolute;
  z-index: 1;
  left: 0;
  top: 49px;
  height: 2px;
  /* width will be set using JavaScript */
  background: #dfdfdf;
  -webkit-transition: -webkit-transform 0.4s;
  -moz-transition: -moz-transform 0.4s;
  transition: transform 0.4s;
}
.cd-horizontal-timeline .filling-line {
  /* this is used to create the green line filling the timeline */
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #7b9d6f;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: left center;
  -moz-transform-origin: left center;
  -ms-transform-origin: left center;
  -o-transform-origin: left center;
  transform-origin: left center;
  -webkit-transition: -webkit-transform 0.3s;
  -moz-transition: -moz-transform 0.3s;
  transition: transform 0.3s;
}
.cd-horizontal-timeline .events a {
  position: absolute;
  bottom: 0;
  z-index: 2;
  text-align: center;
  font-size: 13px;
  padding-bottom: 15px;
  color: #383838;
  /* fix bug on Safari - text flickering while timeline translates */
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}
.cd-horizontal-timeline .events a::after {
  /* this is used to create the event spot */
  content: '';
  position: absolute;
  left: 50%;
  right: auto;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  bottom: -5px;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  border: 2px solid #dfdfdf;
  background-color: #f8f8f8;
  -webkit-transition: background-color 0.3s, border-color 0.3s;
  -moz-transition: background-color 0.3s, border-color 0.3s;
  transition: background-color 0.3s, border-color 0.3s;
}
.no-touch .cd-horizontal-timeline .events a:hover::after {
  background-color: #7b9d6f;
  border-color: #7b9d6f;
}
.cd-horizontal-timeline .events a.selected {
  pointer-events: none;
}
.cd-horizontal-timeline .events a.selected::after {
  background-color: #7b9d6f;
  border-color: #7b9d6f;
}
.cd-horizontal-timeline .events a.older-event::after {
  border-color: #7b9d6f;
}
@media only screen and (min-width: 1100px) {
  .cd-horizontal-timeline {
    margin: auto;
  }
  .cd-horizontal-timeline::before {
    /* never visible - this is used in jQuery to check the current MQ */
    content: 'desktop';
  }
}

ul.cd-timeline-navigation {
	margin-left: 0 !important;
	list-style: none;
}

.cd-timeline-navigation a {
  /* these are the left/right arrows to navigate the timeline */
  position: absolute;
  z-index: 1;
  top: 50%;
  bottom: auto;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  height: 34px;
  width: 34px;
  border-radius: 50%;
  border: 2px solid #e7e7e7;
  /* replace text with an icon */
  overflow: hidden;
  color: transparent;
  white-space: nowrap;
  -webkit-transition: border-color 0.3s;
  -moz-transition: border-color 0.3s;
  transition: border-color 0.3s;
}
.cd-timeline-navigation a::after {
  /* arrow icon */
  content: '';
  position: absolute;
  height: 16px;
  width: 16px;
  left: 50%;
  top: 50%;
  bottom: auto;
  right: auto;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  -o-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  
  font-size: 13px;
  line-height: 16px;
  margin-left: 2px;
  
}
.cd-timeline-navigation a.prev {
  left: 0;
  -webkit-transform: translateY(-50%) rotate(180deg);
  -moz-transform: translateY(-50%) rotate(180deg);
  -ms-transform: translateY(-50%) rotate(180deg);
  -o-transform: translateY(-50%) rotate(180deg);
  transform: translateY(-50%) rotate(180deg);
}
.cd-timeline-navigation a.prev:after {
	font-family: 'themify';
	content: "\e649";
	color: #e7e7e7;
}

.cd-timeline-navigation a.next:after {
	font-family: 'themify';
	content: "\e649";
	color: #e7e7e7;
}

.cd-timeline-navigation a.next {
  right: 0;
}
.no-touch .cd-timeline-navigation a:hover {
  border-color: #7b9d6f;
}
.cd-timeline-navigation a.inactive {
  cursor: not-allowed;
}
.cd-timeline-navigation a.inactive::after {
  background-position: 0 -16px;
}
.no-touch .cd-timeline-navigation a.inactive:hover {
  border-color: #dfdfdf;
}

.cd-horizontal-timeline .events-content {
  position: relative;
  width: 100%;
  margin: auto;
  margin-top: 10px;
  overflow: hidden;
  -webkit-transition: height 0.4s;
  -moz-transition: height 0.4s;
  transition: height 0.4s;
}
.cd-horizontal-timeline .events-content li {
  position: absolute;
  z-index: 1;
  width: 100%;
  left: 0;
  top: 0;
  -webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  padding: 0 5%;
  opacity: 0;
  -webkit-animation-duration: 0.4s;
  -moz-animation-duration: 0.4s;
  animation-duration: 0.4s;
  -webkit-animation-timing-function: ease-in-out;
  -moz-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
}
.cd-horizontal-timeline .events-content li.selected {
  /* visible event content */
  position: relative;
  z-index: 2;
  opacity: 1;
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}
.cd-horizontal-timeline .events-content li.enter-right, .cd-horizontal-timeline .events-content li.leave-right {
  -webkit-animation-name: cd-enter-right;
  -moz-animation-name: cd-enter-right;
  animation-name: cd-enter-right;
}
.cd-horizontal-timeline .events-content li.enter-left, .cd-horizontal-timeline .events-content li.leave-left {
  -webkit-animation-name: cd-enter-left;
  -moz-animation-name: cd-enter-left;
  animation-name: cd-enter-left;
}
.cd-horizontal-timeline .events-content li.leave-right, .cd-horizontal-timeline .events-content li.leave-left {
  -webkit-animation-direction: reverse;
  -moz-animation-direction: reverse;
  animation-direction: reverse;
}
.cd-horizontal-timeline .events-content li > * {
  margin: 0 auto;
}
.cd-horizontal-timeline .events-content h2 {
	font-size: 24px;
	font-weight: 400;
}
.cd-horizontal-timeline .events-content em {
  display: block;
  font-style: normal;
  margin: auto;
}

.cd-horizontal-timeline .events-content li .events-content-desc {
	margin-top: 10px;
}

@-webkit-keyframes cd-enter-right {
  0% {
    opacity: 0;
    -webkit-transform: translateX(100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0%);
  }
}
@-moz-keyframes cd-enter-right {
  0% {
    opacity: 0;
    -moz-transform: translateX(100%);
  }
  100% {
    opacity: 1;
    -moz-transform: translateX(0%);
  }
}
@keyframes cd-enter-right {
  0% {
    opacity: 0;
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
    -ms-transform: translateX(100%);
    -o-transform: translateX(100%);
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0%);
    -moz-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -o-transform: translateX(0%);
    transform: translateX(0%);
  }
}
@-webkit-keyframes cd-enter-left {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0%);
  }
}
@-moz-keyframes cd-enter-left {
  0% {
    opacity: 0;
    -moz-transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    -moz-transform: translateX(0%);
  }
}
@keyframes cd-enter-left {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100%);
    -moz-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
    -o-transform: translateX(-100%);
    transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0%);
    -moz-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -o-transform: translateX(0%);
    transform: translateX(0%);
  }
}

/*
	End CSS for dotlife-horizontal-timeline
*/


/*
	Begin CSS for dotlife-portfolio-classic
*/

.portfolio_classic_grid_wrapper .portfolio_classic_img {
  color: #fff;
  position: relative;
  float: left;
  overflow: hidden;
  text-align: center;
  line-height: 0;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img img {
  opacity: 1;
  width: 100%;
  -webkit-transition: opacity 0.35s;
  transition: opacity 0.35s;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img > div {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img > div::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  content: '';
  opacity: 0;
  -webkit-transition: opacity 0.4s;
  transition: opacity 0.4s;
  background-image: linear-gradient(-45deg, #000000 0%, transparent 40%, rgba(255, 255, 255, 0.6));
}
.portfolio_classic_grid_wrapper .portfolio_classic_img span.ti-arrow-right {
  display: inline-block;
  font-size: 24px;
  color: #ffffff;
  padding: 6px 16px;
  position: absolute;
  bottom: 10px;
  right: 0px;
  opacity: 0;
  z-index: 1;
  -webkit-transition: 0.05s linear;
  transition: 0.05s linear;
  -webkit-transition-delay: 0.01s;
  transition-delay: 0.01s;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img .curl {
  width: 0px;
  height: 0px;
  position: absolute;
  bottom: 0;
  right: 0;
  background: linear-gradient(135deg, #ffffff, #f3f3f3 20%, #bbbbbb 38%, #aaaaaa 44%, #888888 50%, rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0.4) 60%, rgba(0, 0, 0, 0.3));
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  transition: all .4s ease;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img .curl:before,
.portfolio_classic_grid_wrapper .portfolio_classic_img .curl:after {
  content: '';
  position: absolute;
  z-index: -1;
  left: 12%;
  bottom: 6%;
  width: 70%;
  max-width: 300px;
  max-height: 100px;
  height: 55%;
  box-shadow: 0 12px 15px rgba(0, 0, 0, 0.3);
  transform: skew(-10deg) rotate(-6deg);
}
.portfolio_classic_grid_wrapper .portfolio_classic_img .curl:after {
  left: auto;
  right: 6%;
  bottom: auto;
  top: 14%;
  transform: skew(-15deg) rotate(-84deg);
}
.portfolio_classic_grid_wrapper .portfolio_classic_img a {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: absolute;
  color: #ffffff;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img:hover > div::before,
.portfolio_classic_grid_wrapper .portfolio_classic_img.hover > div::before {
  opacity: 1;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img:hover span.ti-arrow-right,
.portfolio_classic_grid_wrapper .portfolio_classic_img.hover span.ti-arrow-right {
  opacity: 1;
  -webkit-transition-delay: 0.15s;
  transition-delay: 0.15s;
}
.portfolio_classic_grid_wrapper .portfolio_classic_img:hover .curl,
.portfolio_classic_grid_wrapper .portfolio_classic_img.hover .curl {
  width: 90px;
  height: 90px;
}

.portfolio_classic_content
{
	clear: both;
	padding: 30px;
}

.portfolio_classic_grid_wrapper
{
	margin-bottom: 4%;
}

/*
	End CSS for dotlife-portfolio-classic
*/


/*
	Begin CSS for dotlife-background-list
*/

.tg_background_list_wrapper
{
	position: relative;
	width: 100%;
	overflow: hidden;
	display: flex;
	background: #000;
}

.tg_background_list_column
{
	min-height: 50vh;
}

.tg_background_list_wrapper .tg_background_list_column
{
	position: relative;
    z-index: 2;
	border-right: 1px solid rgba(256,256,256,0.5);
}

.tg_background_list_wrapper .tg_background_list_column.last
{
	border-right: 0;
}

.tg_background_list_wrapper.one_col .tg_background_list_column
{
	-webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
}

.tg_background_list_wrapper.two_cols .tg_background_list_column
{
	-webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}

.tg_background_list_wrapper.three_cols .tg_background_list_column
{
	-webkit-box-flex: 0;
    -ms-flex: 0 0 33.33%;
    flex: 0 0 33.33%;
    max-width: 33.33%;
}

.tg_background_list_wrapper.four_cols .tg_background_list_column
{
	-webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
}

.tg_background_list_wrapper .tg_background_list_column .tg_background_list_content
{
	display: flex;
    height: 100%;
    flex-direction: column;
    justify-content: flex-end;
    padding: 40px;
    color: #fff;
}

.tg_background_list_wrapper .tg_background_list_column .tg_background_list_content h3
{
	color: #fff;
}

.tg_background_list_wrapper .tg_background_img, 
.tg_background_list_wrapper .tg_background_list_overlay
{
	position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 0;
    overflow: hidden;
}

.tg_background_list_wrapper .tg_background_img
{
	visibility: hidden;
    opacity: 0;
    
	transition: all 1s;
}

.tg_background_list_wrapper .tg_background_img.hover
{
	opacity: 1;
	visibility: visible;
	
	-ms-transform: scale(1.05);
    -moz-transform: scale(1.05);
    -o-transform: scale(1.05);
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
}

.tg_background_list_wrapper .tg_background_list_overlay
{
	z-index: 1;
	background: rgba(0,0,0,0.3);
}

.tg_background_list_wrapper .tg_background_img img
{
	width: 100% !important;
    height: 100% !important;
    object-fit: cover;
}

.tg_background_list_wrapper .tg_background_list_column:hover .tg_background_list_link
{
	max-height: 100%;
    transform: scaleY(1);
    overflow: visible;
}

.tg_background_list_wrapper .tg_background_list_content .tg_background_list_link
{
    overflow: hidden;
    transform: scaleY(0);
    transform-origin: bottom;
    transition: transform 0.2s ease;
    max-height: 0;
}

.tg_background_list_wrapper .tg_background_list_content .tg_background_list_title
{
	transition: transform 0.3s ease;
	-moz-transform: translateY(10px);
    -ms-transform: translateY(10px);
    -webkit-transform: translateY(10px);
    transform: translateY(10px);
}

.tg_background_list_wrapper .tg_background_list_column:hover .tg_background_list_title
{
	-moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
}

.tg_background_list_wrapper .tg_background_list_content .tg_background_list_link .button
{
	background: transparent;
	border: 1px solid #fff;
	color: #fff;
	margin-top: 20px;
}

/*
	End CSS for dotlife-background-list
*/


/*
	Begin CSS for dotlife-portfolio-grid
*/

.portfolio_grid_wrapper {
  background-color: #000;
  color: #fff;
  display: inline-block;
  overflow: hidden;
  position: relative;
  text-align: center;
}

.portfolio_grid_wrapper * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.45s ease;
  transition: all 0.45s ease;
}

.portfolio_grid_wrapper:before,
.portfolio_grid_wrapper:after {
  background-color: rgba(0, 0, 0, 0.1);
  border-top: 50px solid rgba(256, 256, 256, 1);
  border-bottom: 50px solid rgba(256, 256, 256, 1);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  z-index: 1;
  opacity: 0;
}

.portfolio_grid_wrapper:before {
  -webkit-transform: scaleY(1.5);
  transform: scaleY(1.5);
}

.portfolio_grid_wrapper:after {
  -webkit-transform: scaleY(1.5);
  transform: scaleY(1.5);
}

.portfolio_grid_wrapper img {
  vertical-align: top;
  max-width: 100%;
  backface-visibility: hidden;
}

.portfolio_grid_wrapper figcaption {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  align-items: center;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  line-height: 1.1em;
  opacity: 0;
  z-index: 2;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
  color: #fff;
  padding: 0 20px 0 20px;
  box-sizing: border-box;
}

.portfolio_grid_wrapper h3 {
  margin: 0;
  color: #fff;
}

.portfolio_grid_wrapper h3 span {
  display: block;
  font-weight: 700;
}

.portfolio_grid_wrapper a {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3;
}

.portfolio_grid_wrapper:hover > img,
.portfolio_grid_wrapper.hover > img {
  opacity: 0.7;
}

.portfolio_grid_wrapper:hover:before,
.portfolio_grid_wrapper.hover:before,
.portfolio_grid_wrapper:hover:after,
.portfolio_grid_wrapper.hover:after {
  -webkit-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}

.portfolio_grid_wrapper:hover figcaption,
.portfolio_grid_wrapper.hover figcaption {
  opacity: 1;
  -webkit-transition-delay: 0.1s;
  transition-delay: 0.1s;
}

.portfolio_grid_wrapper.tg_two_cols,
.portfolio_grid_wrapper.tg_three_cols
{
	margin-bottom: 4%;
}

.portfolio_grid_wrapper.tg_four_cols,
.portfolio_grid_wrapper.tg_five_cols
{
	margin-bottom: 3%;
}

.portfolio_grid_wrapper figcaption .portfolio_grid_subtitle
{
	margin-top: 5px;
}

/*
	End CSS for dotlife-portfolio-grid
*/


/*
	Begin CSS for dotlife-portfolio-masonry
*/

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper .tg_two_cols
{
	width: calc(50% - 35px);
	margin-bottom: 140px;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper .tg_two_cols.stellar
{
	margin-bottom: 200px;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper .tg_two_cols.last
{
	width: calc(50% - 35px);
	margin-right: 0;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper.do_masonry .tg_two_cols
{
	margin-right: 0;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper .tg_three_cols
{
	width: calc(33.33% - 40px);
	margin-bottom: 120px;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper.do_masonry .tg_three_cols
{
	margin-right: 0;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper .tg_three_cols.stellar
{
	margin-bottom: 200px;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper .tg_four_cols
{
	width: calc(25% - 38px);
	margin-bottom: 120px;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper.do_masonry .tg_four_cols
{
	margin-right: 0;
}

.portfolio_masonry_content_wrapper.gallery_grid_content_wrapper .tg_four_cols.stellar
{
	margin-bottom: 180px;
}

.portfolio_masonry_grid_wrapper {
  position: relative;
  float: left;
  box-shadow: none !important;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: visible !important;
}
.portfolio_masonry_grid_wrapper * {
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.portfolio_masonry_grid_wrapper img {
  max-width: 100%;
  vertical-align: top;
}
.portfolio_masonry_grid_wrapper .border {
  position: absolute;
  opacity: 1;
  z-index: 1;
}
.portfolio_masonry_grid_wrapper .border:before,
.portfolio_masonry_grid_wrapper .border:after,
.portfolio_masonry_grid_wrapper .border div:before,
.portfolio_masonry_grid_wrapper .border div:after {
  background-color: #000;
  position: absolute;
  content: "";
  display: block;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}
.portfolio_masonry_grid_wrapper .border:before,
.portfolio_masonry_grid_wrapper .border:after {
  width: 0;
  height: 1px;
}
.portfolio_masonry_grid_wrapper .border div:before,
.portfolio_masonry_grid_wrapper .border div:after {
  width: 1px;
  height: 0;
}
.portfolio_masonry_grid_wrapper .border.one {
  left: -6px;
  top: -6px;
  right: 6px;
  bottom: 6px;
}
.portfolio_masonry_grid_wrapper .border.one:before,
.portfolio_masonry_grid_wrapper .border.one div:before {
  left: 0;
  top: 0;
}
.portfolio_masonry_grid_wrapper .border.one:after,
.portfolio_masonry_grid_wrapper .border.one div:after {
  bottom: 0;
  right: 0;
}
.portfolio_masonry_grid_wrapper .border.two {
  left: 6px;
  top: 6px;
  right: -6px;
  bottom: -6px;
}
.portfolio_masonry_grid_wrapper .border.two:before,
.portfolio_masonry_grid_wrapper .border.two div:before {
  right: 0;
  top: 0;
}
.portfolio_masonry_grid_wrapper .border.two:after,
.portfolio_masonry_grid_wrapper .border.two div:after {
  bottom: 0;
  left: 0;
}
.portfolio_masonry_grid_wrapper figcaption {
  left: 15%;
  bottom: -60px;
  position: absolute;
  padding: 25px;
  margin: 6px;
  background-color: #fff;
  width: 70%;
  -webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.1);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.1);
}

.portfolio_masonry_grid_wrapper.tg_three_cols figcaption {
	width: 80%;
	left: 10%;
}

.portfolio_masonry_grid_wrapper.tg_four_cols figcaption {
	width: 80%;
	left: 10%;
}

.portfolio_masonry_grid_wrapper h3 {
  margin: 0;
}
.portfolio_masonry_grid_wrapper h3 {
  margin-bottom: 5px;
}
.portfolio_masonry_grid_wrapper a {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: absolute;
  z-index: 1;
}
.portfolio_masonry_grid_wrapper:hover .border:before,
.portfolio_masonry_grid_wrapper.hover .border:before,
.portfolio_masonry_grid_wrapper:hover .border:after,
.portfolio_masonry_grid_wrapper.hover .border:after {
  width: 100%;
}
.portfolio_masonry_grid_wrapper:hover .border div:before,
.portfolio_masonry_grid_wrapper.hover .border div:before,
.portfolio_masonry_grid_wrapper:hover .border div:after,
.portfolio_masonry_grid_wrapper.hover .border div:after {
  height: 100%;
}

/*
	End CSS for dotlife-portfolio-masonry
*/


/*
	Begin CSS for dotlife-portfolio-timeline
*/

.tg_portfolio_timeline_wrapper .portfolio_timeline_content_wrapper
{
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;

	-ms-flex-align: center;
	-webkit-align-items: center;
	-webkit-box-align: center;

	align-items: center;
}

.tg_portfolio_timeline_wrapper .portfolio_timeline_img
{
	float: left;
	width: 66.66%;
}

.tg_portfolio_timeline_wrapper .portfolio_timeline_content
{
	float: right;
	width: calc(33.33% - 60px);
	margin-left: 60px;
}

.tg_portfolio_timeline_wrapper .portfolio_timeline_content .portfolio_timeline_link
{
	margin-top: 20px;
	display: block;
}

/*
	End CSS for dotlife-portfolio-timeline
*/


/*
	Begin CSS for dotlife-portfolio-timeline-vertical
*/

.portfolio_timeline_vertical_content_wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  flex-direction: column;
}

.portfolio_timeline_vertical_content_wrapper .timeline {
  width: 100%;
  background-color: #fff;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-container {
  height: 800px;
  width: 100%;
  position: relative;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-wrapper {
  transition: 2s cubic-bezier(0.68, -0.4, 0.27, 1.34) 0.2s;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide {
  position: relative;
  color: #fff;
  overflow: hidden;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide::after {
  content: "";
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide-content {
  position: absolute;
  text-align: center;
  width: 80%;
  max-width: 310px;
  right: 50%;
  top: 13%;
  -webkit-transform: translate(50%, 0);
          transform: translate(50%, 0);
  z-index: 2;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide .timeline-year {
  display: block;
  margin-bottom: 10px;
  -webkit-transform: translate3d(20px, 0, 0);
          transform: translate3d(20px, 0, 0);
  color: #fff;
  opacity: 0;
  transition: .2s ease .4s;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide .timeline-title {
  font-size: 34px;
  margin: 0;
  opacity: 0;
  color: #fff;
  -webkit-transform: translate3d(20px, 0, 0);
          transform: translate3d(20px, 0, 0);
  transition: .2s ease .5s;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide .timeline-text {
  line-height: 1.5;
  opacity: 0;
  -webkit-transform: translate3d(20px, 0, 0);
          transform: translate3d(20px, 0, 0);
  transition: .2s ease .6s;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide-active .timeline-year {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  transition: .4s ease 1.6s;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide-active .timeline-title {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  transition: .4s ease 1.7s;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide-active .timeline-text {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  transition: .4s ease 1.8s;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-pagination {
  right: 10% !important;
  height: 100%;
  left: auto !important;
  width: auto !important;
  display: none;
  flex-direction: column;
  justify-content: center;
  z-index: 1;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-pagination::before {
  content: "";
  position: absolute;
  left: -30px;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-pagination-bullet {
  width: auto;
  height: auto;
  text-align: left;
  opacity: 0.6;
  background: transparent;
  color: #fff;
  margin: 15px 0 !important;
  position: relative;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-pagination-bullet::before {
  content: "";
  position: absolute;
  top: 5px;
  left: -37px;
  width: 16px;
  height: 16px;
  border-radius: 100%;
  background-color: #fff;
  -webkit-transform: scale(0);
          transform: scale(0);
  transition: .2s;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-pagination-bullet-active {
  opacity: 1;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-pagination-bullet-active::before {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-next,
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-prev {
  background-size: 20px 20px;
  top: 15%;
  width: 20px;
  height: 20px;
  margin-top: 0;
  z-index: 2;
  transition: .2s;
  color: #fff;font-size: 26px;
  
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-prev {
  left: 8%;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-prev:hover {
  -webkit-transform: translateX(-3px);
          transform: translateX(-3px);
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-next {
  right: 8%;
}
.portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-next:hover {
  -webkit-transform: translateX(3px);
          transform: translateX(3px);
}
.portfolio_timeline_vertical_content_wrapper .timeline .portfolio_timeline_vertical_link {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 3;
    display: block;
}

.portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide-content {
    right: 28%;
}
   
@media screen and (min-width: 768px) {
  .portfolio_timeline_vertical_content_wrapper .timeline .swiper-slide-content {
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
    width: 310px;
    text-align: right;
  }
  .portfolio_timeline_vertical_content_wrapper .timeline .swiper-pagination {
    display: flex;
  }
  .portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-prev {
    top: 8%;
    left: auto;
    right: 17%;
  }
  .portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-prev:hover {
    -webkit-transform: translate(0px, 10px);
            transform: translate(0px, 10px);
  }
  .portfolio_timeline_vertical_content_wrapper .timeline .swiper-button-next {
    top: auto;
    bottom: 10%;
    right: 17%;
  }
  .portfolio_timeline_vertical_content_wrapper  .timeline .swiper-button-next:hover {
    -webkit-transform: translate(0px, 10px);
            transform: translate(0px, 10px);
  }
}

/*
	End CSS for dotlife-portfolio-timeline-vertical
*/


/*
	Begin CSS for dotlife-slider-parallax
*/

.slider_parallax_wrapper {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100vh;
  z-index: 1;
}
.slider_parallax_wrapper .slideshow-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.slider_parallax_wrapper .slides {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.slider_parallax_wrapper .slide {
  display: none;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.slider_parallax_wrapper .slide.is-active {
  display: block;
}
.slider_parallax_wrapper .slide.is-loaded {
  opacity: 1;
}
.slider_parallax_wrapper .slide .image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  z-index: 1;
  background-size: cover;
  image-rendering: optimizeQuality;
}
.slider_parallax_wrapper .slide .image-container::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}
.slider_parallax_wrapper .slide .image {
  width: 100% !important;
  max-width: none !important;
  object-fit: cover;
  height: 100% !important;
}
.slider_parallax_wrapper .slide-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  color: #fff;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.slider_parallax_wrapper .slide .title {
  margin: 0 auto 15px;
}
.slider_parallax_wrapper .slide .title h2 {
	color: #fff;
}
.slider_parallax_wrapper .slide .text {
  margin: 0 auto;
  font-size: 18px;
}
.slider_parallax_wrapper .slide .button {
  margin: 30px 0 0;
}
.slider_parallax_wrapper .pagination {
  position: absolute;
  bottom: 35px;
  left: 0;
  width: 100%;
  height: 12px;
  cursor: default;
  z-index: 2;
  text-align: center;
}
.slider_parallax_wrapper .pagination .item {
  display: inline-block;
  padding: 15px 5px;
  position: relative;
  width: 46px;
  height: 32px;
  cursor: pointer;
  text-indent: -999em;
  z-index: 1;
}
.slider_parallax_wrapper .pagination .item + .page {
  margin-left: -2px;
}
.slider_parallax_wrapper .pagination .item::before {
  content: "";
  display: block;
  position: absolute;
  top: 15px;
  left: 5px;
  width: 36px;
  height: 2px;
  opacity: 0.3;
  transition: background 0.2s ease;
}
.slider_parallax_wrapper .pagination .item::after {
  width: 0;
  background: #fff;
  z-index: 2;
  transition: width 0.2s ease;
}
.slider_parallax_wrapper .pagination .item:hover::before, .slider_parallax_wrapper .pagination .item.is-active::before {
  opacity: 1;
}
.slider_parallax_wrapper .arrows .arrow {
  margin: -33px 0 0;
  padding: 20px;
  position: absolute;
  top: 50%;
  cursor: pointer;
  z-index: 3;
}
.slider_parallax_wrapper .arrows .prev {
  left: 30px;
}
.slider_parallax_wrapper .arrows .prev:hover .svg {
  left: -10px;
}
.slider_parallax_wrapper .arrows .next {
  right: 30px;
}
.slider_parallax_wrapper .arrows .next:hover .svg {
  left: 10px;
}
.slider_parallax_wrapper .arrows .svg {
  position: relative;
  left: 0;
  width: 14px;
  height: 26px;
  fill: #fff;
  transition: left 0.2s ease;
}

/*
	End CSS for dotlife-slider-parallax
*/


/*
	Begin CSS for dotlife-distortion-grid
*/

.distortion_grid_item {
	position: relative;
	display: flex;
	align-items: stretch;
	width: 50vw;
}

.distortion_grid_item--bg {
	background-color: var(--item-bg);
	height: 50vw;
}

.distortion_grid_item-content {
	width: 100%;
	padding: 5vw;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	pointer-events: none;
	background: var(--item-bg);
}

.distortion_grid_item--bg .distortion_grid_item-content {
	position: absolute;
	height: 100%;
	width: 100%;
	top: 0;
	left: 0;
}

.distortion_grid_item-img {
	display: block;
	height: 100%;
	width: 100%;
}

.distortion_grid_item-img canvas {
	height: 100%;
}

.distortion_grid_item-img img {
	height: 100%;
	display: block;
}

.distortion_grid_item-img img:nth-child(2) {
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
	transition: opacity 0.3s;
}

.distortion_grid_item:hover .distortion_grid_item-img img:nth-child(2) {
	opacity: 1;
}

body.elementor-default .distortion_grid_item-img img {
	display: none;
}

.distortion_grid_item-title {
	font-size: 2.5rem;
	line-height: 1;
	margin: 0;
	color: var(--item-title);
}

.distortion_grid_item-title--small {
	font-size: 1.5rem;
	line-height: 1.25;
	margin-bottom: 1rem;
}

.distortion_grid_item-meta {
	text-transform: uppercase;
	font-weight: 400;
	letter-spacing: 0.25rem;
	font-size: 0.95rem;
	color: var(--item-meta);
}

.distortion_grid_item-subtitle {
	display: block;
	margin: auto 0 0 0;
	font-weight: 400;
	font-size: 1.5em;
	color: var(--item-subtitle);
}

.distortion_grid_item-subtitle span {
	display: block;
}

.distortion_grid_item-text {
	font-size: 1rem;
	line-height: 1.75;
	margin: 2rem 0 0 0;
	color: var(--item-text);
}

.distortion_grid_item-link {
	display: inline-block;
	color: currentColor;
	font-weight: 700;
	font-size: 0.95rem;
	position: relative;
	padding: 0 0 0.2rem;
	pointer-events: auto;
	margin-top: 30px;
	color: var(--item-link);
}

.distortion_grid_item-link:focus,
.distortion_grid_item-link:hover {
	color: var(--item-link-hover);
}

.distortion_grid_item-link::before {
	content: '';
	position: absolute;
	background: currentColor;
	width: 80%;
	height: 1px;
	bottom: 0;
	transform-origin: 0% 50%;
	transform: scale3d(0,1,1);
	opacity: 0;
	transition: all 0.3s;
	transition-property: opacity, transform;
}

.distortion_grid_item-link:hover::before {
	opacity: 1;
	transform: scale3d(1,1,1);
}

.distortion_grid_item-link + .distortion_grid_item-link {
	margin: 0 0 0 3rem;
}

.distortion_grid_item-text + .distortion_grid_item-link {
	white-space: nowrap;
	margin-top: 1rem;
	align-self: flex-start;
	margin-top: auto;
}

.distortion_grid_item-nav {
	display: flex;
}

/* Themes */
.theme-2 {
	--item-bg: rgba(0,0,0,0.2);
	--item-link: #fff;
	--item-link-hover: #fff;
	--item-meta: #fff;
	--item-title: #fff;
	--item-subtitle: #fff;
	--item-text: #fff;
}

.theme-3 {
	--item-bg: rgba(0,0,0,0.2);
	--item-link: #fff;
	--item-link-hover: #fff;
	--item-meta: #fff;
	--item-title: #fff;
	--item-subtitle: #fff;
	--item-text: #fff;
}

.theme-6 {
	--item-bg: rgba(0,0,0,0.2);
    --item-link: #12161e;
    --item-link-hover: #fff;
    --item-meta: #fff;
    --item-title: #ffffff;
    --item-subtitle: #12161e;
    --item-text: #ffffff;
}

.theme-7 {
	--item-bg: rgba(0,0,0,0.2);
    --item-link: #fff;
    --item-link-hover: #fff;
    --item-meta: #fff;
    --item-title: #fff;
    --item-subtitle: #fff;
    --item-text: #fff;
}

.theme-10 {
	--item-bg: rgba(0,0,0,0.2);
    --item-link: #000;
    --item-link-hover: #fff;
    --item-meta: #fff;
    --item-title: #fff;
    --item-subtitle: #000;
    --item-text: #fff;
}

.theme-11 {
	--item-bg: rgba(0,0,0,0.2);
    --item-link: #000;
    --item-link-hover: #fff;
    --item-meta: #000;
    --item-title: #000;
    --item-subtitle: #000;
    --item-text: #000;
}

.theme-14 {
	--item-bg: rgba(0,0,0,0.2);
    --item-link: #fff;
    --item-link-hover: #fff;
    --item-meta: #fff;
    --item-title: #fff;
    --item-subtitle: #fff;
    --item-text: #fff;
}

.theme-15 {
	--item-bg: rgba(0,0,0,0.2);
    --item-link: #fff;
    --item-link-hover: #fff;
    --item-meta: #fff;
    --item-title: #fff;
    --item-subtitle: #fff;
    --item-text: #fff;
}

.theme-19 {
	--item-bg: rgba(0,0,0,0.2);
    --item-link: #6317f5;
    --item-link-hover: #fff;
    --item-meta: #6317f5;
    --item-title: #fff;
    --item-subtitle: #6317f5;
    --item-text: #fff;
}

@media screen and (min-width: 85em) {
	.distortion_grid_item-text {
		width: 80%;
	}
}

.distortion_grid_wrapper {
		display: grid;
		grid-template-columns: repeat(2,49.999vmax);
	}

@media only screen and (max-width: 960px) {
	.distortion_grid_wrapper {
		display: grid;
		grid-template-columns: repeat(2,49.999vmax);
	}
	.distortion_grid_item {
		height: 49.999vmax;
		width: 100%;
	}
	.distortion_grid_item-subtitle * {
		opacity: 0;
		transform: translate3d(100px, 0, 0);
		transition: all 0.5s cubic-bezier(0.2, 1, 0.7, 1);
		transition-property: transform, opacity;
	}
	.distortion_grid_item:hover .distortion_grid_item-subtitle * {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
	.distortion_grid_item:hover .distortion_grid_item-subtitle span {
		transition-delay: 0.1s;
	}
	.distortion_grid_item:hover .distortion_grid_item-subtitle .grid__item-link {
		transition-delay: 0s;
	}
	.distortion_grid_item-title {
		font-size: 6vw;
		font-weight: 400;
	}
	.distortion_grid_item-title--small {
		font-size: 3vw;
	}
	.distortion_grid_item-text {
		margin-top: 2.5rem;
	}
}

/*
	End CSS for dotlife-distortion-grid
*/


/*
	Begin CSS for dotlife-animated-slider
*/

.tg_animated_slider_wrapper .pagination {
  position: absolute !important;
  width: 100%;
  text-align: center;
  right: 0;
  padding: 0 !important;
  bottom: 30px;
  margin: 0;
  z-index: 999;
}

.tg_animated_slider_wrapper .pagination__item {
  cursor: pointer;
  display: inline-block;
  white-space: nowrap;
  font-size: 0;
  width: 10px;
  height: 10px;
  border: 1px solid #fff;
  background: transparent;
  margin: 0 5px;
  border-radius: 250px;
  transition: .2s ease-in-out;
}
.tg_animated_slider_wrapper .pagination__item.is-current, 
.tg_animated_slider_wrapper .pagination__item:hover {
  background-color: #fff;
}

.tg_animated_slider_wrapper .pagination .container {
  position: relative;
  margin: 0 auto;
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper .pagination .container {
    padding-right: 40px;
    padding-left: 40px;
  }
}
@media (min-width: 700px) and (max-width: 1599px) {
  .tg_animated_slider_wrapper .pagination .container {
    padding-right: 7.5rem;
    padding-left: 7.5rem;
    max-width: 140rem;
  }
}
@media (min-width: 1600px) {
  .tg_animated_slider_wrapper .pagination .container {
    padding-right: 9.5625rem;
    padding-left: 9.5625rem;
    max-width: 144.125rem;
  }
}
.tg_animated_slider_wrapper .background-absolute {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-position: center;
  background-size: cover;
}

.tg_animated_slider_wrapper.slideshow {
  position: relative;
  color: #fff;
  overflow: hidden;
  height: 700px;
}

.tg_animated_slider_wrapper .slideshow__slide {
  visibility: hidden;
  transition: visibility 0s 1.7s;
}

.tg_animated_slider_wrapper .slideshow__slide.is-current {
  visibility: visible;
  transition-delay: 0s;
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper.slideshow .slideshow__slide {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
@media (min-width: 700px) {
  .tg_animated_slider_wrapper.slideshow .slideshow__slide {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
.tg_animated_slider_wrapper .slideshow__slide-background-load-wrap {
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate3d(0, 100%, 0);
  overflow: hidden;
}

body.is-animated .tg_animated_slider_wrapper .slideshow__slide-background-load-wrap {
  transform: translate3d(0, 0, 0);
  transition-delay: 0s;
}

.tg_animated_slider_wrapper .slideshow__slide.is-prev .slideshow__slide-background-parallax, 
.tg_animated_slider_wrapper .slideshow__slide.is-next .slideshow__slide-background-parallax, 
.tg_animated_slider_wrapper .slideshow__slide.is-prev-section .slideshow__slide-background-parallax, 
.tg_animated_slider_wrapper .slideshow__slide.is-next-section .slideshow__slide-background-parallax {
  transform: none !important;
}

.tg_animated_slider_wrapper .slideshow__slide-background-load {
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate3d(0, -50%, 0);
}

body.is-animated .tg_animated_slider_wrapper .slideshow__slide-background-load {
  transform: translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper .slideshow__slide-background-wrap {
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.5s;
  transform: translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper .slideshow__slide.is-prev .slideshow__slide-background-wrap {
  transform: translate3d(0, -100%, 0);
}
.tg_animated_slider_wrapper .slideshow__slide.is-next .slideshow__slide-background-wrap {
  transform: translate3d(0, 100%, 0);
}
.tg_animated_slider_wrapper .slideshow__slide.is-prev-section .slideshow__slide-background-wrap {
  transform: translate3d(0, -100%, 0);
  transition: none;
}
.tg_animated_slider_wrapper .slideshow__slide.is-next-section .slideshow__slide-background-wrap {
  transform: translate3d(0, 100%, 0);
  transition: none;
}

.tg_animated_slider_wrapper .slideshow__slide-background {
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s;
  transform: scale(1);
  overflow: hidden;
}

.tg_animated_slider_wrapper .slideshow__slide.is-prev .slideshow__slide-background, 
.tg_animated_slider_wrapper .slideshow__slide.is-next .slideshow__slide-background {
  transform: scale(0.5);
  transition-delay: 0s;
}
.tg_animated_slider_wrapper .slideshow__slide.is-prev-section .slideshow__slide-background, 
.tg_animated_slider_wrapper .slideshow__slide.is-next-section .slideshow__slide-background {
  transform: scale(0.5);
  transition-delay: 0s;
  transition: none;
}

.tg_animated_slider_wrapper .slideshow__slide-image-wrap {
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
  transform: translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper .slideshow__slide.is-prev .slideshow__slide-image-wrap {
  transform: translate3d(0, 50%, 0);
}

.tg_animated_slider_wrapper .slideshow__slide-image {
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s;
  transform: scale(1);
}

.tg_animated_slider_wrapper .slideshow__slide.is-prev .slideshow__slide-image, 
.tg_animated_slider_wrapper .slideshow__slide.is-next .slideshow__slide-image {
  transform: scale(1.25);
  transition-delay: 0s;
}
.tg_animated_slider_wrapper .slideshow__slide.is-prev-section .slideshow__slide-image, 
.tg_animated_slider_wrapper .slideshow__slide.is-next-section .slideshow__slide-image {
  transform: scale(1.25);
  transition-delay: 0s;
  transition: none;
}

.tg_animated_slider_wrapper .slideshow__slide-image::before, 
.tg_animated_slider_wrapper .slideshow__slide-image::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0.35;
}

.tg_animated_slider_wrapper .slideshow__slide.is-prev .slideshow_container, 
.tg_animated_slider_wrapper .slideshow__slide.is-next .slideshow_container, 
.tg_animated_slider_wrapper .slideshow__slide.is-prev-section .slideshow_container, 
.tg_animated_slider_wrapper .slideshow__slide.is-next-section .slideshow_container {
  transform: none !important;
}

.tg_animated_slider_wrapper .slideshow__slide-caption-text {
  position: relative;
  height: 100%;
  padding-top: 10%;
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transform: translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper .slideshow__slide.is-prev .slideshow__slide-caption-text {
  transform: translate3d(-100%, -50%, 0);
}
.tg_animated_slider_wrapper .slideshow__slide.is-next .slideshow__slide-caption-text {
  transform: translate3d(-100%, 10%, 0);
}
.tg_animated_slider_wrapper .slideshow__slide.is-prev-section .slideshow__slide-caption-text {
  transform: translate3d(-100%, -50%, 0);
  transition: none;
}
.tg_animated_slider_wrapper .slideshow__slide.is-next-section .slideshow__slide-caption-text {
  transform: translate3d(-100%, 10%, 0);
  transition: none;
}

.tg_animated_slider_wrapper .slideshow__slide-caption {
  position: relative;
  height: 100%;
  transform: translate3d(-100%, 10%, 0);
  transition: transform 1s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
}

body.is-animated .tg_animated_slider_wrapper .slideshow__slide-caption {
  transform: translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper .slideshow__slide-caption-title {
  line-height: 1;
}
.tg_animated_slider_wrapper .slideshow__slide-caption-title.-full {
  width: 100%;
}

.tg_animated_slider_wrapper .slideshow__slide-caption-subtitle {
  display: inline-block;
  padding: 1.875rem 0;
}
.tg_animated_slider_wrapper .slideshow__slide-caption-subtitle.-load {
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transform: translate3d(0, 3.75rem, 0);
}

body.is-animated .tg_animated_slider_wrapper .slideshow__slide-caption-subtitle.-load {
  transform: translate3d(0, 0, 0);
}

body[data-route-option="prev-section"] .tg_animated_slider_wrapper .slideshow__slide-caption-subtitle.-load, 
body[data-route-option="next-section"] .tg_animated_slider_wrapper .slideshow__slide-caption-subtitle.-load {
  transform: translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper .slideshow__slide-caption-subtitle-label {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  display: inline-block;
}

.tg_animated_slider_wrapper .o-hsub.-link:hover .slideshow__slide-caption-subtitle-label,
.tg_animated_slider_wrapper .o-hsub-wrap:hover .slideshow__slide-caption-subtitle-label {
  transform: translateX(20px);
}

.tg_animated_slider_wrapper .c-header-home_subheading {
  display: inline-block;
  padding: 1.875rem 0;
}
.tg_animated_slider_wrapper .c-header-home_subheading.-load {
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transform: translate3d(0, 3.75rem, 0);
}

body.is-loaded .tg_animated_slider_wrapper .c-header-home_subheading.-load {
  transform: translate3d(0, 0, 0);
}

body[data-route-option="prev-section"] .tg_animated_slider_wrapper .c-header-home_subheading.-load, 
body[data-route-option="next-section"] .tg_animated_slider_wrapper .c-header-home_subheading.-load {
  transform: translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper .c-header-home_footer {
  z-index: 3;
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
}

.tg_animated_slider_wrapper .c-header-home_controls,
.tg_animated_slider_wrapper .c-header-home_buttons {
  margin-left: 0;
  letter-spacing: normal;
  font-size: 28px;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate3d(0, 100%, 0);
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper .c-header-home_controls,
  .tg_animated_slider_wrapper .c-header-home_buttons {
    padding-bottom: 40px;
  }
}
@media (min-width: 700px) {
  .tg_animated_slider_wrapper .c-header-home_controls,
  .tg_animated_slider_wrapper .c-header-home_buttons {
    padding-bottom: 6rem;
  }
}
@media (min-width: 700px) and (max-width: 749px) {
  .tg_animated_slider_wrapper .c-header-home_controls,
  .tg_animated_slider_wrapper .c-header-home_buttons {
    padding-bottom: 3.75rem;
  }
}
body.is-loaded .tg_animated_slider_wrapper .c-header-home_controls,
body.is-loaded .tg_animated_slider_wrapper .c-header-home_buttons {
  transform: translate3d(0, 0, 0);
}

body[data-route-option="prev-section"] .tg_animated_slider_wrapper .c-header-home_controls,
body[data-route-option="prev-section"] .tg_animated_slider_wrapper .c-header-home_buttons {
  transform: translate3d(0, 0, 0);
}
body[data-route-option="next-section"] .tg_animated_slider_wrapper .c-header-home_controls,
body[data-route-option="next-section"] .tg_animated_slider_wrapper .c-header-home_buttons {
  transform: translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper .c-header-home_controls {
  transition-delay: 0.65s;
}

@media (min-width: 700px) {
  .tg_animated_slider_wrapper .c-header-home_controls {
    float: left;
  }
}
.tg_animated_slider_wrapper .c-header-home_buttons {
  transition-delay: 0.75s;
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper .c-header-home_buttons {
    margin-left: -20px;
    margin-right: -20px;
  }
}
@media (min-width: 1000px) {
  .tg_animated_slider_wrapper .c-header-home_buttons {
    float: right;
  }
}
@media (max-width: 699px) {
  .tg_animated_slider_wrapper .c-header-home_button {
    width: 50% !important;
  }
}
@media (min-width: 700px) {
  .tg_animated_slider_wrapper .c-header-home_button {
    width: 15.625rem;
  }
}

.tg_animated_slider_wrapper button,
.tg_animated_slider_wrapper .c-header-filters_button,
.tg_animated_slider_wrapper .o-button {
  display: inline-block;
  overflow: visible;
  margin: 0;
  padding: 0;
  outline: 0;
  border: 0;
  background: none;
  color: inherit;
  vertical-align: middle;
  text-align: center;
  text-decoration: none;
  text-transform: none;
  font: inherit;
  line-height: normal;
  cursor: pointer;
  user-select: none;
  color: #000;
  margin-right: 10px;
}

.tg_animated_slider_wrapper button:hover,
.tg_animated_slider_wrapper .c-header-filters_button:hover,
.tg_animated_slider_wrapper .o-button:hover {
  text-decoration: none;
}

.tg_animated_slider_wrapper .o-link {
  color: #1a0dab;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tg_animated_slider_wrapper .o-link:hover {
  color: #13097c;
}

.tg_animated_slider_wrapper .o-link.-normal {
  color: currentColor;
  text-decoration: none;
}

.tg_animated_slider_wrapper .o-link.-normal:hover {
  text-decoration: underline;
}

.tg_animated_slider_wrapper .o-link.-blue:hover {
  text-decoration: none;
  color: #0084c0;
}

.tg_animated_slider_wrapper .o-link.-hover {
  position: relative;
  text-decoration: none;
  color: #fff;
}

.tg_animated_slider_wrapper .o-link.-hover::after {
  content: "";
  position: absolute;
  bottom: -1px;
  right: 0;
  left: 0;
  border-bottom: 1px solid;
  transform: scaleX(0);
  transition: transform 0.45s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center left;
}

.tg_animated_slider_wrapper .o-link.-hover:hover::after {
  transform: scaleX(1);
}

.tg_animated_slider_wrapper .js-parallax {
  transform: translateZ(0);
  will-change: transform;
}

.tg_animated_slider_wrapper .scroll-content {
  overflow: hidden;
}

.tg_animated_slider_wrapper .o-blockquote.-nomargin {
  margin: 0;
}

.tg_animated_slider_wrapper .o-action-link {
  display: block;
  padding-top: 12.8125rem;
  padding-bottom: 7.5rem;
  text-align: center;
  text-decoration: none;
  font-weight: 700;
}
.tg_animated_slider_wrapper .o-action-link:hover {
  color: #fff;
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper .o-action-link {
    font-size: 40px;
    padding-top: 120px;
  }
}
@media (max-width: 1199px) {
  .tg_animated_slider_wrapper .o-action-link {
    color: #1e1e22;
  }
}
@media (min-width: 700px) {
  .tg_animated_slider_wrapper .o-action-link {
    font-size: 5.625rem;
  }
}
@media (min-width: 1200px) {
  .tg_animated_slider_wrapper .o-action-link {
    color: #fff;
  }
}
.tg_animated_slider_wrapper .o-action-link_label {
  display: inline-block;
  position: relative;
}
.tg_animated_slider_wrapper .o-action-link_label::after {
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  border-bottom: 0.1875rem solid;
  transform: scaleX(0);
  transform-origin: center left;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.tg_animated_slider_wrapper .o-action-link:hover .o-action-link_label::after {
  transform: scaleX(1);
}

.tg_animated_slider_wrapper .o-hsub {
  padding: 1.25rem 0;
  display: inline-block;
}
.tg_animated_slider_wrapper .o-hsub::before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  border-bottom: 1px solid;
  width: 1.5rem;
  background-color: #1e1e22;
  margin-right: 1.125rem;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center left;
}
.tg_animated_slider_wrapper .o-hsub.-link {
  color: #fff;
  text-decoration: none;
}
.tg_animated_slider_wrapper .o-hsub.-link:hover::before {
  transform: scaleX(1.5);
}

.tg_animated_slider_wrapper .o-hsub-wrap:hover .o-hsub.-link::before {
  transform: scaleX(1.5);
}
.tg_animated_slider_wrapper .o-hsub.-h {
  vertical-align: middle;
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper .o-hsub.-h {
    display: block;
    margin-top: 20px;
  }
}
@media (min-width: 700px) {
  .tg_animated_slider_wrapper .o-hsub.-h {
    margin-left: 2.5rem;
  }
}
.tg_animated_slider_wrapper .o-hsub_label {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  display: inline-block;
}

.tg_animated_slider_wrapper .o-hsub.-link:hover .o-hsub_label,
.tg_animated_slider_wrapper .o-hsub-wrap:hover .o-hsub_label {
  transform: translateX(20px);
}

body.is-loaded .tg_animated_slider_wrapper .o-loader {
  visibility: hidden;
  transition-delay: 0.6s;
}

.tg_animated_slider_wrapper .o-container {
  position: relative;
  margin: 0 auto;
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper .o-container {
    padding-right: 40px;
    padding-left: 40px;
  }
  .tg_animated_slider_wrapper .o-container.-small {
    padding-right: 20px;
    padding-left: 20px;
  }
}
@media (min-width: 700px) and (max-width: 1599px) {
  .tg_animated_slider_wrapper .o-container {
    padding-right: 90px;
    padding-left: 90px;
  }
}
.tg_animated_slider_wrapper .o-section {
  position: relative;
}
.tg_animated_slider_wrapper .o-section.-offset {
  margin-top: -9.375rem;
  background-color: #f6f6f6;
}
.tg_animated_slider_wrapper .o-section.-padding {
  padding-top: 7.5rem;
  padding-bottom: 7.5rem;
}
.tg_animated_slider_wrapper .o-section.-padding-top {
  padding-top: 7.5rem;
}
.tg_animated_slider_wrapper .o-section.-left {
  margin-right: 15rem;
}
.tg_animated_slider_wrapper .o-section.-right {
  margin-left: 15rem;
}
.tg_animated_slider_wrapper .o-section.-left-large {
  margin-right: 22.5rem;
}
.tg_animated_slider_wrapper .o-section.-right.-padding {
  padding-left: 9.5625rem;
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper .o-section.-bottom {
    padding-bottom: 60px;
  }
}
@media (min-width: 700px) {
  .tg_animated_slider_wrapper .o-section.-bottom {
    padding-bottom: 7.5rem;
  }
}

.tg_animated_slider_wrapper .slideshow__slide-image.background-absolute {
  width: 65%;
  left: 35%;
}

.tg_animated_slider_wrapper h2.slideshow__slide-caption-title {
  padding-left: 90px;
  padding-right: 90px;
  color: black;
}

.tg_animated_slider_wrapper .o-hsub.-link {
  padding-left: 90px;
  padding-right: 90px;
  color: #000;
  text-decoration: none;
}

.tg_animated_slider_wrapper button.slider-more-button {
  width: 16rem;
  height: 7rem;
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #fcc865;
  color: #000;
  text-transform: uppercase;
  transition: opacity 700ms cubic-bezier(0.8, 0, 0.55, 0.94) 470ms, transform 700ms cubic-bezier(0.8, 0, 0.55, 0.94) 470ms;
  transform: rotateZ(-20deg) translate3d(-20vh, 20vh, 0);
}

body.is-animated .tg_animated_slider_wrapper .slider-more-button {
  transform: rotateZ(0deg) translate3d(0, 0, 0);
}

.tg_animated_slider_wrapper p.slideshow__slide-caption-content {
  padding-left: 90px;
  padding-right: 90px;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.7);
  cursor: pointer;
  display: block;
  margin-top: 20px;
  opacity: 0;
  text-size-adjust: 100%;
  transition: opacity 700ms cubic-bezier(0.8, 0, 0.55, 0.94), transform 700ms cubic-bezier(0.8, 0, 0.55, 0.94);
  transform: translate3d(-100px, 0, 0);
  max-width: 40%;
  width: 40%;
}

.tg_animated_slider_wrapper .slideshow__slide.is-current p.slideshow__slide-caption-content {
  opacity: 1;
  transition: opacity 700ms cubic-bezier(0.8, 0, 0.55, 0.94) 1470ms, transform 700ms cubic-bezier(0.8, 0, 0.55, 0.94) 1470ms;
  transform: translate3d(0, 0, 0);
}

/*end slider show*/
/*Floating button*/
.tg_animated_slider_wrapper .slideshow__slide.is-current .floating-btn {
  border: none;
  border-width: 0px;
  margin: 0;
  overflow: hidden;
  border: none;
  outline: none;
  color: white;
  font-size: 20px;
  background: #F6EFE3;
  position: relative;
  padding: 14px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  box-shadow: 0 12px 42px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.24);
  transition: .3s;
  margin: 0.5em;
}
.tg_animated_slider_wrapper .slideshow__slide.is-current .floating-btn:hover {
  background: #d2cbbf;
  cursor: pointer;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.tg_animated_slider_wrapper .slideshow__slide.is-current .js-parallax.is-inview {
  width: 100px;
}

.tg_animated_slider_wrapper .slideshow__slide.is-current button .floating-btn:focus {
  outline: 0px;
  outline: 0px;
}

.tg_animated_slider_wrapper .slideshow__slide.is-current .c-header-home_controls.-nomobile.o-button-group {
  margin-left: 30%;
}

.tg_animated_slider_wrapper .slideshow__slide.is-current .divide {
  margin-top: 50px;
  margin-bottom: 50px;
}

.tg_animated_slider_wrapper .slideshow__slide.is-current .ripple {
  overflow: hidden;
}

.tg_animated_slider_wrapper .slideshow__slide.is-current .ripple-effect {
  position: absolute;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  background: white;
  animation: ripple-animation 2s;
}

.tg_animated_slider_wrapper .slideshow__slide.is-current .ripple > .o-button_icon {
  top: 1px;
}

@keyframes ripple-animation {
  from {
    transform: scale(1);
    opacity: 0.4;
  }
  to {
    transform: scale(100);
    opacity: 0;
  }
}
/* End Floating Button */
/* nav bar*/
.tg_animated_slider_wrapper .slideshow__slide.is-current .navbar-container {
  z-index: 1000;
  background-color: #fff;
}

@media (max-width: 699px) {
  .tg_animated_slider_wrapper .slideshow__slide.is-current .container {
    padding-right: 0px;
    padding-left: 0px;
  }
}
@media (min-width: 700px) and (max-width: 1599px) {
  .tg_animated_slider_wrapper .slideshow__slide.is-current .container {
    padding-right: 0rem;
    padding-left: 0rem;
    max-width: 140rem;
  }
}

/*
	End CSS for dotlife-animated-slider
*/


/*
	Begin CSS for dotlife-fade-up-slider
*/

.tg_fadeup_slider_wrapper.cd-slider {
	position: relative;
	width: 100%;
	height: 430px;
	overflow: hidden;
}
.tg_fadeup_slider_wrapper.cd-slider li {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	visibility: hidden;
	transition: visibility 0s 1s;
  will-change: visibility;
}
.tg_fadeup_slider_wrapper li .image {
	position: absolute;
	top: 0;
	left: 0;
	width: 66.66%;
	height: 100%;
	background-size: cover;
	background-position: 50% 50%;
	clip: rect(0, 80rem, 50rem, 80rem);
	transition: clip .5s cubic-bezier(0.99, 0.01, 0.45, 0.9) .5s;
  will-change: clip;
}
.tg_fadeup_slider_wrapper li .content {
	position: absolute;
	top: 0;
	right: 0;
	width: 100%;
	height: 100%;
	padding: 4rem 0 0 2rem;
	text-align: right;
}
.tg_fadeup_slider_wrapper li .content h2 {
	font-size: 42px;
	padding: 1.5rem;
	display: inline-block;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-transform: translateY(-30%);
	        transform: translateY(-30%);
	opacity: 0;
	transition: opacity .5s, -webkit-transform .5s;
	transition: transform .5s, opacity .5s;
	transition: transform .5s, opacity .5s, -webkit-transform .5s;
  will-change: transform, opacity;
}
.tg_fadeup_slider_wrapper li .content .description {
	display: inline-block;
	width: 30%;
	font-size: 16px;
	padding: 1.5rem;
	padding-top: 0;
	opacity: 0;
	-webkit-transform: translateY(-60%);
	        transform: translateY(-60%);
	transition: opacity .5s .1s, -webkit-transform .5s .1s;
	transition: transform .5s .1s, opacity .5s .1s;
	transition: transform .5s .1s, opacity .5s .1s, -webkit-transform .5s .1s;
  will-change: transform, opacity;
}
.tg_fadeup_slider_wrapper li .content a {
	display: block;
	font-size: 16px;
	padding: 1.5rem;
	opacity: 0;
	-webkit-transform: translateY(-100%);
	        transform: translateY(-100%);
	transition: opacity .5s .2s, -webkit-transform .5s .2s;
	transition: transform .5s .2s, opacity .5s .2s;
	transition: transform .5s .2s, opacity .5s .2s, -webkit-transform .5s .2s;
  will-change: transform, opacity;
}
/* Current Slide 
-------------------------------------*/
.tg_fadeup_slider_wrapper li.current {
	visibility: visible;
	transition-delay: 0s;
}
.tg_fadeup_slider_wrapper li.current .image {
	clip: rect(0, 80rem, 50rem, 0);
}
.tg_fadeup_slider_wrapper li.current .content h2 {
	-webkit-transform: translateY(0);
	        transform: translateY(0);
	opacity: 1;
	transition-delay: 1s;
}
.tg_fadeup_slider_wrapper li.current .content .description {
	-webkit-transform: translateY(0);
	        transform: translateY(0);
	opacity: 1;
	transition-delay: 1.1s;
}
.tg_fadeup_slider_wrapper li.current .content a {
	-webkit-transform: translateY(0);
	        transform: translateY(0);
	opacity: 1;
	transition-delay: 1.2s;
}
/* Prev Slide 
------------------------------------*/
.tg_fadeup_slider_wrapper li.prev_slide .image {
	clip: rect(0, 0, 50rem, 0);
}
.tg_fadeup_slider_wrapper nav {
	position: absolute;
	bottom: 0;
	right: 0;
	z-index: 2;
}
.tg_fadeup_slider_wrapper .prev, 
.tg_fadeup_slider_wrapper .next, 
.tg_fadeup_slider_wrapper .counter {
	vertical-align: middle;
}
.tg_fadeup_slider_wrapper .prev, 
.tg_fadeup_slider_wrapper .next {
	position: relative;
	display: inline-block;
	background: transparent;
	border: 0;
	font-size: 30px;
	cursor: pointer;
	outline: none;
}
.tg_fadeup_slider_wrapper .prev::before {
	font-family: 'themify';
	content: "\e629";
	margin-right: 10px;
} 
.tg_fadeup_slider_wrapper .next::before {
	font-family: 'themify';
	content: "\e628";
	margin-left: 10px;
}

.tg_fadeup_slider_wrapper .counter {
	display: inline-block;
	font-size: 30px;
}
.tg_fadeup_slider_wrapper .counter span:last-child::before {
	content: '/';
	margin: 0 1rem;
}
.tg_fadeup_slider_wrapper .prev:hover::after, 
.tg_fadeup_slider_wrapper .next:hover::after {
	width: 3.5rem;
}
@media screen and ( max-width: 550px ) {
	.tg_fadeup_slider_wrapper li .image { width: 100%; }
	.tg_fadeup_slider_wrapper li .content { padding-right: 2rem; }
}

/*
	End CSS for dotlife-fade-up-slider
*/


/*
	Begin CSS for dotlife-motion-reveal-slider
*/

.tg_motion_reveal_slider_wrapper.slideshow {
	position: relative;
	width: 100%;
	margin: 3.5rem 0 0 0;
	padding: 0 0 3.5rem 0;
	pointer-events: none;
	overflow: hidden;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide {
	width: 100%;
	height: 50vh;
	top: 0;
	position: absolute;
	display: grid;
	grid-template-columns: 100%;
	grid-template-rows: 100%;
	align-items: center;
	justify-items: center;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide--current {
	position: relative;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide__img-wrap {
	grid-area: 1 / 1 / 1 / 1;
	width: 100%;
	height: calc(100vh - 13rem);
	max-height: 100%;
	overflow: hidden;
	position: relative;
	opacity: 0;
	outline: 3px solid transparent;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide__img {
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	background-size: cover;
	background-position: 50% 50%;
	position: absolute;
	backface-visibility: hidden;
	pointer-events: none;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide__img-reveal {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: var(--color-reveal);
	transform: translate3d(-100%,0,0);
	pointer-events: none;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide__number {
	font-size: 2rem;
	position: absolute;
	top: 5.25rem;
	opacity: 0;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide__title {
	margin: 0;
	grid-area: 1 / 1 / 1 / 1;
	font-size: 50px;
	color: var(--color-title);
	position: relative;
	opacity: 0;
	text-align: center;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide--current .slide__img-wrap,
.tg_motion_reveal_slider_wrapper.slideshow .slide--current .slide__number,
.tg_motion_reveal_slider_wrapper.slideshow .slide--current .slide__title {
	opacity: 1;
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__item {
	pointer-events: auto;
	border: 0;
	background: none;
	padding: 1rem;
	margin: 0.85rem;
	text-transform: inherit;
	position: relative;
	line-height: 1;
	color: var(--color-nav);
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__item::before,
.tg_motion_reveal_slider_wrapper.slideshow .slidenav__item::after {
	content: '';
	position: absolute;
	width: 3rem;
	height: 1px;
	background: currentColor;
	top: 50%;
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__item::before {
	right: 100%;
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__item::after {
	left: 100%;
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__item:hover {
	color: var(--color-link-hover);
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__item--prev {
	transform: rotate(-90deg) translateY(-50%);
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__item--next {
	transform: rotate(90deg) translateY(-25%);
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__preview {
	pointer-events: auto;
	color: var(--color-nav);
	position: absolute;
	bottom: 0;
	transform: translateX(-50%);
	left: 50%;
	border: 0;
	background: none;
	padding: 1rem;
	transition: 0.3s transform;
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__preview:hover {
	color: var(--color-link-hover);
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__preview .icon {
	pointer-events: none;
}

.tg_motion_reveal_slider_wrapper.slideshow .slidenav__preview--open {
	transform: translateX(-50%) rotate(180deg);
}

.tg_motion_reveal_slider_wrapper.slideshow .preview,
.tg_motion_reveal_slider_wrapper.slideshow .preview__img-wrap,
.tg_motion_reveal_slider_wrapper.slideshow .preview__img,
.tg_motion_reveal_slider_wrapper.slideshow .preview__img-reveal {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	pointer-events: none;
}

.tg_motion_reveal_slider_wrapper.slideshow .preview {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.tg_motion_reveal_slider_wrapper.slideshow .preview__img-wrap {
	overflow: hidden;
	opacity: 0;
}

.tg_motion_reveal_slider_wrapper.slideshow .preview__img {
	background-size: cover;
	background-position: 50% 50%;
	backface-visibility: hidden;
}

.tg_motion_reveal_slider_wrapper.slideshow .preview__img-reveal {
	background: var(--color-reveal);
}

.tg_motion_reveal_slider_wrapper.slideshow .preview__title {
	position: relative;
	font-size: 1.5rem;
	opacity: 0;
	padding: 0 1rem;
	margin: 0 0 1rem 0;
	text-align: center;
}

.tg_motion_reveal_slider_wrapper.slideshow .preview__content {
	position: relative;
	text-align: justify;
	opacity: 0;
	padding: 0 1rem;
	width: 70%;
}

.tg_motion_reveal_slider_wrapper.slideshow .slide__img-wrap {
	width: 400px;
}

.tg_motion_reveal_slider_wrapper.slideshow .icon {
	display: block;
	width: 1.5em;
	height: 1.5em;
	margin: 0 auto;
	fill: currentColor;
}

.tg_motion_reveal_slider_wrapper.slideshow nav button {
	cursor: pointer;
	outline: none;
}

.tg_motion_reveal_slider_wrapper.slideshow .hidden {
	position: absolute;
	overflow: hidden;
	width: 0;
	height: 0;
	pointer-events: none;
}

/*
	End CSS for dotlife-motion-reveal-slider
*/


/*
	Begin CSS for dotlife-testimonial-card
*/

.tg_testimonials_card_wrapper {
  width: 100%;
  overflow: hidden;
}

.tg_testimonials_card_wrapper .slider {
    margin-bottom: 6em;
    position: relative;
  }

.tg_testimonials_card_wrapper .slider > ul {
    position: relative;
    margin: 0 auto !important;
    list-style: none !important;
  }

.tg_testimonials_card_wrapper .slider > ul li {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    float: left;
    width: 555px;
    min-height: 272px;
    margin: 0 2.5em;
    padding: 3em;
    position: absolute;
    transition: left .3s ease-in-out, top .3s ease-in-out;
  }

.tg_testimonials_card_wrapper .testimonial-info {
    width: 100%;
  }

.tg_testimonials_card_wrapper .testimonial-info h3 {
      display: inline-block;
      margin-bottom: 0;
    }

.tg_testimonials_card_wrapper .testimonial-info .rating {
      float: right;
      margin-top: 5px;
    }

.tg_testimonials_card_wrapper .testimonial-info i {
      color: #7dc855;
    }

.tg_testimonials_card_wrapper .testimonial-info p {
      margin-bottom: 1em;
    }

.tg_testimonials_card_wrapper .slider-nav {
    position: absolute;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
    z-index: 999;
    margin: 2em 0;
  }

.tg_testimonials_card_wrapper .bullet {
    display: inline-block;
    border-radius: 50%;
    background: #7dc855;
    width: 10px;
    height: 10px;
    margin: 5px 0;
    border: 1px solid transparent;
    margin: 0 .25em;
  }

.tg_testimonials_card_wrapper .bullet:not(.active-bullet):hover {
      cursor: pointer;
      border: 2px solid #ffffff;
      -webkit-transform: scale(1.04);
              transform: scale(1.04);
    }

.tg_testimonials_card_wrapper .active-bullet {
    background: #ffffff ;
    transition: background 1s ease;
  }

.tg_testimonials_card_wrapper .tag {
    margin-bottom: 4em;
  }

.tg_testimonials_card_wrapper .companies {
    width: 100%;
    height: auto;
    text-align: center;
  }

.tg_testimonials_card_wrapper .companies li {
      display: inline-block;
      margin: 0 3em 3em 3em;
    }

.tg_testimonials_card_wrapper .companies a:hover i {
      color: #7dc855;
    }

.tg_testimonials_card_wrapper .companies i {
      font-size: 3.5em;
      color: #ccc;
    }

.tg_testimonials_card_wrapper .testimonial-info div.author {
	margin-top: 10px;
}

.tg_testimonials_card_wrapper .testimonial-info .testimonial-info-title {
	width: calc(100% - 120px);
	float: left;
}

.tg_testimonials_card_wrapper .testimonial-info .testimonial-info-img {
	width: 120px;
	float: right;
	text-align: right;
}

.tg_testimonials_card_wrapper .testimonial-info .testimonial-info-img img {
	max-width: 100px;
	border-radius: 250px;
}

.tg_testimonials_card_wrapper .testimonial-info-desc {
	width: 100%;
	clear: both;
	margin-top: 20px;
	float: left;
}

/*
	End CSS for dotlife-testimonial-card
*/


/*
	Begin CSS for dotlife-slider-image-carousel
*/

.tg_image_carousel_slider_wrapper.carousel {
    width: 100%;
    overflow: hidden;
	position: relative;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item {
  visibility:visible;
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: flex-end;
      -webkit-align-items: center;
    -webkit-justify-content: flex-end;
    position: relative;
    background-color: #fff;
    flex-shrink: 0;
   -webkit-flex-shrink: 0;
    position: absolute;
    z-index: 0;
  transition: 0.6s all linear;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item .carousel-item__info {
  height: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  
    display: -webkit-flex;
  -webkit-justify-content: center;
  -webkit-flex-direction: column;
  
  order: 1;
  left: 0;
  margin: auto;
  padding: 0 40px;
  width: 40%;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item .carousel-item__image {
    width: 60%;
    height: 100%;
    order: 2;
    align-self: flex-end;
    flex-basis: 60%;
  
      -webkit-order: 2;
    -webkit-align-self: flex-end;
    -webkit-flex-basis: 60%;
  
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
   position:relative;
  transform: translateX(100%);
  transition: 0.6s all ease-in-out;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item .carousel-item__subtitle {
    letter-spacing: 3px;
    font-size: 10px;
    text-transform: uppercase;
    margin: 0;
    color: #7E7E7E;    
    font-weight: 700;
    transform: translateY(25%);
    opacity: 0;
    visibility: hidden;
    transition: 0.4s all ease-in-out;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item .carousel-item__title {
    margin: 15px 0 0 0;
    font-size: 44px;
    letter-spacing: 3px;
    color: #2C2C2C;
    line-height: 1.2;
    transform: translateY(25%);
    opacity: 0;
    visibility: hidden;
    transition: 0.6s all ease-in-out;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item .carousel-item__description {
    transform: translateY(25%);
    opacity: 0;
    visibility: hidden;
    transition: 0.6s all ease-in-out;
    margin-top: 35px;
    font-size: 13px;
    color: #7e7e7e;
    margin-bottom: 35px;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item .carousel-item__btn {
    width: 35%;
    color: #2C2C2C;
    letter-spacing: 3px;
    font-size: 11px;
    text-transform: uppercase;
    margin: 0;
    width: 35%;
    font-weight: 600;
  text-decoration: none;
      transform: translateY(25%);
    opacity: 0;
    visibility: hidden;
    transition: 0.6s all ease-in-out;
}

.tg_image_carousel_slider_wrapper.carousel .carousel__nav {
    position: absolute;
    right: 0;
    z-index: 2;
    background-color: #fff;
  bottom: 0;
}

.tg_image_carousel_slider_wrapper.carousel .carousel__nav .carousel__icon {
    display: inline-block;
    vertical-align: middle;
    width: 16px;
	fill: #5d5d5d;
}

.tg_image_carousel_slider_wrapper.carousel .carousel__nav .carousel__arrow {
    cursor: pointer;
    display: inline-block;
    padding: 11px 15px;
    position: relative;
}

.tg_image_carousel_slider_wrapper.carousel .carousel__nav .carousel__arrow:nth-child(1):after {
    content:'';
    right: -3px;
    position: absolute;
    width: 1px;
    background-color: #b0b0b0;
    height: 14px;
    top: 50%;
    margin-top: -7px;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item.active {
   z-index: 1;
   display: flex;
   visibility:visible;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item.active .carousel-item__subtitle, 
.tg_image_carousel_slider_wrapper.carousel .carousel-item.active .carousel-item__title, 
.tg_image_carousel_slider_wrapper.carousel .carousel-item.active .carousel-item__description,
.tg_image_carousel_slider_wrapper.carousel .carousel-item.active .carousel-item__btn {
    transform: translateY(0);
    opacity: 1;
	transition: 0.6s all ease-in-out;
    visibility: visible;
}

.tg_image_carousel_slider_wrapper.carousel .carousel-item.active .carousel-item__image { 
	transition: 0.6s all ease-in-out;
	transform: translateX(0);
}

/*
	End CSS for dotlife-slider-image-carousel
*/


/*
	Begin CSS for dotlife-navigation-menu
*/

.tg_navigation_wrapper:empty
{
	min-height: 70px;
}

.tg_navigation_wrapper .nav
{
	padding: 0;
}

.tg_navigation_wrapper .nav li
{
	display: inline-block;
	margin: 0;
	padding: 12px 0 12px 0;
}

.tg_navigation_wrapper.menu_style2 .nav li,
.tg_navigation_wrapper.menu_style3 .nav li
{
	padding: 12px 0 12px 0;
}

.tg_navigation_wrapper.menu_style1 .nav li
{
	padding: 0;
}

.tg_navigation_wrapper .nav li > a
{
	display: inline-block;
	position: relative;
	padding: 0 5px;
	
	-webkit-transition: padding 0.3s;
	-moz-transition: padding 0.3s;
	transition: padding 0.3s;
}

.tg_navigation_wrapper.menu_style1 .nav li > a,
.tg_navigation_wrapper.menu_style3 .nav li > a
{
	padding: 0;
}

.tg_navigation_wrapper.menu_style1 .nav li > a
{
	padding: 0;
}

.tg_navigation_wrapper.menu_style3 .nav li > a:before
{
    content: '';
    display: block;
    position: absolute;
    bottom: 2px;
    left: 0;
    height: 1px;
    width: 100%;
    background-color: #666;
    transform-origin: center top;
    transform: scale(0,1);
    transition: color .1s,transform .2s ease-out;

}

.tg_navigation_wrapper.menu_style2 .nav li > a:before
{
	content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    opacity: 0.2;
    height: 50%;
    width: 100%;
    background-color: #666;
    transform-origin: right top;
    transform: scale(0, 1);
    transition: color 0.1s,transform 0.2s ease-out;
}

.tg_navigation_wrapper.menu_style1 .nav li > a:before
{
	content: '';
    display: block;
    position: absolute;
    bottom: 2px;
    left: 0;
    height: 1px;
    width: 100%;
    background-color: #666;
    transform-origin: right top;
    transform: scale(0,1);
    transition: color .1s,transform .2s ease-out;
}

.tg_navigation_wrapper.menu_style1 .nav li > a:active:before,
.tg_navigation_wrapper.menu_style2 .nav li > a:active:before
{
	background-color: #666;
}

.tg_navigation_wrapper.menu_style1 .nav li > a:hover:before,
.tg_navigation_wrapper.menu_style2 .nav li > a:hover:before
{
  	transform-origin: left top;
    transform: scale(1, 1);
}

.tg_navigation_wrapper.menu_style3 .nav li > a:hover:before
{
	transform-origin: center top;
    transform: scale(1, 1);
}

.tg_navigation_wrapper .nav li.arrow > a:after
{
	text-decoration: inherit;
	-webkit-font-smoothing: antialiased;
	display: inline;
	width: auto;
	height: auto;
	line-height: normal;
	vertical-align: 10%;
	background-image: none;
	background-position: 0% 0%;
	background-repeat: repeat;
	font-family: 'themify';
	content: "\e64b";
	float: right;
	margin-left: 8px;
	line-height: 3.4em;
}

.tg_navigation_wrapper .nav li.arrow > a:after
{
	font-size: 9px;
}

.tg_navigation_wrapper .nav li > ul,
.tg_navigation_wrapper .nav li > .elementor-megamenu-wrapper
{
	list-style: none;
	background: transparent;
	position: absolute;
 	width: 220px;
 	height: 0;
 	padding: 0;
 	z-index: -1; 
	margin: 0;
	margin-left: 0;
	margin-top: 0;
	border: 0;
	-webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
	opacity: 0;
	overflow: hidden;
    
    -webkit-transform: translate(0px,-15px);
    -moz-transform: translate(0px,-15px);
    transform: translate(0px,-15px);
    
    transition: all 0.2s ease-out;
    -webkit-transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
}

.tg_navigation_wrapper .nav li ul
{
	padding: 5px 0 5px 0;
}

.tg_navigation_wrapper .nav li ul li
{
	padding: 5px 0 5px 0;
	clear: both;
	display: inline-block;
	width: 100%;
	box-sizing: border-box;
}

.tg_navigation_wrapper .nav li ul li ul
{
	position: absolute;
	left: 221px;
	margin-top: -39px;
	margin-left: 0;
	opacity: 0;
}

.tg_navigation_wrapper .nav li ul li ul:before
{
	display: none;
}

.tg_navigation_wrapper .nav > li:hover > ul,
.tg_navigation_wrapper .nav > li > ul > li:hover > ul,
.tg_navigation_wrapper .nav > li:hover > .elementor-megamenu-wrapper
{
    z-index: 9;
    opacity: 1;
    height: auto;
    -ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
    
    -webkit-transform: translate(0px,0px);
    -moz-transform: translate(0px,0px);
    transform: translate(0px,0px);
    
    overflow: visible;
}

.tg_navigation_wrapper .nav li.current-menu-item ul li a, .tg_navigation_wrapper .nav li ul li.current-menu-item a, .tg_navigation_wrapper .nav li ul li.current-menu-parent a
{
	background: transparent;
	height: auto;
}

.tg_navigation_wrapper .nav li ul li a, .tg_navigation_wrapper .nav li.current-menu-parent ul li a
{
	border-top: 0;
	margin: 0;
	font-size: 11px;
	padding: 0;
	padding-right: 5px;
	letter-spacing: 0;
	font-weight: 400;
	text-transform: none;
	box-sizing: border-box;
	
	-webkit-transition: color .2s linear, background .2s linear;
	-moz-transition: color .2s linear, background .2s linear;
	-ms-transition: color .2s linear, background .2s linear;
	-o-transition: color .2s linear, background .2s linear;
	transition: color .2s linear, background .2s linear;
}

.tg_navigation_wrapper .nav li:first-child > a, 
.tg_navigation_wrapper .nav li ul li:first-child > a,
.tg_navigation_wrapper .nav li.current-menu-parent ul li:first-child > a
{
	margin-top: 10px;
}

.tg_navigation_wrapper .nav li:last-child > a, 
.tg_navigation_wrapper .nav li ul li:last-child > a,
.tg_navigation_wrapper .nav li.current-menu-parent ul li:last-child > a
{
	margin-bottom: 10px;
}

.tg_navigation_wrapper .nav li.megamenu > ul,
.tg_navigation_wrapper .nav li.megamenu > .elementor-megamenu-wrapper
{
	position: fixed;
	width: 960px;
	left: 0;
	right: 0;
	margin-left:auto;
    margin-right:auto;
    padding: 0;
	box-sizing: border-box;
	overflow: hidden;
}

.tg_navigation_wrapper .nav li.elementor-megamenu.megamenu > ul.sub-menu
{
	display: none;
}

.tg_navigation_wrapper .nav li:not(.megamenu) > ul.sub-menu > li.arrow > a:after, .tg_navigation_wrapper .nav li:not(.megamenu) > ul.sub-menu > li.arrow > a:after
{
	font-size: 7px;
	margin-left: 8px;
	text-decoration: inherit;
	-webkit-font-smoothing: antialiased;
	display: inline;
	width: auto;
	height: auto;
	line-height: normal;
	vertical-align: 10%;
	background-image: none;
	background-position: 0% 0%;
	background-repeat: repeat;
	margin-top: 0;
	font-family: 'themify';
    content: "\e649";
	float: right;
	margin-right: 0px;
	line-height: 3.4em;
}

.tg_navigation_wrapper .nav li.megamenu > ul li
{
	display: block;
	box-sizing: border-box;
	clear: none;
	float: left;
	border-left: 1px solid #eeeeee;
}

.tg_navigation_wrapper .nav li.megamenu > ul li > a
{
	display: none;
}

.tg_navigation_wrapper .nav li.megamenu.elementor-megamenu > ul li > a
{
	display: inline;
}

.tg_navigation_wrapper .nav li.megamenu > ul li:first-child
{
	border: 0;
}

.tg_navigation_wrapper .nav li.megamenu.col2 > ul > li
{
	width: 50%;
	padding: 5px 15px 0 0;
}

.tg_navigation_wrapper .nav li.megamenu.col3 > ul > li
{
	width: 33.3%;
	padding: 5px 15px 0 0;
}

.tg_navigation_wrapper .nav li.megamenu.col4 > ul > li
{
	width: 25%;
	padding: 5px 15px 0 0;
}

.tg_navigation_wrapper .nav li.megamenu > ul li ul, 
.tg_navigation_wrapper .nav li.megamenu > ul li ul
{
	position: relative;
	width: 100%;
	margin: 0;
	border: 0;
	box-shadow: 0 0 0;
	display: block !important;
	opacity: 1 !important;
	left: 0;
	height: auto;
    -ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
    overflow: visible;
}

.tg_navigation_wrapper .nav li.megamenu ul li ul li, .tg_navigation_wrapper .nav li.megamenu ul li ul li
{
	width: 100% !important;
	border: 0 !important;
}

.tg_navigation_wrapper .nav li.megamenu ul li > a, .tg_navigation_wrapper .nav li.megamenu ul li > a:hover, .tg_navigation_wrapper .nav li.megamenu ul li  > a:active
{
	color: #444;
	box-sizing: border-box;
	background: transparent;
}

.tg_navigation_wrapper .nav li.megamenu ul li ul li a, .tg_navigation_wrapper .nav li.megamenu ul li ul li a
{
	color: #888;
	border-bottom: 0;
}

.tg_navigation_wrapper .nav li.megamenu ul li ul li, .tg_navigation_wrapper .nav li.megamenu ul li ul li a
{
	width: auto;
	display: inline-block;
	margin-left: 5px;
	padding: 0;
}

@media only screen and (min-width: 1200px) {
	.tg_navigation_wrapper .nav li.megamenu > ul, 
	.tg_navigation_wrapper .nav li.megamenu > .elementor-megamenu-wrapper
	{
		max-width: 1350px;
		width: 100%;
		width: calc(100vw - 180px);
		box-sizing: border-box;
	}
}

#elementor_mobile_nav.elementor-button .elementor-align-icon-left
{
	float: none;
}

.elementor_mobile_nav
{
	cursor: pointer;
}

.elementor_desktop_hide
{
	z-index: -1;
	opacity: 0;
}

.text-alignright .elementor-column-wrap .elementor-widget-wrap .elementor-widget:first-child,
.text-alignright .elementor-column-wrap .elementor-widget-wrap .elementor-widget.elementor_mobile_nav
{
	margin-left: auto;
}

.no_padding .tg_navigation_wrapper .nav li
{
	padding: 0;
}

.no_padding .tg_navigation_wrapper .nav li a
{
	margin-top: 0;
}

.tg_navigation_wrapper .elementor-widget .elementor-icon-list-item a
{
	width: initial;
}

/*
	End CSS for dotlife-navigation-menu
*/


/*
	Begin CSS for dotlife-video-grid
*/

.portfolio_classic_container.video_grid .video_card {
	position: relative;
	  overflow: hidden;
	  display: block;
	  text-align: center;
	  -webkit-border-top-left-radius: 5px;
	-webkit-border-top-right-radius: 5px;
	-moz-border-radius-topleft: 5px;
	-moz-border-radius-topright: 5px;
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;
}

.portfolio_classic_container.video_grid .portfolio_classic_content {
	-webkit-border-bottom-right-radius: 5px;
	-webkit-border-bottom-left-radius: 5px;
	-moz-border-radius-bottomright: 5px;
	-moz-border-radius-bottomleft: 5px;
	border-bottom-right-radius: 5px;
	border-bottom-left-radius: 5px;
}

.portfolio_classic_container.video_grid .has_no_space .portfolio_classic_content,
.portfolio_classic_container.video_grid .has_no_space .video_card {
	border-radius: 0 !important;
}

.portfolio_classic_container.video_grid .video_card:hover .video_card__image {
  -webkit-transform: scale(1.06);
          transform: scale(1.06);
  transition: -webkit-transform 500ms cubic-bezier(0.165, 0.84, 0.44, 1) 0ms;
  transition: transform 500ms cubic-bezier(0.165, 0.84, 0.44, 1) 0ms;
  transition: transform 500ms cubic-bezier(0.165, 0.84, 0.44, 1) 0ms, -webkit-transform 500ms cubic-bezier(0.165, 0.84, 0.44, 1) 0ms;
}

.portfolio_classic_container.video_grid .video_card__image {
  width: 100%;
  height: 100%;
  -webkit-transform: scale(1);
          transform: scale(1);
  transition: -webkit-transform 700ms cubic-bezier(0.165, 0.84, 0.44, 1) 0ms;
  transition: transform 700ms cubic-bezier(0.165, 0.84, 0.44, 1) 0ms;
  transition: transform 700ms cubic-bezier(0.165, 0.84, 0.44, 1) 0ms, -webkit-transform 700ms cubic-bezier(0.165, 0.84, 0.44, 1) 0ms;
}

.portfolio_classic_container.video_grid .video_card iframe {
	background: #fff;
}

.portfolio_classic_container.video_grid .portfolio_classic_subtitle {
	margin-top: 5px;
}

/*
	End CSS for dotlife-video-grid
*/


/*
	Begin CSS for dotlife-discography-coverflow
*/

.portfolio_coverflow {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
  overflow: hidden;
}

.portfolio_coverflow .swiper-slide {
  width: 400px;
  height: 30vw;
  line-height: 0;
}

.portfolio_coverflow .swiper-slide .swiper-content {
  width: 100%;
  height: 100%;
  overflow: visible;
}

.portfolio_coverflow .swiper-slide .swiper-content .article {
  width: 100%;
  height: 25vw;
  position: relative;
  transition: all 366ms ease-in-out;
  perspective: 1500px;
  border-radius: 8px;
  display: inline-block;
  user-select: none;
}
.portfolio_coverflow .swiper-slide .swiper-content .article:hover {
  cursor: pointer;
}
.portfolio_coverflow .swiper-slide .swiper-content .article .article-thumbnail,
.portfolio_coverflow .swiper-slide .swiper-content .article .article-preview {
  width: 100%;
  height: 25vw;
  position: absolute;
  overflow: hidden;
}

.portfolio_coverflow .swiper-slide .swiper-content .article .article-thumbnail {
  background-size: cover;
  background-position: center center;
  transition: all 366ms ease-in-out;
  transform-origin: 0 0;
}
.portfolio_coverflow .swiper-slide .swiper-content .article .article-thumbnail h2 {
  color: #fff;
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  padding: 50px 20px 20px;
  font-size: 25px;
  background: linear-gradient(to top, rgba(0,0,0,0.2) 50%, rgba(0,0,0,0));
}
.portfolio_coverflow .swiper-slide .swiper-content .article .article-thumbnail h2 span {
  display: block;
  font-size: 11px;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.7;
}
.portfolio_coverflow .swiper-slide .swiper-content .article .article-thumbnail:before {
  content: "";
  height: 100%;
  width: 100%;
  background: linear-gradient(190deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 90%);
  -webkit-clip-path: polygon(0 0, 100% 0, 100% 25%, 0 75%);
  clip-path: polygon(0 0, 100% 0, 100% 25%, 0 75%);
  opacity: 0;
  position: absolute;
  transition: all 366ms ease-in-out;
  transform: translateY(-100%);
}
.portfolio_coverflow .swiper-slide.swiper-slide-active .swiper-content .article:hover > .article-thumbnail {
  transform: rotateX(40deg);
}
.portfolio_coverflow .swiper-slide.swiper-slide-active .swiper-content .article:hover > .article-thumbnail:before {
  transform: translateY(-20%);
  opacity: 1;
}
.portfolio_coverflow .swiper-slide.swiper-slide-active .swiper-content .article:hover > .article-thumbnail:hover > .article:hover > .article-thumbnail-preview,
.portfolio_coverflow .swiper-slide.swiper-slide-active .swiper-content .article:hover > .article-thumbnail:before {
  opacity: 1;
  transform: none;
}
.portfolio_coverflow .swiper-slide.swiper-slide-active .swiper-content .article:hover > .article-preview {
  background-color: #f8f8f8;
}
.portfolio_coverflow .swiper-slide.swiper-slide-active .swiper-content .article:hover > .article-preview:before {
  transform: translateY(0%);
}
.portfolio_coverflow .swiper-slide .swiper-content .article .article-preview {
  font-size: 17px;
  line-height: 28px;
  color: #222;
  transition: all 366ms ease-in-out;
}
.portfolio_coverflow .swiper-slide .swiper-content .article .article-preview p {
  margin: 24px;
  transform: translateY(6px);
}

.portfolio_coverflow .swiper-slide .swiper-content .article .article-preview .controls {
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  display: table;
}

.portfolio_coverflow .swiper-slide .swiper-content .article .article-preview .controls label {
  width: 33.33%;
  box-sizing: border-box;
  cursor: pointer;
  background-color: #f5f5f5;
  padding: 20px;
  display: table-cell;
  vertical-align: bottom;
}
.portfolio_coverflow .swiper-slide .swiper-content .article .article-preview .controls label span {
  width: 100%;
  display: block;
  pointer-events: none;
  color: #000;
  text-align: center;
  font-size: 22px;
  
  transition: color 0.5s;
}

.portfolio_coverflow .swiper-slide .swiper-content .article .article-preview .controls label.active {
  background-color: #f5f5f5;
  zoom: 1;
  
  box-shadow: inset 0px 0px 10px 5px rgba(120, 120, 120, 0.1);
  border: 1px solid #fff;
}

.portfolio_coverflow .swiper-slide .swiper-content .article audio {
	display: none;
}

.portfolio_coverflow .swiper-pagination {
	display: none;
}

.portfolio_coverflow .article .music-playing {
	position: absolute;
	top: 20px;
	right: 20px;
	z-index: 2;
}

/*
	End CSS for dotlife-discography-coverflow
*/

/*
	Begin CSS for slider-glitch-slideshow
*/

.slider_glitch_slideshow.content {
	position: relative;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin: 0 auto;
}

.slider_glitch_slideshow.content .slides {
	position: relative;
}

.slider_glitch_slideshow1.content .slides.slides--contained {
	margin: 0 11rem 0 0;
	width: calc(100vw - 11rem - 4rem);
	display: flex;
	justify-content: flex-end;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide {
	position: absolute;
	opacity: 0;
	top: 0;
	left: 0;
	pointer-events: none;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide--current {
	position: relative;
	opacity: 1;
	pointer-events: auto;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide__img {
	width: 100vw;
	height: 700px;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide__img {
	width: 600px;
	max-width: 100%;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide__text {
	position: absolute;
	top: 40%;
	left: 20vw;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide__text {
	left: -10vw;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide__text {
	color: #fff;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide--current .slide__title {
	animation: glitch-anim-title 0.25s linear alternate 1 backwards;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide__title {
	font-weight: 400;
	font-size: 6rem;
	margin: 0 0 0.25rem;
	font-weight: 600;
	text-transform: none;
	cursor: default;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide__description {
	text-transform: none;
	margin: 20px 0 0 0;
	max-width: 70%;
	cursor: default;
	color: #fff;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide__description a {
	color: #aaa;
	pointer-events: auto;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide__description a:hover {
	color: #fff;
}

.slider_glitch_slideshow.content .slides .slide-nav {
	display: flex;
	flex-direction: column;
	position: absolute;
	top: 0;
	left: 20vw;
	width: 60vw;
	height: 700px;
	justify-content: center;
	align-items: center;
}

.slider_glitch_slideshow.content .slides.slides--contained + .slide-nav {
	flex-direction: row;
	justify-content: flex-start;
	height: auto;
	margin: 0 0 0 0.25rem;
	top: calc(50% - 0.5rem);
	left: calc(100vw - 600px - 11rem - 4rem - 10vw);
	    color: #fff;
}

.slider_glitch_slideshow.content .slide-nav .slide-nav__button {
	display: block;
	background: none;
	border: 0;
	margin: 0 1rem 0 1rem;
	padding: 0;
	text-transform: inherit;
	letter-spacing: inherit;
	font-weight: inherit;
	color: #fff;
}

.slider_glitch_slideshow.content .slide-nav .slide-nav__button span {
	display: block;
	font-size: 30px;
	cursor: pointer;
	display: block;
	margin: 20px 0 20px 0;
}

.slider_glitch_slideshow.content .slide-nav .slide-nav__button:focus {
	outline: none;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide-nav__text {
	font-size: 4vw;
	margin: 0.75rem 0;
	font-weight: 900;
	text-transform: lowercase;
	letter-spacing: 0;
	color: inherit;
}

.slider_glitch_slideshow.content .slides.slides--contained .slide .slide__description .button {
	margin-top: 40px;
}

.effect-2 {
	--gap-horizontal: 10px;
	--gap-vertical: 5px;
	--time-anim: 2s;
	--delay-anim: 0s;
	--blend-mode-1: none;
	--blend-mode-2: none;
	--blend-mode-3: none;
	--blend-mode-4: none;
	--blend-mode-5: overlay;
	--blend-color-1: transparent;
	--blend-color-2: transparent;
	--blend-color-3: transparent;
	--blend-color-4: transparent;
	--blend-color-5: #af4949;
}

/* Glitch styles */
.glitch {
	overflow: hidden;
	position: relative;
}

.glitch,
.glitch__img {
	background-repeat: no-repeat;
	background-position: 50% 50%;
	background-size: cover;
}

.glitch__img {
	position: absolute;
	top: calc(-1 * var(--gap-vertical));
	left: calc(-1 * var(--gap-horizontal));
	width: calc(100% + var(--gap-horizontal) * 2);
	height: calc(100% + var(--gap-vertical) * 2);
	background-color: var(--blend-color-1);
	transform: translate3d(0,0,0);
	background-blend-mode: var(--blend-mode-1);
}

.glitch__img:nth-child(n+2) {
	opacity: 0;
}

.glitch--animate .glitch__img:nth-child(n+2) {
	animation-duration: var(--time-anim);
	animation-delay: var(--delay-anim);
	animation-timing-function: linear;
	animation-iteration-count: infinite;
	animation-fill-mode: forwards;
}

.glitch--animate .glitch__img:nth-child(2) {
	background-color: var(--blend-color-2);
	background-blend-mode: var(--blend-mode-2);
	animation-name: glitch-anim-1;
}

.glitch--animate .glitch__img:nth-child(3) {
	background-color: var(--blend-color-3);
	background-blend-mode: var(--blend-mode-3);
	animation-name: glitch-anim-2;
}

.glitch--animate .glitch__img:nth-child(4) {
	background-color: var(--blend-color-4);
	background-blend-mode: var(--blend-mode-4);
	animation-name: glitch-anim-3;
}

.glitch--animate .glitch__img:nth-child(5) {
	background-color: var(--blend-color-5);
	background-blend-mode: var(--blend-mode-5);
	animation-name: glitch-anim-flash;
}

/* Animations */

@keyframes glitch-anim-1 {
	0%, 100% { 
		opacity: 1;
		transform: translate3d(var(--gap-horizontal),0,0) scale3d(-1,-1,1);
		-webkit-clip-path: polygon(0 2%, 100% 2%, 100% 5%, 0 5%);
		clip-path: polygon(0 2%, 100% 2%, 100% 5%, 0 5%);
	}
	20% {
		-webkit-clip-path: polygon(0 15%, 100% 15%, 100% 15%, 0 15%);
		clip-path: polygon(0 15%, 100% 15%, 100% 15%, 0 15%);
	}
	30% {
		-webkit-clip-path: polygon(0 10%, 100% 10%, 100% 20%, 0 20%);
		clip-path: polygon(0 10%, 100% 10%, 100% 20%, 0 20%);
	}
	40% {
		-webkit-clip-path: polygon(0 1%, 100% 1%, 100% 2%, 0 2%);
		clip-path: polygon(0 1%, 100% 1%, 100% 2%, 0 2%);
	}
	50% {
		-webkit-clip-path: polygon(0 33%, 100% 33%, 100% 33%, 0 33%);
		clip-path: polygon(0 33%, 100% 33%, 100% 33%, 0 33%);
	}
	55% {
		-webkit-clip-path: polygon(0 44%, 100% 44%, 100% 44%, 0 44%);
		clip-path: polygon(0 44%, 100% 44%, 100% 44%, 0 44%);
	}
	60% {
		-webkit-clip-path: polygon(0 50%, 100% 50%, 100% 20%, 0 20%);
		clip-path: polygon(0 50%, 100% 50%, 100% 20%, 0 20%);
	}
	65% {
		-webkit-clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
		clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
	}
	70% {
		-webkit-clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
		clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
	}
	80% {
		-webkit-clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
		clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
	}
	85% {
		-webkit-clip-path: polygon(0 60%, 100% 60%, 100% 65%, 0 65%);
		clip-path: polygon(0 60%, 100% 60%, 100% 65%, 0 65%);
	}
	95% {
		-webkit-clip-path: polygon(0 72%, 100% 72%, 100% 78%, 0 78%);
		clip-path: polygon(0 72%, 100% 72%, 100% 78%, 0 78%);
	}
}

@keyframes glitch-anim-2 {
	0%, 100% { 
		opacity: 1;
		transform: translate3d(calc(-1 * var(--gap-horizontal)),0,0);
		-webkit-clip-path: polygon(0 25%, 100% 25%, 100% 30%, 0 30%);
		clip-path: polygon(0 25%, 100% 25%, 100% 30%, 0 30%);
	}
	10% {
		-webkit-clip-path: polygon(0 3%, 100% 3%, 100% 3%, 0 3%);
		clip-path: polygon(0 3%, 100% 3%, 100% 3%, 0 3%);
	}
	15% {
		-webkit-clip-path: polygon(0 5%, 100% 5%, 100% 20%, 0 20%);
		clip-path: polygon(0 5%, 100% 5%, 100% 20%, 0 20%);
	}
	17% {
		-webkit-clip-path: polygon(0 20%, 100% 20%, 100% 20%, 0 20%);
		clip-path: polygon(0 20%, 100% 20%, 100% 20%, 0 20%);
	}
	19% {
		-webkit-clip-path: polygon(0 40%, 100% 40%, 100% 40%, 0 40%);
		clip-path: polygon(0 40%, 100% 40%, 100% 40%, 0 40%);
	}
	33% {
		-webkit-clip-path: polygon(0 52%, 100% 52%, 100% 59%, 0 59%);
		clip-path: polygon(0 52%, 100% 52%, 100% 59%, 0 59%);
	}
	35% {
		-webkit-clip-path: polygon(0 60%, 100% 60%, 100% 60%, 0 60%);
		clip-path: polygon(0 60%, 100% 60%, 100% 60%, 0 60%);
	}
	40% {
		-webkit-clip-path: polygon(0 75%, 100% 75%, 100% 75%, 0 75%);
		clip-path: polygon(0 75%, 100% 75%, 100% 75%, 0 75%);
	}
	45% {
		-webkit-clip-path: polygon(0 65%, 100% 65%, 100% 40%, 0 40%);
		clip-path: polygon(0 65%, 100% 65%, 100% 40%, 0 40%);
	}
	49% {
		-webkit-clip-path: polygon(0 45%, 100% 45%, 100% 50%, 0 50%);
		clip-path: polygon(0 45%, 100% 45%, 100% 50%, 0 50%);
	}
	50% {
		-webkit-clip-path: polygon(0 14%, 100% 14%, 100% 33%, 0 33%);
		clip-path: polygon(0 14%, 100% 14%, 100% 33%, 0 33%);
	}
	55% {
		-webkit-clip-path: polygon(0 15%, 100% 15%, 100% 35%, 0 35%);
		clip-path: polygon(0 15%, 100% 15%, 100% 35%, 0 35%);
	}
	60% {
		-webkit-clip-path: polygon(0 15%, 100% 15%, 100% 15%, 0 15%);
		clip-path: polygon(0 15%, 100% 15%, 100% 15%, 0 15%);
	}
	70% {
		-webkit-clip-path: polygon(0 65%, 100% 65%, 100% 60%, 0 60%);
		clip-path: polygon(0 65%, 100% 65%, 100% 60%, 0 60%);
	}
	80% {
		-webkit-clip-path: polygon(0 80%, 100% 80%, 100% 85%, 0 85%);
		clip-path: polygon(0 80%, 100% 80%, 100% 85%, 0 85%);
	}
	90% {
		-webkit-clip-path: polygon(0 55%, 100% 55%, 100% 65%, 0 65%);
		clip-path: polygon(0 55%, 100% 55%, 100% 65%, 0 65%);
	}
}

@keyframes glitch-anim-3 {
	0%, 100% { 
		opacity: 1;
		transform: translate3d(0, calc(-1 * var(--gap-vertical)), 0) scale3d(-1,-1,1);
		-webkit-clip-path: polygon(0 1%, 100% 1%, 100% 3%, 0 3%);
		clip-path: polygon(0 1%, 100% 1%, 100% 3%, 0 3%);
	}
	5% {
		-webkit-clip-path: polygon(0 10%, 100% 10%, 100% 9%, 0 9%);
		clip-path: polygon(0 10%, 100% 10%, 100% 9%, 0 9%);
	}
	11% {
		-webkit-clip-path: polygon(0 5%, 100% 5%, 100% 6%, 0 6%);
		clip-path: polygon(0 5%, 100% 5%, 100% 6%, 0 6%);
	}
	20% {
		-webkit-clip-path: polygon(0 20%, 100% 20%, 100% 20%, 0 20%);
		clip-path: polygon(0 20%, 100% 20%, 100% 20%, 0 20%);
	}
	25% {
		-webkit-clip-path: polygon(0 10%, 100% 10%, 100% 10%, 0 10%);
		clip-path: polygon(0 10%, 100% 10%, 100% 10%, 0 10%);
	}
	35% {
		-webkit-clip-path: polygon(0 30%, 100% 30%, 100% 25%, 0 25%);
		clip-path: polygon(0 30%, 100% 30%, 100% 25%, 0 25%);
	}
	42% {
		-webkit-clip-path: polygon(0 15%, 100% 15%, 100% 16%, 0 16%);
		clip-path: polygon(0 15%, 100% 15%, 100% 16%, 0 16%);
	}
	48% {
		-webkit-clip-path: polygon(0 40%, 100% 40%, 100% 39%, 0 39%);
		clip-path: polygon(0 40%, 100% 40%, 100% 39%, 0 39%);
	}
	50% {
		-webkit-clip-path: polygon(0 20%, 100% 20%, 100% 21%, 0 21%);
		clip-path: polygon(0 20%, 100% 20%, 100% 21%, 0 21%);
	}
	56% {
		-webkit-clip-path: polygon(0 60%, 100% 60%, 100% 55%, 0 55%);
		clip-path: polygon(0 60%, 100% 60%, 100% 55%, 0 55%);
	}
	61% {
		-webkit-clip-path: polygon(0 30%, 100% 30%, 100% 31%, 0 31%);
		clip-path: polygon(0 30%, 100% 30%, 100% 31%, 0 31%);
	}
	68% {
		-webkit-clip-path: polygon(0 70%, 100% 70%, 100% 69%, 0 69%);
		clip-path: polygon(0 70%, 100% 70%, 100% 69%, 0 69%);
	}
	72% {
		-webkit-clip-path: polygon(0 40%, 100% 40%, 100% 41%, 0 41%);
		clip-path: polygon(0 40%, 100% 40%, 100% 41%, 0 41%);
	}
	77% {
		-webkit-clip-path: polygon(0 80%, 100% 80%, 100% 75%, 0 75%);
		clip-path: polygon(0 80%, 100% 80%, 100% 75%, 0 75%);
	}
	81% {
		-webkit-clip-path: polygon(0 50%, 100% 50%, 100% 51%, 0 51%);
		clip-path: polygon(0 50%, 100% 50%, 100% 51%, 0 51%);
	}
	86% {
		-webkit-clip-path: polygon(0 90%, 100% 90%, 100% 90%, 0 90%);
		clip-path: polygon(0 90%, 100% 90%, 100% 90%, 0 90%);
	}
	90% {
		-webkit-clip-path: polygon(0 60%, 100% 60%, 100% 60%, 0 60%);
		clip-path: polygon(0 60%, 100% 60%, 100% 60%, 0 60%);
	}
	92% {
		-webkit-clip-path: polygon(0 100%, 100% 100%, 100% 99%, 0 99%);
		clip-path: polygon(0 100%, 100% 100%, 100% 99%, 0 99%);
	}
	94% {
		-webkit-clip-path: polygon(0 70%, 100% 70%, 100% 71%, 0 71%);
		clip-path: polygon(0 70%, 100% 70%, 100% 71%, 0 71%);
	}
}

@keyframes glitch-anim-text {
	0%, 100% { 
		transform: translate3d(2px, -2px, 0) scale3d(-1,-1,1);
		-webkit-clip-path: polygon(0 20%, 100% 20%, 100% 21%, 0 21%);
		clip-path: polygon(0 20%, 100% 20%, 100% 21%, 0 21%);
	}
	20% {
		-webkit-clip-path: polygon(0 33%, 100% 33%, 100% 33%, 0 33%);
		clip-path: polygon(0 33%, 100% 33%, 100% 33%, 0 33%);
	}
	41% {
		-webkit-clip-path: polygon(0 44%, 100% 44%, 100% 44%, 0 44%);
		clip-path: polygon(0 44%, 100% 44%, 100% 44%, 0 44%);
	}
	52% {
		-webkit-clip-path: polygon(0 50%, 100% 50%, 100% 20%, 0 20%);
		clip-path: polygon(0 50%, 100% 50%, 100% 20%, 0 20%);
	}
	61% {
		-webkit-clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
		clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
	}
	75% {
		-webkit-clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
		clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
	}
	80% {
		-webkit-clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
		clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
	}
	96% {
		-webkit-clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
		clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
	}
}

@keyframes glitch-anim-title {
	0%, 100% { 
		transform: translate3d(2px, -2px, 0);
		-webkit-clip-path: polygon(0 20%, 100% 20%, 100% 21%, 0 21%);
		clip-path: polygon(0 20%, 100% 20%, 100% 21%, 0 21%);
	}
	20% {
		-webkit-clip-path: polygon(0 33%, 100% 33%, 100% 33%, 0 33%);
		clip-path: polygon(0 33%, 100% 33%, 100% 33%, 0 33%);
	}
	41% {
		-webkit-clip-path: polygon(0 44%, 100% 44%, 100% 44%, 0 44%);
		clip-path: polygon(0 44%, 100% 44%, 100% 44%, 0 44%);
	}
	52% {
		-webkit-clip-path: polygon(0 50%, 100% 50%, 100% 20%, 0 20%);
		clip-path: polygon(0 50%, 100% 50%, 100% 20%, 0 20%);
	}
	61% {
		-webkit-clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
		clip-path: polygon(0 70%, 100% 70%, 100% 70%, 0 70%);
	}
	75% {
		-webkit-clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
		clip-path: polygon(0 80%, 100% 80%, 100% 80%, 0 80%);
	}
	80% {
		-webkit-clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
		clip-path: polygon(0 50%, 100% 50%, 100% 55%, 0 55%);
	}
	96% {
		-webkit-clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
		clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
	}
}

/* Flash */
@keyframes glitch-anim-flash {
	0%, 5% { 
		opacity: 0.2; 
		transform: translate3d(var(--gap-horizontal), var(--gap-vertical), 0);
	}
	5.5%, 100% {
		opacity: 0;
		transform: translate3d(0, 0, 0);
	}
}

/*
	End CSS for slider-glitch-slideshow
*/


/*
	Begin CSS for course-grid
*/

.course_grid_container .portfolio_classic_grid_wrapper .card__img {
  visibility: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 235px;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__price {
	position: absolute;
    top: 200px;
    right: 20px;
    font-size: 13px;
    display: inline-block;
    background: #0067DA;
    color: #fff;
    font-weight: 900;
    text-transform: uppercase;
    border-radius: 50%;
    width: 60px;
    text-align: center;
    line-height: 60px;
    z-index: 2;
    transition: all .21s cubic-bezier(.5,.5,.4,.9);
}

.course_grid_container .portfolio_classic_grid_wrapper:hover .card__price {
	-ms-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -o-transform: scale(1.2);
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
}

.course_grid_container .portfolio_classic_grid_wrapper .card__info-hover {
    position: absolute;
    padding: 16px;
  width: 100%;
  opacity: 0;
  top: 0;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__img--hover {
  transition: 0.2s all ease-out;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
  position: absolute;
    height: 235px;
top: 0;
  
}
.course_grid_container .portfolio_classic_grid_wrapper  {
  transition: all .2s cubic-bezier(0.175, 0.885, 0, 1);
  background-color: #fff;
  position: relative;
  overflow: hidden;
}

.course_grid_container .has_no_space .portfolio_classic_grid_wrapper {
	border-radius: 0 !important;
}

.course_grid_container .portfolio_classic_grid_wrapper:hover  {
	background-color: #000 !important;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__info {
	z-index: 2;
  background-color: #fff;
   padding: 25px 30px 25px 30px;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__title {
    margin-bottom: 10px;
    font-size: 20px;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__excerpt p {
	padding: 0 !important;
}

.course_grid_container .portfolio_classic_grid_wrapper:not(.no-hover-animation):hover .card__img--hover {
    height: 100% !important;
    opacity: 0.5;
}

.course_grid_container .portfolio_classic_grid_wrapper:not(.no-hover-animation):hover .card__info {
    background-color: transparent !important;
    position: relative;
    color: #fff !important;
}

.course_grid_container .portfolio_classic_grid_wrapper:not(.no-hover-animation):hover .card__info .card__meta,
.course_grid_container .portfolio_classic_grid_wrapper:not(.no-hover-animation):hover .card__info .card__date,
.course_grid_container .portfolio_classic_grid_wrapper:not(.no-hover-animation):hover .card__info .card__excerpt {
	color: #fff !important;
}

.course_grid_container .portfolio_classic_grid_wrapper:not(.no-hover-animation):hover a
{
	color: #fff !important;
}

.course_grid_container .portfolio_classic_grid_wrapper:not(.no-hover-animation):hover .card__info-hover {
    opacity: 1;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__info .card__date {
	font-size: 13px;
	font-weight: 700;
	letter-spacing: 2px;
	text-transform: uppercase;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__info .card__excerpt {
	margin-bottom: 15px;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__info .card__meta {
	margin-top: 15px;
	font-weight: 900;
	font-size: 13px;
	display: inline-block;
	margin-right: 20px;
}

.course_grid_container .portfolio_classic_grid_wrapper .card__info .card__meta:last-child {
	margin-right: 0;
}

body.elementor-page form.learn-press-search-course-form {
	margin-bottom: 60px;
}

body.elementor-page form.learn-press-search-course-form .lp-button {
	padding: .7em 2.5em .6em 2.5em;
	top: 0;
	border-radius: 0;
	-webkit-border-top-right-radius: 5px;
	-webkit-border-bottom-right-radius: 5px;
	-moz-border-radius-topright: 5px;
	-moz-border-radius-bottomright: 5px;
	border-top-right-radius: 5px;
	border-bottom-right-radius: 5px;
}

/*
	End CSS for course-grid
*/

/*
	Begin CSS for dotlife-music-player
*/

.music_player .player {
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  color: white;
  width: 100%;
  height: 580px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1), 0 40px 35px -20px rgba(0, 0, 0, 0.2), 0 40px 35px -40px rgba(0, 0, 0, 0.1);
  text-align: center;
  padding: 15px;
  margin: auto;
}
.music_player .player .player__background {
  position: absolute;
  top: -20px;
  left: -20px;
  bottom: -20px;
  right: -20px;
  background-size: cover;
  background-position: center top;
  filter: blur(8px);
  z-index: 0;
  transition: .5s;
}
.music_player .player .player__background:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: black;
  opacity: .3;
}
.music_player .player .player__img {
  width: 160px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2), 0 40px 35px -20px rgba(0, 0, 0, 0.3);
  transition: .5s;
  z-index: 1;
}
.music_player .player .player__title {
  margin-top: 20px;
  color: #fff;
  z-index: 1;
}
.music_player .player .player__artist {
  opacity: .8;
  font-size: .8em;
  letter-spacing: .2em;
  text-transform: uppercase;
  font-weight: 300;
  color: #fff;
  z-index: 1;
}
.music_player .player .player__controls {
  width: 80%;
  display: flex;
  justify-content: space-between;
  margin: 5px 10%;
  margin-top: 40px;
  z-index: 1;
}
.music_player .player .player__controls > a {
  cursor: pointer;
  color: white;
  outline: none;
  position: relative;
  transition: transform .3s;
}
.music_player .player .player__controls > a:hover {
	transform: scale(1.2);
}
.music_player .player .player__scrubber {
  width: 80%;
  margin: 30px 10% 5px;
  height: 2px;
  padding: 6px 0;
  position: relative;
  z-index: 1;
}
.music_player .player .player__scrubber:before {
  content: '';
  position: absolute;
  top: 5px;
  left: 0;
  height: 2px;
  width: 100%;
  background: white;
  z-index: 0;
}
.music_player .player .player__scrubber__fill {
  position: absolute;
  top: 5px;
  left: 0;
  height: 2px;
  width: 0%;
  background: black;
}
.music_player .player .player__scrubber__handle {
  position: absolute;
  top: -3px;
  left: 0%;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: white;
  margin-left: -8px;
  transition: transform .3s;
  cursor: pointer;
}
.music_player .player .player__scrubber__handle:hover {
  transform: scale(1.1);
}
.music_player .player .player__time {
  width: 80%;
  font-weight: 300;
  font-size: .8em;
  display: flex;
  justify-content: space-between;
  z-index: 1;
}

/*
	End CSS for dotlife-music-player
*/


/*
	Begin CSS for dotlife-mouse-driven-vertical-carousel
*/

.tg_mouse_driven_vertical_carousel_wrapper {
	overflow: hidden;
	width: 100%;
}

.tg_mouse_driven_vertical_carousel_wrapper .c-header {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper {
  position: relative;
  width: 100%;
  background-size: cover;
  background-position: center;
  height: 0;
  height: 100%;
}

.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .carousel__header {
	position: absolute;
	top: 45%;
	left: 90px;
	z-index: 9;
}

.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .carousel__header h2 {
	color: #fff;
}

.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .c-gradient-overlay {
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 49% 50%, rgba(2, 21, 25, 0.3), rgba(2, 21, 25, 0.6));
}

.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .c-mouse-vertical-carousel {
  position: relative;
}
.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .c-mouse-vertical-carousel__list {
  width: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  z-index: 1;
  list-style: none;
  
}
.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .c-mouse-vertical-carousel__list-item a {
  display: block;
  padding-bottom: 25px;
  padding-top: 25px;
  padding-left: 54.1666666667%;
  padding-right: 10%;
}
.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .c-mouse-vertical-carousel__eyebrow {
  position: relative;
  color: #fff;
  padding: 0 !important;
}
.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .c-mouse-vertical-carousel__title {
  max-width: 100%;
  color: #fff;
  padding: 0 !important;
  font-size: 26px;
  font-weight: 700;
}
.tg_mouse_driven_vertical_carousel_wrapper .u-media-wrapper .c-mouse-vertical-carousel__bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  will-change: transform;
}


/*
	End CSS for dotlife-mouse-driven-vertical-carousel
*/


/*
	Begin CSS for dotlife-slider-synchronized-carousel
*/

.tg_synchronized_carousel_slider_wrapper.sliders-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images {
  position: relative;
  overflow: hidden;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images.ms-container--horizontal {
  width: 100%;
  height: 400px;
  max-width: 100%;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images.ms-container--horizontal .ms-track {
  left: calc(50% - 350px);
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images.ms-container--horizontal .ms-slide {
  display: inline-flex;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images .ms-track {
  display: flex;
  position: absolute;
  white-space: nowrap;
  padding: 0;
  margin: 0;
  list-style: none;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images .ms-slide {
  align-items: center;
  justify-content: center;
  width: 700px;
  height: 400px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images {
  left: calc(50% - 280px);
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images.ms-container--horizontal .ms-track {
  left: -70px;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images .ms-slide__image-container {
  width: 80%;
  height: 80%;
  background-color: rgba(0, 0, 0, 0.3);
  overflow: hidden;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images .ms-slide__image {
  width: 100%;
  height: 100%;
  background-size: cover;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers {
  position: relative;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers.ms-container--horizontal {
  width: 240px;
  height: 240px;
  max-width: 100%;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers.ms-container--horizontal .ms-track {
  left: calc(50% - 120px);
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers.ms-container--horizontal .ms-slide {
  display: inline-flex;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers .ms-track {
  display: flex;
  position: absolute;
  white-space: nowrap;
  padding: 0;
  margin: 0;
  list-style: none;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers .ms-slide {
  align-items: center;
  justify-content: center;
  width: 240px;
  height: 240px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers {
  position: absolute;
  left: calc(50% - 420px);
  top: calc(50% - 280px);
  z-index: -1;
  pointer-events: none;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers .ms-slide {
  font-size: 100px;
  font-weight: 900;
  color: #ccc;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles {
  position: relative;
  overflow: hidden;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles.ms-container--vertical {
  width: 400px;
  min-height: 170px;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles.ms-container--vertical .ms-track {
  flex-direction: column;
  top: calc(50% - 85px);
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles.ms-container--vertical.ms-container--reverse .ms-track {
  flex-direction: column-reverse;
  top: auto;
  bottom: calc(50% - 85px);
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles.ms-container--vertical .ms-slide {
  display: flex;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles .ms-track {
  display: flex;
  position: absolute;
  white-space: nowrap;
  padding: 0;
  margin: 0;
  list-style: none;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles .ms-slide {
  align-items: center;
  justify-content: center;
  width: 400px;
  height: 170px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles {
  position: absolute;
  left: calc(50% - 420px);
  top: calc(50% - 120px);
  z-index: 1;
  pointer-events: none;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles .ms-track {
  white-space: normal;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles .ms-slide {
  font-size: 3.3em;
  font-weight: 600;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles .ms-slide h3 {
  margin: 0;
  font-size: 36px;
  font-weight: 700;
  line-height: 1.4em;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links {
  position: relative;
  overflow: hidden;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links.ms-container--vertical {
  width: 400px;
  min-height: 60px;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links.ms-container--vertical .ms-track {
  flex-direction: column;
  top: calc(50% - 10px);
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links.ms-container--vertical .ms-slide {
  display: flex;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links .ms-track {
  display: flex;
  position: absolute;
  white-space: nowrap;
  padding: 0;
  margin: 0;
  list-style: none;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links .ms-slide {
  align-items: left;
  justify-content: left;
  min-width: 300px;
  min-height: 90px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links {
  position: absolute;
  left: calc(50% - 420px);
  top: calc(50% + 85px);
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 13px;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links .ms-track {
  white-space: normal;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links .ms-slide__link {
  cursor: pointer;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .tg_synchronized_carousel_pagination {
  display: flex;
  position: absolute;
  left: calc(50% - 420px);
  top: calc(100%);
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
  z-index: 1;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .pagination__button {
  display: inline-block;
  position: relative;
  width: 36px;
  height: 20px;
  margin: 0 5px;
  cursor: pointer;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .pagination__button:before, .pagination__button:after {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 1px);
  width: 100%;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .pagination__button:before {
  height: 2px;
  background-color: #ccc;
}
.tg_synchronized_carousel_slider_wrapper.sliders-container .pagination__button:after {
  height: 2px;
  background-color: #0067DA;
  opacity: 0;
  transition: 0.5s opacity;
}

.tg_synchronized_carousel_slider_wrapper.sliders-container .pagination__item--active .pagination__button:after {
  opacity: 1;
}

@media screen and (max-width: 860px) {
  .tg_synchronized_carousel_slider_wrapper.sliders-container .ms--numbers {
    left: calc(50% - 120px);
  }

  .tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles {
    left: calc(50% - 200px);
    top: calc(50% - 135px);
    text-align: center;
  }

  .tg_synchronized_carousel_slider_wrapper.sliders-container .ms--links {
    left: calc(50% - 60px);
    top: calc(50% + 80px);
  }

  .tg_synchronized_carousel_slider_wrapper.sliders-container .tg_synchronized_carousel_pagination {
    left: 50%;
    top: calc(100%);
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
}
@media screen and (max-width: 600px) {
  .tg_synchronized_carousel_slider_wrapper.sliders-container .ms--images {
    overflow: visible;
  }
}
@media screen and (max-width: 400px) {
  .tg_synchronized_carousel_slider_wrapper.sliders-container .ms--titles .ms-slide {
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
  }
}

/*
	End CSS for dotlife-slider-synchronized-carousel
*/


/*
	Begin CSS for dotlife-flip-box
*/

.tg_flip_box_wrapper.square-flip{
	-webkit-perspective: 1000;
	-moz-perspective: 1000;
	-ms-perspective: 1000;
	perspective: 1000;

	-webkit-transform: perspective(1000px);
	-moz-transform: perspective(1000px);
  -ms-transform: perspective(1000px);
  transform: perspective(1000px);

  -moz-transform-style: preserve-3d; 
  
  position:relative;
}

.tg_flip_box_wrapper.square-flip {
	width:100%;
	height:400px;
}
.tg_flip_box_wrapper.square-flip .square,.square2{
  	width:100%;
  	height:100%;
  }
.tg_flip_box_wrapper.square-flip .square{

  	background-size: cover;
  	background-position:center center;

  	-ms-transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	    transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	    -webkit-transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	overflow: hidden;

  	position:absolute;
  	top:0;


  	-webkit-backface-visibility: hidden; 
  	backface-visibility: hidden;
  }
.tg_flip_box_wrapper.square-flip  .square{
  	-webkit-transform: rotateY(0deg);
  	-moz-transform: rotateY(0deg);
  	-o-transform: rotateY(0deg);
  	-ms-transform: rotateY(0deg);
  	transform: rotateY(0deg);
  	transform-style: preserve-3d;
  	z-index:1;
  }
.tg_flip_box_wrapper.square-flip:hover .square{
  	-webkit-transform: rotateY(-180deg);
  	-moz-transform: rotateY(-180deg);
  	-o-transform: rotateY(-180deg);
  	-ms-transform: rotateY(-180deg);
  	transform: rotateY(-180deg);
  	transform-style: preserve-3d;
  }


  .tg_flip_box_wrapper.square-flip .square2{

  	background-size: cover;
  	background-position:center center;

  	-ms-transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	    transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	    -webkit-transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	overflow: hidden;

  	position:absolute;
  	top:0;

  	-webkit-backface-visibility: hidden; 
  	backface-visibility: hidden;
  }
.tg_flip_box_wrapper.square-flip  .square2{
  	-webkit-transform: rotateY(180deg);
  	-moz-transform: rotateY(180deg);
  	-o-transform: rotateY(180deg);
  	-ms-transform: rotateY(180deg);
  	transform: rotateY(180deg);
  	transform-style: preserve-3d;
  	z-index:1;
  }
.tg_flip_box_wrapper.square-flip:hover .square2{
  	-webkit-transform: rotateY(0deg);
  	-moz-transform: rotateY(0deg);
  	-o-transform: rotateY(0deg);
  	-ms-transform: rotateY(0deg);
  	transform: rotateY(0deg);
  	transform-style: preserve-3d;
  }

  /*Square content*/
.tg_flip_box_wrapper.square-flip .square-container{
  	padding:40px;
  	text-align:center;
  	position:relative;
  	top:50%;

  	-ms-transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	    transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	    -webkit-transition: transform 0.60s cubic-bezier(.5,.3,.3,1);

  
  	-webkit-transform: translateY(-50%) translateX(0px)  scale(1);
  	-ms-transform: translateY(-50%) translateX(0px)  scale(1);
ansform: translateY(-50%) translateX(0px)  scale(1);
  	transform-style: preserve-3d;
  	z-index:2;
  }
  
.tg_flip_box_wrapper.square-flip:hover .square-container{
  	
  	-webkit-transform: translateY(-50%) translateX(-650px)  scale(.88);
  	-ms-transform: translateY(-50%) translateX(-650px)  scale(.88);
transform: translateY(-50%) translateX(-650px)  scale(.88);
  	transform-style: preserve-3d;

  }

.tg_flip_box_wrapper.square-flip .square-container2{
  	padding:40px;
  	text-align:center;
  	position:relative;
  	top:50%;

  	-ms-transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	    transition: transform 0.60s cubic-bezier(.5,.3,.3,1);
  	    -webkit-transition: transform 0.60s cubic-bezier(.5,.3,.3,1);

  	
  	-webkit-transform: translateY(-50%) translateX(650px) translateZ(60px) scale(.88);
  	-ms-transform: translateY(-50%) translateX(650px) translateZ(60px) scale(.88);
transform: translateY(-50%) translateX(650px) translateZ(60px) scale(.88);

  	transform-style: preserve-3d;
  	z-index:2;
  }
.tg_flip_box_wrapper.square-flip:hover .square-container2{
  	
  	-webkit-transform: translateY(-50%) translateX(0px) translateZ(0px) scale(1);
  	-ms-transform: translateY(-50%) translateX(0px) translateZ(0px) scale(1);
  	transform: translateY(-50%) translateX(0px) translateZ(0px) scale(1);
transform-style: preserve-3d;
  }
  
.tg_flip_box_wrapper.square-flip .flip-overlay {
    display: block;
    background: rgba(0,0,0,0.4);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
}

.tg_flip_box_wrapper.square-flip .square-container2 .button {
	margin-top: 20px;
}

/*
	End CSS for dotlife-flip-box
*/


/*
	Begin CSS for dotlife-slider-zoom
*/

.slider_zoom_wrapper .pagination {
  position: absolute !important;
  width: 100%;
  text-align: center;
  right: 0;
  padding: 0 !important;
  bottom: 40px;
  z-index: 999;
  margin: 0 !important;
}
.slider_zoom_wrapper .pagination__item {
  cursor: pointer;
  display: inline-block;
  white-space: nowrap;
  font-size: 0;
  width: 10px;
  height: 10px;
  border-radius: 250px;
  border: 1px solid #fff;
  margin: 0 5px;
  transition: .2s ease-in-out;
  background: transparent;
}
.slider_zoom_wrapper .pagination__item.is-current, 
.slider_zoom_wrapper .pagination__item:hover {
  background-color: #fff;
}

.slider_zoom_wrapper .container {
  	width: 960px;
	margin: auto;
}
@media (max-width: 699px) {
  .slider_zoom_wrapper .container {
    padding-right: 40px;
    padding-left: 40px;
  }
}
@media only screen and (min-width: 1200px) {
  .slider_zoom_wrapper .container {
    max-width: 1425px;
	width: 100%;
	padding: 0 90px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 960px) {
  .slider_zoom_wrapper .container {
	width: 100%;
	padding: 0 30px;
  }
}

@media only screen and (max-width: 767px) {
  .slider_zoom_wrapper .container {
	width: 100%;
	padding: 0 30px;
  }	
}

.background-absolute {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-position: center;
  background-size: cover;
}

.slider_zoom_wrapper {
  position: relative;
  color: #ffffff;
  background-color: #1e1e22;
  overflow: hidden;
  height: 900px;
}
.slideshow__slide {
  visibility: hidden;
  transition: visibility 0s 1.7s;
}
.slideshow__slide.is-current {
  visibility: visible;
  transition-delay: 0s;
}
@media (max-width: 699px) {
  .slider_zoom_wrapper .slideshow__slide {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
@media (min-width: 700px) {
  .slider_zoom_wrapper .slideshow__slide {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}

.slideshow__slide-background-load-wrap {
  transition: -webkit-transform 0.9s cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.9s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  overflow: hidden;
}

.is-loaded .slideshow__slide-background-load-wrap {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  transition-delay: 0s;
}

.slideshow__slide.is-prev .slideshow__slide-background-parallax,
.slideshow__slide.is-next .slideshow__slide-background-parallax {
  -webkit-transform: none !important;
          transform: none !important;
}

.slideshow__slide.is-prev-section .slideshow__slide-background-parallax,
.slideshow__slide.is-next-section .slideshow__slide-background-parallax {
  -webkit-transform: none !important;
          transform: none !important;
}

.slideshow__slide-background-load {
  transition: -webkit-transform 0.9s cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.9s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transform: translate3d(0, -50%, 0);
          transform: translate3d(0, -50%, 0);
}

.is-loaded .slideshow__slide-background-load {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}

.slideshow__slide-background-wrap {
  transition: -webkit-transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.5s;
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.5s;
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.5s, -webkit-transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.5s;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slideshow__slide.is-prev .slideshow__slide-background-wrap {
  -webkit-transform: translate3d(0, -100%, 0);
  transform: translate3d(0, -100%, 0);
}

.slideshow__slide.is-next .slideshow__slide-background-wrap {
  -webkit-transform: translate3d(0, 100%, 0);
  transform: translate3d(0, 100%, 0);
}

.slideshow__slide.is-prev-section .slideshow__slide-background-wrap {
  -webkit-transform: translate3d(0, -100%, 0);
  transform: translate3d(0, -100%, 0);
  transition: none;
}

.slideshow__slide.is-next-section .slideshow__slide-background-wrap {
  -webkit-transform: translate3d(0, 100%, 0);
  transform: translate3d(0, 100%, 0);
  transition: none;
}

.slideshow__slide-background {
  transition: -webkit-transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s, -webkit-transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s;
  -webkit-transform: scale(1);
          transform: scale(1);
  overflow: hidden;
}

.slideshow__slide.is-prev .slideshow__slide-background, .slideshow__slide.is-next .slideshow__slide-background {
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  transition-delay: 0s;
}

.slideshow__slide.is-prev-section .slideshow__slide-background, .slideshow__slide.is-next-section .slideshow__slide-background {
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  transition-delay: 0s;
  transition: none;
}

.slideshow__slide-image-wrap {
  transition: -webkit-transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.6s, -webkit-transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}

.slideshow__slide.is-prev .slideshow__slide-image-wrap {
  -webkit-transform: translate3d(0, 50%, 0);
          transform: translate3d(0, 50%, 0);
}

.slideshow__slide-image {
  transition: -webkit-transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s, -webkit-transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.5s;
  -webkit-transform: scale(1);
          transform: scale(1);
}

.slideshow__slide.is-prev .slideshow__slide-image, .slideshow__slide.is-next .slideshow__slide-image {
  -webkit-transform: scale(1.25);
          transform: scale(1.25);
  transition-delay: 0s;
}

.slideshow__slide.is-prev-section .slideshow__slide-image, .slideshow__slide.is-next-section .slideshow__slide-image {
  -webkit-transform: scale(1.25);
          transform: scale(1.25);
  transition-delay: 0s;
  transition: none;
}

.slideshow__slide-image::before, .slideshow__slide-image::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0.35;
}

.slider_zoom_wrapper .slideshow__slide-image::before {
  background-color: #1e1e22;
}

.slideshow__slide.is-prev .slideshow_container,
.slideshow__slide.is-next .slideshow_container {
  -webkit-transform: none !important;
          transform: none !important;
}

.slideshow__slide.is-prev-section .slideshow_container,
.slideshow__slide.is-next-section .slideshow_container {
  -webkit-transform: none !important;
          transform: none !important;
}

.slideshow__slide-caption-text {
  position: relative;
  height: 100%;
  padding-top: 30%;
  transition: -webkit-transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.4s, -webkit-transform 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}

.slideshow__slide.is-prev .slideshow__slide-caption-text {
  -webkit-transform: translate3d(0, -100%, 0);
          transform: translate3d(0, -100%, 0);
}

.slideshow__slide.is-next .slideshow__slide-caption-text {
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
}

.slideshow__slide.is-prev-section .slideshow__slide-caption-text {
  -webkit-transform: translate3d(0, -100%, 0);
          transform: translate3d(0, -100%, 0);
  transition: none;
}

.slideshow__slide.is-next-section .slideshow__slide-caption-text {
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  transition: none;
}

.slideshow__slide-caption {
  position: relative;
  height: 100%;
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  transition: -webkit-transform 1s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
  transition: transform 1s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
  transition: transform 1s cubic-bezier(0.4, 0, 0.2, 1) 0.1s, -webkit-transform 1s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
}

.is-loaded .slideshow__slide-caption {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}

.slideshow__slide-caption-title {
  line-height: 1;
  color: #fff;
}
@media (max-height: 500px) {
  .slideshow__slide-caption-title {
    margin-bottom: 0 !important;
  }
}

@media (max-width: 699px) {
  .slideshow.-full .slideshow__slide-caption-title {
    margin-bottom: 30px;
  }
}
@media (min-width: 700px) {
  .slideshow__slide-caption-title {
    font-size: 3.5rem;
    margin-bottom: 1.25rem;
  }
}
@media (min-width: 700px) and (max-width: 749px) {
  .slideshow__slide-caption-title {
    font-size: 4.375rem;
  }
}
@media (min-width: 1600px) {
  .slideshow__slide-caption-title {
    font-size: 6.25rem;
  }
}
.slideshow__slide-caption-title.-full {
  width: 100%;
}

.slideshow__slide-caption-subtitle {
  display: inline-block;
  padding: 1.875rem 0;
}

.slideshow__slide-caption-subtitle.-load {
  transition: -webkit-transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s, -webkit-transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  -webkit-transform: translate3d(0, 3.75rem, 0);
  transform: translate3d(0, 3.75rem, 0);
}

.is-loaded .slideshow__slide-caption-subtitle.-load {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

body[data-route-option="prev-section"] .slideshow__slide-caption-subtitle.-load, body[data-route-option="next-section"] .slideshow__slide-caption-subtitle.-load {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slideshow__slide-caption-subtitle-label {
  transition: -webkit-transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  display: inline-block;
}

.o-hsub.-link:hover .slideshow__slide-caption-subtitle-label, .o-hsub-wrap:hover .slideshow__slide-caption-subtitle-label {
  -webkit-transform: translateX(20px);
          transform: translateX(20px);
}

/* OLD */
.c-header-home_heading {
  line-height: 1;
}

@media (max-height: 500px) {
  .c-header-home_heading {
    margin-bottom: 0 !important;
  }
}
@media (max-width: 699px) {
  .c-header-home_heading {
    font-size: 40px;
    margin-bottom: 150px;
  }

  .c-header-home.-full .c-header-home_heading {
    margin-bottom: 30px;
  }
}
@media (min-width: 700px) {
  .c-header-home_heading {
    font-size: 5.625rem;
    margin-bottom: 1.25rem;
  }
}
@media (min-width: 700px) and (max-width: 749px) {
  .c-header-home_heading {
    font-size: 4.375rem;
  }
}
@media (min-width: 1600px) {
  .c-header-home_heading {
    font-size: 6.25rem;
  }
}
.c-header-home_heading.-full {
  width: 100%;
}

.c-header-home_subheading {
  display: inline-block;
  padding: 1.875rem 0;
}

.c-header-home_subheading.-load {
  transition: -webkit-transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transition: transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s, -webkit-transform 0.9s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  -webkit-transform: translate3d(0, 3.75rem, 0);
          transform: translate3d(0, 3.75rem, 0);
}

.is-loaded .c-header-home_subheading.-load {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}

body[data-route-option="prev-section"] .c-header-home_subheading.-load, body[data-route-option="next-section"] .c-header-home_subheading.-load {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}

.o-hsub {
  font-size: 0.75rem;
  padding: 1.25rem 0;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.12em;
  font-weight: 500;
}

.o-hsub::before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  border-bottom: 1px solid;
  width: 1.5rem;
  background-color: #1e1e22;
  margin-right: 1.125rem;
  transition: -webkit-transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transform-origin: center left;
          transform-origin: center left;
}

.o-hsub.-link {
  color: #ffffff;
  text-decoration: none;
}

.o-hsub.-link:hover::before, .o-hsub-wrap:hover .o-hsub.-link::before {
  -webkit-transform: scaleX(1.5);
          transform: scaleX(1.5);
}


/*
	End CSS for dotlife-slider-zoom
*/


/*
	Begin CSS for dotlife-animated-text
*/

.themegoods-animated-text span.blast {
	transform: translateX(101%) translateY(0px) translateZ(0px);
	transition: all 800ms ease;
	opacity: 0;
	display: inline-block;
	transition-delay: 1000ms;
}

.themegoods-animated-text.overflow-hidden > * > span:not(.blast) {
	overflow: hidden;
	display: inline-block;
}

.themegoods-animated-text.overflow-hidden h1,
.themegoods-animated-text.overflow-hidden h2,
.themegoods-animated-text.overflow-hidden h3,
.themegoods-animated-text.overflow-hidden h4,
.themegoods-animated-text.overflow-hidden h5,
.themegoods-animated-text.overflow-hidden h6,
.themegoods-animated-text.overflow-hidden h7,
.themegoods-animated-text.overflow-hidden div,
.themegoods-animated-text.overflow-hidden > span:not(.blast) {
	overflow: hidden;
	display: inline-block;
}

.themegoods-animated-text.transition-right span.blast {
	transform: translateX(101%) translateY(0px) translateZ(0px);
}

.themegoods-animated-text.transition-left span.blast {
	transform: translateX(-101%) translateY(0px) translateZ(0px);
}

.themegoods-animated-text.transition-top span.blast {
	transform: translateX(0px) translateY(-101%) translateZ(0px);
}

.themegoods-animated-text.transition-bottom span.blast {
	transform: translateX(0px) translateY(101%) translateZ(0px);
}

.themegoods-animated-text.transition-zoomin span.blast {
	transform: scaleX(1.2) scaleY(1.2);
}

.themegoods-animated-text.transition-zoomout span.blast {
	transform: scaleX(0.8) scaleY(0.8);
}

.themegoods-animated-text span.blast.visible {
	opacity: 1;
	transform: translateX(0px) translateY(0px) translateZ(0px);
}

.themegoods-animated-text.overflow-hidden span.blast {
	opacity: 1;
}

/*
	End CSS for dotlife-animated-text
*/

/*
	Begin CSS for dotlife-service-grid
*/

.service-grid-wrapper
{
	background-size: cover;
	background-position: center center;
	overflow: hidden;
	position: relative;
	display: flex;
}

.service-grid-wrapper:hover
{
    box-shadow: 0 25px 55px rgba(0,0,0,.22);
}

.service-grid-wrapper:before 
{
	position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 85%;
    display: block;
    z-index: 1;
    content: '';
    background: linear-gradient(to bottom,rgba(15,15,15,0),rgba(15,15,15,.75) 100%);
    transition: opacity .65s cubic-bezier(.05,.2,.1,1);
    opacity: 0;
}

.service-grid-wrapper:hover:before
{
	opacity: 1;
}

.service-grid-wrapper .inner-wrap
{
	display: flex;
	position: relative;
	width: 100%;
	padding: 20px;
	box-sizing: border-box;
}

.service-grid-wrapper .inner-wrap .inner-wrap-border
{
	display: flex;
	position: relative;
	width: 100%;
	border: 1px solid #fff;
	box-sizing: border-box;
	z-index: 1;
}

.service-grid-wrapper .inner-wrap:before
{
	position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 85%;
    display: block;
    z-index: 1;
    content: '';
	background: linear-gradient(to bottom,rgba(15,15,15,0),rgba(15,15,15,.75) 100%);
    transition: opacity .65s cubic-bezier(.05,.2,.1,1);
}

.service-grid-wrapper:hover .inner-wrap:before
{
	opacity: 0;
}

.service-grid-wrapper .overflow-inner
{
	padding: 0;
    width: 100%;
    align-self: flex-end;
    padding: 10px 20px 10px 20px;
}

.service-grid-wrapper .overflow-inner .header-wrap
{
	position: relative;
	z-index: 2;
	transition: opacity .65s cubic-bezier(.05,.2,.1,1),transform .65s cubic-bezier(.05,.2,.1,1);
}

.service-grid-wrapper .overflow-inner .header-wrap i
{
	font-size: 30px;
	color: #fff;
}

.service-grid-wrapper .overflow-inner .header-wrap.hover
{
	transform: translateY(-90px);
}

.service-grid-wrapper .overflow-inner .header-wrap h2
{
	color: #fff;
	margin-bottom: 10px;
}

.service-grid-wrapper .overflow-inner .hover-content 
{
	color: #fff;
	opacity: 0;
    position: absolute;
    z-index: 2;
    bottom: 0;
    left: 20px;
    transform: translateY(20px);
    width: calc(100% - 40px);
    
	transition: opacity .18s cubic-bezier(.05,.2,.1,1),transform .18s cubic-bezier(.05,.2,.1,1);
}

.service-grid-wrapper:hover .overflow-inner .hover-content 
{
	opacity: 1;
    transform: translateY(0%);
    transition: opacity .65s cubic-bezier(.05,.2,.1,1) 0.15s,transform .65s cubic-bezier(.05,.2,.1,1) 0.15s;
}

.portfolio-classic-content-wrapper.has-no-space img
{
	max-width: none;
	width: 100%;
}

.service-grid-content-wrapper.has-no-space .tg_two_cols
{
	width: 50%;
	margin: 0;
}

.service-grid-content-wrapper.has-no-space .tg_three_cols
{
	width: 33.33%;
	margin: 0;
}

.service-grid-content-wrapper.has-no-space .tg_four_cols
{
	width: 25%;
	margin: 0;
}

.service-grid-content-wrapper.has-no-space .tg_five_cols
{
	width: 20%;
	margin: 0;
}

/*
	End CSS for dotlife-service-grid
*/

/*
	Begin CSS for dotlife-testimonial-card
*/

.testimonials-carousel-wrapper {
  width: 100%;
}

.testimonials-carousel-wrapper .owl-carousel .shadow-effect {
	background: #fff;
	padding: 40px;
	text-align: center;
	border-radius: 5px;
	box-shadow: 0 35px 60px rgba(0,0,0,0.10);
}

.testimonials-carousel-wrapper .owl-carousel  {
	-webkit-transform: scale3d(1.2, 1.2, 1);
    transform: scale3d(1.2, 1.2, 1);
}

.testimonials-carousel-wrapper .owl-carousel .item.active.center {
	box-shadow: 0 35px 60px rgba(0,0,0,0.10);
}
.testimonials-carousel-wrapper .owl-carousel .item {
    text-align: center;
    margin-top:80px;
	margin-bottom:80px;
    opacity: .2;
    -webkit-transform: scale3d(0.9, 0.9, 1);
    transform: scale3d(0.9, 0.9, 1);
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
.testimonials-carousel-wrapper .owl-carousel .owl-item.active.center .item {
    opacity: 1;
    -webkit-transform: scale3d(1.0, 1.0, 1);
    transform: scale3d(1.0, 1.0, 1);
}
.testimonials-carousel-wrapper .owl-carousel .owl-item img {
    -webkit-transform-style: preserve-3d;
            transform-style: preserve-3d;
    max-width: 40px !important;
    border-radius: 50px;
    margin-right: 10px;
}
.testimonials-carousel-wrapper .owl-carousel .owl-dots .owl-dot.active span {
    background: #3190E7;
    -webkit-transform: translate3d(0px, -50%, 0px) scale(0.7);
            transform: translate3d(0px, -50%, 0px) scale(0.7);
}
.testimonials-carousel-wrapper .owl-carousel .owl-dots{
	display: inline-block;
	width: 100%;
	text-align: center;
}
.testimonials-carousel-wrapper .owl-carousel .owl-dots .owl-dot{
	display: inline-block;
}
.testimonials-carousel-wrapper .owl-carousel .owl-dots .owl-dot span {
    background: #3190E7;
    display: inline-block;
    height: 20px;
    border-radius: 250px;
    margin: 0 2px 5px;
    -webkit-transform: translate3d(0px, -50%, 0px) scale(0.3);
            transform: translate3d(0px, -50%, 0px) scale(0.3);
    -webkit-transform-origin: 50% 50% 0;
            transform-origin: 50% 50% 0;
    -webkit-transition: all 250ms ease-out 0s;
    transition: all 250ms ease-out 0s;
    width: 20px;
}

.testimonials-carousel-wrapper .owl-carousel .shadow-effect .testimonial-info-img,
.testimonials-carousel-wrapper .owl-carousel .testimonial-name {
	display: inline-block;
}

.testimonials-carousel-wrapper .owl-carousel .shadow-effect .testimonial-info-desc {
	margin-bottom: 20px;
}

.testimonials-carousel-wrapper .owl-carousel .shadow-effect .testimonial-info-img {
	vertical-align: middle;
}

.testimonials-carousel-wrapper .owl-carousel .owl-dots {
	transform: translateY(-20px);
}

/*
	End CSS for dotlife-testimonial-card
*/

/*
	Begin CSS for dotlife-search
*/

body.elementor-search-activate .parallax-mirror
{
	z-index: 0 !important;
}

.dotlife-search-icon a
{
	display: block;
}

.dotlife-search-wrapper
{
	background: rgba(0,0,0,0.9);
	opacity: 0;
	position: fixed;
	width: 100vw;
	height: 100vh;
	visibility: hidden;
	display: table;
	top: 0;
	left: 0;
	transform: scale(0.9);
	z-index: -1;
	
    -webkit-transition: all 250ms ease;
    transition: all 250ms ease;
}

.dotlife-search-wrapper.active
{
	transform: scale(1);
	z-index: 9;
	opacity: 1;
	visibility: visible;
}

.dotlife-search-inner
{
	display: table-cell;
	vertical-align: middle;
	text-align: center;
}

.dotlife-search-inner.touch
{
	padding-top: 80px;
    vertical-align: top;
}

.dotlife-search-wrapper .input-group
{
	position: relative;
    display: inline-block;
    padding-bottom: 5px;
    border-bottom: 3px solid rgba(256,256,256,0.1);
}

.dotlife-search-wrapper .input-group input
{
	-webkit-appearance: none;
	display: inline-block;
	padding: 15px 20px 15px 0;
	box-shadow: none;
	outline: none;
	border: 0;
	background: transparent;
	-webkit-transition: all 350ms ease;
    transition: all 350ms ease;
    position: relative;
    top: 1px;
    color: #fff;
    font-size: 36px;
}

.dotlife-search-wrapper .input-group input::placeholder
{
	color: #fff;
}

.dotlife-search-wrapper .input-group .input-group-button
{
	position: relative;
    white-space: nowrap;
}

.dotlife-search-wrapper .input-group .input-group-button button
{
	position: relative;
    white-space: nowrap;
    margin-left: -1px;
    z-index: 2;
    display: inline-block;
	text-align: center;
    vertical-align: middle;
    -webkit-appearance: button;
    border: 0;
    background: transparent;
    cursor: pointer;
    outline: none;
    border-radius: 0;
    padding: 10px 0 0 20px;
    
    -webkit-transition: all 350ms ease;
    transition: all 350ms ease;
}

.dotlife-search-wrapper .input-group .input-group-button button i
{
	font-size: 26px;
	line-height: 0;
}

.autocomplete
{
    display: none;
    z-index:1001;
	text-align: left;
	overflow: hidden;
}

.autocomplete.visible:empty
{
	display: none;
}

.autocomplete.visible
{
	display: inline-block;
}

@-moz-document url-prefix() { 
  .autocomplete {
     margin-top: 20px;
  }
}

.autocomplete ul 
{
    margin: 0;
    padding: 20px 0 20px 0;
    border: 0;
    float: left;
    list-style: none;
    width: 100%;
    box-shadow: 0 1px 20px rgba(0, 0, 0, 0.1);
}

.autocomplete li
{
    display:block;
    clear:both;
    float: left;
    width: 100%;
    box-sizing: border-box;
}

.autocomplete li:hover a
{
	background: #f0f0f0;
}

.autocomplete li:first-child a
{
	margin-top: 5px;
}

.autocomplete li:last-child a
{
	margin-top: 5px;
}

.autocomplete li a
{
	display: block;
	float: left;
	width: 100%;
	padding: 10px 20px 10px 20px;
	box-sizing: border-box;
}

/*
	End CSS for dotlife-search
*/

/*
	Begin CSS for dotlife-pricing-table
*/

.pricing-plan-switch-wrap {
	width: 100%;
	text-align: center;
	margin-bottom: 60px;
}

.pricing-plan-unit-year.hide,
.pricing-plan-unit-month.hide {
	display: none;
}

.pricing-table-wrapper {
	box-sizing: border-box;
}

.pricing-table-wrapper.smoove {
	opacity: 0;
}

.pricing-table-wrapper .inner-wrap {
	border: 1px solid transparent;
	box-shadow: 0 35px 60px rgba(0,0,0,.1);
	padding: 60px;
	box-sizing: border-box;
	transition: all .6s cubic-bezier(.23, 1, .32, 1);
	border-radius: 5px;
	box-shadow: 0 0 0 rgba(0, 0, 0, .25);
}

.pricing-table-wrapper.featured-pricing-plan .inner-wrap {
	border: 1px solid #0077FF;
	box-shadow: 0 35px 60px rgba(0,0,0,.1);
}

.pricing-table-wrapper:hover .inner-wrap  {
	transform: translateY(-2px);
	border: 1px solid #0077FF;
    box-shadow: 0 10px 30px rgba(0, 0, 0, .12);
}

.pricing-table-wrapper h2.pricing-plan-title {
	font-size: 22px;
}

.pricing-plan-price-wrap h3.pricing-plan-price {
	font-size: 65px;
	display: inline;
}

#page_content_wrapper .pricing-plan-content ul.pricing-plan-content-list {
	list-style: none;
	margin-bottom: 30px;
}

#page_content_wrapper .pricing-plan-content ul.pricing-plan-content-list li:before {
	position: relative;
	font-family: 'Font Awesome 5 Free';
	content: "\f00c";
	font-weight: 900;
	margin-right: 15px;
}

.pricing-plan-content ul.pricing-plan-content-list li:before {
	color: #0077FF;
}

.pricing-plan-switch-year {
	margin-left: 10px;
}

.pricing-plan-switch-month {
	margin-right: 10px;
}

/*
	End CSS for dotlife-pricing-table
*/