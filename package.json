{"private": true, "scripts": {"dev": "vite", "build": "vite build && vite build --ssr"}, "devDependencies": {"@babel/preset-typescript": "^7.23.3", "@headlessui/react": "^1.4.2", "@hello-pangea/dnd": "^16.5.0", "@inertiajs/react": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^3.0.0", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.12", "axios": "^1.1.2", "laravel-vite-plugin": "^0.7.0", "lodash": "^4.17.19", "postcss": "^8.4.18", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.3", "tailwindcss-animated": "^1.0.1", "typescript": "^5.3.2", "vite": "^4.2.1"}, "dependencies": {"@elastic/elasticsearch": "^8.12.1", "@heroicons/react": "^2.0.17", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@react-pdf/renderer": "^3.3.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.0", "config": "^3.3.11", "date-fns": "^2.30.0", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "formik": "^2.2.9", "framer-motion": "^11.1.3", "lucide-react": "^0.293.0", "motion": "^12.11.0", "next-themes": "^0.3.0", "posthog-js": "^1.132.2", "pptxgenjs": "^3.12.0", "react-day-picker": "^8.9.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-phone-input-2": "^2.15.1", "react-pptx": "^2.20.1", "react-quill": "^2.0.0", "react-responsive-masonry": "^2.1.7", "react-select": "^5.8.0", "react-share-social": "^0.1.60", "shadcn": "^2.5.0", "sonner": "^1.5.0", "swiper": "^11.1.4", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "yup": "^1.0.2", "zod": "^3.22.4"}}