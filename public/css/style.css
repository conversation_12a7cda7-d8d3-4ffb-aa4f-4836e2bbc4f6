/*------------------------------------------------------------------
[Master <PERSON>t]

Project:	DotLife WordPress Theme
Assigned to:	ThemeGoods
Primary use:	WordPress Theme
-------------------------------------------------------------------*/

/*------------------------------------------------------------------
[Color codes]

Background:	#ffffff (white)
Content:	#444444 (grey)
Header:		#222222 (dark grey)
Footer:		#ffffff (white)

a (standard):	#222222 (dark grey)44
a (hover):		#000000 (black)5
a (active):		#000000 (black)4404--------------*/

/*------------------------------------------------------------------
[Typography]

Body copy:		15px/1.65em 'Jost', 'Helvetica Neue', Arial, Verdana, sans-serif
Header:			30px/1.65em 'Jost', 'Helve<PERSON> Neue', Arial, Verdana, sans-serif
Input, textarea:	14px 'Jost','Helvetica Neue', Arial, Verdana, sans-serif
Sidebar heading:	12px 'Jost','Helvetica Neue', Arial, Verdana, sans-serif
-------------------------------------------------------------------*/

/*------------------------------------------------------------------
[Table of contents]

1. Body
2. Navigation / .top_bar
3. Footer / #footer
4. Content / #page_content_wrapper
5. Social Sharing / #social_share_wrapper
6. Sidebar / .sidebar_wrapper
7. Form & Input
8. Pagination / .pagination
9. Widgets
10. Portfolio, Gallery and Template Elements
11. Contact Form Captcha / #captcha-wrap
12. Woocommerce Elements
13. Grid Rotator Plugin
14. Login Plugin Elements
15. LearnPress Plugin Elements
-------------------------------------------------------------------*/

#right_click_content {
    background: rgba(0, 0, 0, 0.5);
    color: #ffffff;
}
body,
input[type="text"],
input[type="password"],
input[type="email"],
input[type="url"],
input[type="date"],
input[type="tel"],
input.wpcf7-text,
.woocommerce table.cart td.actions .coupon .input-text,
.woocommerce-page table.cart td.actions .coupon .input-text,
.woocommerce #content table.cart td.actions .coupon .input-text,
.woocommerce-page #content table.cart td.actions .coupon .input-text,
select,
textarea,
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button,
.ui-widget label,
.ui-widget-header,
.zm_alr_ul_container {
    font-family: "Jost";
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 1.7;
    text-transform: none;
}
h1,
h2,
h3,
h4,
h5,
h6,
h7,
.post_quote_title,
strong[itemprop="author"],
#page_content_wrapper .posts.blog li a,
.page_content_wrapper .posts.blog li a,
#filter_selected,
blockquote,
.sidebar_widget li.widget_products,
#footer ul.sidebar_widget li ul.posts.blog li a,
.woocommerce-page table.cart th,
table.shop_table thead tr th,
.testimonial_slider_content,
.pagination,
.pagination_detail {
    font-family: "Jost";
    font-weight: 700;
    line-height: 1.7;
    text-transform: none;
}
h1 {
    font-size: 34px;
}
h2 {
    font-size: 30px;
}
h3 {
    font-size: 26px;
}
h4 {
    font-size: 24px;
}
h5 {
    font-size: 22px;
}
h6 {
    font-size: 20px;
}
body,
#wrapper,
#page_content_wrapper.fixed,
#gallery_lightbox h2,
.slider_wrapper .gallery_image_caption h2,
#body_loading_screen,
h3#reply-title span,
.overlay_gallery_wrapper,
.pagination a,
.pagination span,
#captcha-wrap .text-box input,
.flex-direction-nav a,
.blog_promo_title h6,
#supersized li,
#horizontal_gallery_wrapper .image_caption,
body.tg_password_protected #page_content_wrapper .inner .inner_wrapper .sidebar_content,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] {
    background-color: #f9f9f9;
}
body,
.pagination a,
#gallery_lightbox h2,
.slider_wrapper .gallery_image_caption h2,
.post_info a,
#page_content_wrapper.split #copyright,
.page_content_wrapper.split #copyright,
.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
.readmore,
.woocommerce-MyAccount-navigation ul a,
.woocommerce #page_content_wrapper div.product p.price,
.woocommerce-page #page_content_wrapper div.product p.price {
    color: #222222;
}
::selection,
.verline {
    background-color: #222222;
}
::-webkit-input-placeholder {
    color: #222222;
}
::-moz-placeholder {
    color: #222222;
}
:-ms-input-placeholder {
    color: #222222;
}
a,
.gallery_proof_filter ul li a {
    color: #222222;
}
.flex-control-paging li a.flex-active,
.post_attribute a:before,
#menu_wrapper .nav ul li a:before,
#menu_wrapper div .nav li > a:before,
.post_attribute a:before {
    background-color: #222222;
}
.flex-control-paging li a.flex-active,
.image_boxed_wrapper:hover,
.gallery_proof_filter ul li a.active,
.gallery_proof_filter ul li a:hover {
    border-color: #222222;
}
a:hover,
a:active,
.post_info_comment a i {
    color: #0067da;
}
input[type="button"]:hover,
input[type="submit"]:hover,
a.button:hover,
.button:hover,
.button.submit,
a.button.white:hover,
.button.white:hover,
a.button.white:active,
.button.white:active,
#menu_wrapper .nav ul li a:hover:before,
#menu_wrapper div .nav li > a:hover:before,
.post_attribute a:hover:before {
    background-color: #0067da;
}
input[type="button"]:hover,
input[type="submit"]:hover,
a.button:hover,
.button:hover,
.button.submit,
a.button.white:hover,
.button.white:hover,
a.button.white:active,
.button.white:active {
    border-color: #0067da;
}
h1,
h2,
h3,
h4,
h5,
h6,
h7,
pre,
code,
tt,
blockquote,
.post_header h5 a,
.post_header h3 a,
.post_header.grid h6 a,
.post_header.fullwidth h4 a,
.post_header h5 a,
blockquote,
.site_loading_logo_item i,
.ppb_subtitle,
.woocommerce .woocommerce-ordering select,
.woocommerce #page_content_wrapper a.button,
.woocommerce.columns-4 ul.products li.product a.add_to_cart_button,
.woocommerce.columns-4 ul.products li.product a.add_to_cart_button:hover,
.ui-accordion .ui-accordion-header a,
.tabs .ui-state-active a,
.post_header h5 a,
.post_header h6 a,
.flex-direction-nav a:before,
.social_share_button_wrapper .social_post_view .view_number,
.social_share_button_wrapper .social_post_share_count .share_number,
.portfolio_post_previous a,
.portfolio_post_next a,
#filter_selected,
#autocomplete li strong,
.themelink,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .ui-dialog-titlebar .ui-dialog-title,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .ui-dialog-titlebar .ui-dialog-title {
    color: #222222;
}
body.page.page-template-gallery-archive-split-screen-php #fp-nav li .active span,
body.tax-gallerycat #fp-nav li .active span,
body.page.page-template-portfolio-fullscreen-split-screen-php #fp-nav li .active span,
body.page.tax-portfolioset #fp-nav li .active span,
body.page.page-template-gallery-archive-split-screen-php #fp-nav ul li a span,
body.tax-gallerycat #fp-nav ul li a span,
body.page.page-template-portfolio-fullscreen-split-screen-php #fp-nav ul li a span,
body.page.tax-portfolioset #fp-nav ul li a span {
    background-color: #222222;
}
#social_share_wrapper,
hr,
#social_share_wrapper,
.post.type-post,
.comment .right,
.widget_tag_cloud div a,
.meta-tags a,
.tag_cloud a,
#footer,
#post_more_wrapper,
#page_content_wrapper .inner .sidebar_content,
#page_content_wrapper .inner .sidebar_content.left_sidebar,
.ajax_close,
.ajax_next,
.ajax_prev,
.portfolio_next,
.portfolio_prev,
.portfolio_next_prev_wrapper.video .portfolio_prev,
.portfolio_next_prev_wrapper.video .portfolio_next,
.separated,
.blog_next_prev_wrapper,
#post_more_wrapper h5,
#ajax_portfolio_wrapper.hidding,
#ajax_portfolio_wrapper.visible,
.tabs.vertical .ui-tabs-panel,
.ui-tabs.vertical.right .ui-tabs-nav li,
.woocommerce div.product .woocommerce-tabs ul.tabs li,
.woocommerce #content div.product .woocommerce-tabs ul.tabs li,
.woocommerce-page div.product .woocommerce-tabs ul.tabs li,
.woocommerce-page #content div.product .woocommerce-tabs ul.tabs li,
.woocommerce div.product .woocommerce-tabs .panel,
.woocommerce-page div.product .woocommerce-tabs .panel,
.woocommerce #content div.product .woocommerce-tabs .panel,
.woocommerce-page #content div.product .woocommerce-tabs .panel,
.woocommerce table.shop_table,
.woocommerce-page table.shop_table,
.woocommerce .cart-collaterals .cart_totals,
.woocommerce-page .cart-collaterals .cart_totals,
.woocommerce .cart-collaterals .shipping_calculator,
.woocommerce-page .cart-collaterals .shipping_calculator,
.woocommerce .cart-collaterals .cart_totals tr td,
.woocommerce .cart-collaterals .cart_totals tr th,
.woocommerce-page .cart-collaterals .cart_totals tr td,
.woocommerce-page .cart-collaterals .cart_totals tr th,
table tr th,
table tr td,
.woocommerce #payment,
.woocommerce-page #payment,
.woocommerce #payment ul.payment_methods li,
.woocommerce-page #payment ul.payment_methods li,
.woocommerce #payment div.form-row,
.woocommerce-page #payment div.form-row,
.ui-tabs li:first-child,
.ui-tabs .ui-tabs-nav li,
.ui-tabs.vertical .ui-tabs-nav li,
.ui-tabs.vertical.right .ui-tabs-nav li.ui-state-active,
.ui-tabs.vertical .ui-tabs-nav li:last-child,
#page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul.menu li.current-menu-item a,
.page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul.menu li.current-menu-item a,
.ui-accordion .ui-accordion-header,
.ui-accordion .ui-accordion-content,
#page_content_wrapper .sidebar .content .sidebar_widget li h2.widgettitle:before,
h2.widgettitle:before,
#autocomplete,
.ppb_blog_minimal .one_third_bg,
.tabs .ui-tabs-panel,
.ui-tabs .ui-tabs-nav li,
.ui-tabs li:first-child,
.ui-tabs.vertical .ui-tabs-nav li:last-child,
.woocommerce .woocommerce-ordering select,
.woocommerce div.product .woocommerce-tabs ul.tabs li.active,
.woocommerce-page div.product .woocommerce-tabs ul.tabs li.active,
.woocommerce #content div.product .woocommerce-tabs ul.tabs li.active,
.woocommerce-page #content div.product .woocommerce-tabs ul.tabs li.active,
.woocommerce-page table.cart th,
table.shop_table thead tr th,
hr.title_break,
.overlay_gallery_border,
#page_content_wrapper.split #copyright,
.page_content_wrapper.split #copyright,
.post.type-post,
.events.type-events,
h5.event_title,
.post_header h5.event_title,
.client_archive_wrapper,
#page_content_wrapper .sidebar .content .sidebar_widget li.widget,
.page_content_wrapper .sidebar .content .sidebar_widget li.widget,
hr.title_break.bold,
blockquote,
.social_share_button_wrapper,
.social_share_button_wrapper,
body:not(.single) .post_wrapper,
.themeborder,
#about_the_author,
.related.products,
.woocommerce div.product div.summary .product_meta,
#single_course_meta ul.single_course_meta_data li.single_course_meta_data_separator,
body .course-curriculum ul.curriculum-sections .section-header,
body.single-post #page_content_wrapper.blog_wrapper .page_title_content {
    border-color: #d8d8d8;
}
input[type="text"],
input[type="password"],
input[type="email"],
input[type="url"],
input[type="tel"],
input[type="date"],
textarea,
select {
    background-color: #ffffff;
    color: #222222;
    border-color: #d8d8d8;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}
input[type="submit"],
input[type="button"],
a.button,
.button,
.woocommerce .page_slider a.button,
a.button.fullwidth,
.woocommerce-page div.product form.cart .button,
.woocommerce #respond input#submit.alt,
.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"] {
    font-family: "Jost";
    font-size: 16px;
    font-weight: 400;
    line-height: 1.7;
    text-transform: none;
}
input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="date"]:focus,
textarea:focus {
    border-color: #0067da;
}
.input_effect ~ .focus-border {
    background-color: #0067da;
}
input[type="submit"],
input[type="button"],
a.button,
.button,
.woocommerce .page_slider a.button,
a.button.fullwidth,
.woocommerce-page div.product form.cart .button,
.woocommerce #respond input#submit.alt,
.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
body.learnpress-page #page_content_wrapper .order-recover .lp-button,
.learnpress-page #learn-press-profile-basic-information button,
body #page_content_wrapper p#lp-avatar-actions button,
.learnpress-page #profile-content-settings form button[type="submit"] {
    font-family: "Jost";
    font-size: 13px;
    font-weight: 700;
    letter-spacing: 1px;
    line-height: 1.7;
    text-transform: uppercase;
}
input[type="submit"],
input[type="button"],
a.button,
.button,
.woocommerce .page_slider a.button,
a.button.fullwidth,
.woocommerce-page div.product form.cart .button,
.woocommerce #respond input#submit.alt,
.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
a#toTop,
.pagination span,
.widget_tag_cloud div a,
.pagination a,
.pagination span,
body.learnpress-page #page_content_wrapper .order-recover .lp-button,
.learnpress-page #learn-press-profile-basic-information button,
body #page_content_wrapper p#lp-avatar-actions button,
.learnpress-page #profile-content-settings form button[type="submit"],
.learnpress-page #page_content_wrapper .lp-button {
    -webkit-border-radius: 25px;
    -moz-border-radius: 25px;
    border-radius: 25px;
}
input[type="submit"],
input[type="button"],
a.button,
.button,
.pagination span,
.pagination a:hover,
.woocommerce .footer_bar .button,
.woocommerce .footer_bar .button:hover,
.woocommerce-page div.product form.cart .button,
.woocommerce #respond input#submit.alt,
.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt,
.post_type_icon,
.filter li a:hover,
.filter li a.active,
#portfolio_wall_filters li a.active,
#portfolio_wall_filters li a:hover,
.comment_box,
.one_half.gallery2 .portfolio_type_wrapper,
.one_third.gallery3 .portfolio_type_wrapper,
.one_fourth.gallery4 .portfolio_type_wrapper,
.one_fifth.gallery5 .portfolio_type_wrapper,
.portfolio_type_wrapper,
.post_share_text,
#close_share,
.widget_tag_cloud div a:hover,
.ui-accordion .ui-accordion-header .ui-icon,
.mobile_menu_wrapper #mobile_menu_close.button,
.mobile_menu_wrapper #close_mobile_menu,
.multi_share_button,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
.learnpress-page #page_content_wrapper .lp-button,
.learnpress-page #learn-press-profile-basic-information button,
.learnpress-page #profile-content-settings form button[type="submit"] {
    background-color: #0067da;
}
.pagination span,
.pagination a:hover,
.button.ghost,
.button.ghost:hover,
.button.ghost:active,
blockquote:after,
.woocommerce-MyAccount-navigation ul li.is-active,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
.learnpress-page #page_content_wrapper .lp-button,
.learnpress-page #learn-press-profile-basic-information button,
.learnpress-page #profile-content-settings form button[type="submit"] {
    border-color: #0067da;
}
.comment_box:before,
.comment_box:after {
    border-top-color: #0067da;
}
.button.ghost,
.button.ghost:hover,
.button.ghost:active,
.infinite_load_more,
blockquote:before,
.woocommerce-MyAccount-navigation ul li.is-active a,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"] {
    color: #0067da;
}
input[type="submit"],
input[type="button"],
a.button,
.button,
.pagination a:hover,
.woocommerce .footer_bar .button,
.woocommerce .footer_bar .button:hover,
.woocommerce-page div.product form.cart .button,
.woocommerce #respond input#submit.alt,
.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt,
.post_type_icon,
.filter li a:hover,
.filter li a.active,
#portfolio_wall_filters li a.active,
#portfolio_wall_filters li a:hover,
.comment_box,
.one_half.gallery2 .portfolio_type_wrapper,
.one_third.gallery3 .portfolio_type_wrapper,
.one_fourth.gallery4 .portfolio_type_wrapper,
.one_fifth.gallery5 .portfolio_type_wrapper,
.portfolio_type_wrapper,
.post_share_text,
#close_share,
.widget_tag_cloud div a:hover,
.ui-accordion .ui-accordion-header .ui-icon,
.mobile_menu_wrapper #mobile_menu_close.button,
#toTop,
.multi_share_button,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
.pagination span.current,
.mobile_menu_wrapper #close_mobile_menu,
body.learnpress-page #page_content_wrapper .lp-button,
.learnpress-page #learn-press-profile-basic-information button,
.learnpress-page #profile-content-settings form button[type="submit"] {
    color: #ffffff;
}
input[type="submit"],
input[type="button"],
a.button,
.button,
.pagination a:hover,
.woocommerce .footer_bar .button,
.woocommerce .footer_bar .button:hover,
.woocommerce-page div.product form.cart .button,
.woocommerce #respond input#submit.alt,
.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt,
.infinite_load_more,
.post_share_text,
#close_share,
.widget_tag_cloud div a:hover,
.mobile_menu_wrapper #close_mobile_menu,
.mobile_menu_wrapper #mobile_menu_close.button,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
.learnpress-page #learn-press-profile-basic-information button,
.learnpress-page #profile-content-settings form button[type="submit"] {
    border-color: #0067da;
}
input[type="button"]:hover,
input[type="submit"]:hover,
a.button:hover,
.button:hover,
.button.submit,
a.button.white:hover,
.button.white:hover,
a.button.white:active,
.button.white:active,
.black_bg input[type="submit"],
.learnpress-page #page_content_wrapper .lp-button:hover,
.learnpress-page #learn-press-profile-basic-information button:hover,
.learnpress-page #profile-content-settings form button[type="submit"]:hover {
    background-color: #ffffff;
}
input[type="button"]:hover,
input[type="submit"]:hover,
a.button:hover,
.button:hover,
.button.submit,
a.button.white:hover,
.button.white:hover,
a.button.white:active,
.button.white:active,
.black_bg input[type="submit"],
body.learnpress-page #page_content_wrapper .lp-button:hover,
.learnpress-page #learn-press-profile-basic-information button:hover,
.learnpress-page #profile-content-settings form button[type="submit"]:hover {
    color: #0067da;
}
input[type="button"]:hover,
input[type="submit"]:hover,
a.button:hover,
.button:hover,
.button.submit,
a.button.white:hover,
.button.white:hover,
a.button.white:active,
.button.white:active,
.black_bg input[type="submit"],
.learnpress-page #learn-press-profile-basic-information button:hover,
.learnpress-page #profile-content-settings form button[type="submit"]:hover {
    border-color: #0067da;
}
.frame_top,
.frame_bottom,
.frame_left,
.frame_right {
    background: #222222;
}
#menu_wrapper .nav ul li a,
#menu_wrapper div .nav li > a,
.header_client_wrapper {
    font-family: "Jost";
    font-size: 14px;
    font-weight: 700;
    line-height: 1.7;
    text-transform: none;
}
#menu_wrapper .nav ul li,
html[data-menu="centeralogo"] #logo_right_button {
    padding-top: 26px;
    padding-bottom: 26px;
}
.top_bar,
html {
    background-color: #ffffff;
}
#menu_wrapper .nav ul li a,
#menu_wrapper div .nav li > a,
#mobile_nav_icon,
#logo_wrapper .social_wrapper ul li a,
.header_cart_wrapper a {
    color: #222222;
}
#mobile_nav_icon {
    border-color: #222222;
}
#menu_wrapper .nav ul li a.hover,
#menu_wrapper .nav ul li a:hover,
#menu_wrapper div .nav li a.hover,
#menu_wrapper div .nav li a:hover,
.header_cart_wrapper a:hover,
#page_share:hover,
#logo_wrapper .social_wrapper ul li a:hover {
    color: #0067da;
}
#menu_wrapper .nav ul li a:before,
#menu_wrapper div .nav li > a:before {
    background-color: #0067da;
}
#menu_wrapper div .nav > li.current-menu-item > a,
#menu_wrapper div .nav > li.current-menu-parent > a,
#menu_wrapper div .nav > li.current-menu-ancestor > a,
#menu_wrapper div .nav li ul:not(.sub-menu) li.current-menu-item a,
#menu_wrapper div .nav li.current-menu-parent ul li.current-menu-item a,
#logo_wrapper .social_wrapper ul li a:active {
    color: #0067da;
}
.top_bar,
#nav_wrapper {
    border-color: #ffffff;
}
.header_cart_wrapper .cart_count {
    background-color: #0067da;
    color: #ffffff;
}
#menu_wrapper .nav ul li ul li a,
#menu_wrapper div .nav li ul li a,
#menu_wrapper div .nav li.current-menu-parent ul li a {
    font-family: "Jost";
    font-size: 14px;
    font-weight: 700;
    text-transform: none;
}
#menu_wrapper .nav ul li ul li a,
#menu_wrapper div .nav li ul li a,
#menu_wrapper div .nav li.current-menu-parent ul li a,
#menu_wrapper div .nav li.current-menu-parent ul li.current-menu-item a,
#menu_wrapper .nav ul li.megamenu ul li ul li a,
#menu_wrapper div .nav li.megamenu ul li ul li a {
    color: #222222;
}
#menu_wrapper .nav ul li ul li a:hover,
#menu_wrapper div .nav li ul li a:hover,
#menu_wrapper div .nav li.current-menu-parent ul li a:hover,
#menu_wrapper .nav ul li.megamenu ul li ul li a:hover,
#menu_wrapper div .nav li.megamenu ul li ul li a:hover,
#menu_wrapper .nav ul li.megamenu ul li ul li a:active,
#menu_wrapper div .nav li.megamenu ul li ul li a:active,
#menu_wrapper div .nav li.current-menu-parent ul li.current-menu-item a:hover {
    color: #0067da;
}
#menu_wrapper .nav ul li ul li a:before,
#menu_wrapper div .nav li ul li > a:before,
#wrapper.transparent .top_bar:not(.scroll) #menu_wrapper div .nav ul li ul li a:before {
    background-color: #0067da;
}
#menu_wrapper .nav ul li ul,
#menu_wrapper div .nav li ul {
    background: #ffffff;
    border-color: #d8d8d8;
}
#menu_wrapper div .nav li.megamenu ul li > a,
#menu_wrapper div .nav li.megamenu ul li > a:hover,
#menu_wrapper div .nav li.megamenu ul li > a:active,
#menu_wrapper div .nav li.megamenu ul li.current-menu-item > a {
    color: #222222;
}
#menu_wrapper div .nav li.megamenu ul li {
    border-color: #eeeeee;
}
.above_top_bar {
    background: #222222;
}
#top_menu li a,
.top_contact_info,
.top_contact_info i,
.top_contact_info a,
.top_contact_info a:hover,
.top_contact_info a:active {
    color: #ffffff;
}
.mobile_main_nav li a,
#sub_menu li a {
    font-family: "Jost";
    font-size: 18px;
    font-weight: 700;
    line-height: 2;
    text-transform: none;
}
#sub_menu li a {
    font-family: "Jost";
    font-size: 18px;
    font-weight: 700;
    line-height: 2;
    text-transform: none;
}
.mobile_menu_wrapper {
    background-color: #000000;
}
.mobile_main_nav li a,
#sub_menu li a,
.mobile_menu_wrapper .sidebar_wrapper a,
.mobile_menu_wrapper .sidebar_wrapper,
#close_mobile_menu i,
.mobile_menu_wrapper .social_wrapper ul li a,
.fullmenu_content #copyright,
.mobile_menu_wrapper .sidebar_wrapper h2.widgettitle {
    color: #ffffff;
}
.mobile_main_nav li a:hover,
.mobile_main_nav li a:active,
#sub_menu li a:hover,
#sub_menu li a:active,
.mobile_menu_wrapper .social_wrapper ul li a:hover {
    color: #ffffff;
}
#page_caption.hasbg {
    height: 600px;
}
#page_caption {
    background-color: #ffffff;
    padding-top: 60px;
    padding-bottom: 60px;
    margin-bottom: 45px;
}
#page_caption .page_title_wrapper .page_title_inner {
    text-align: center;
}
#page_caption h1 {
    font-family: "Jost";
    font-size: 45px;
    font-weight: 700;
    line-height: 1.2;
    text-transform: none;
    color: #222222;
}
.page_tagline,
.thumb_content span,
.portfolio_desc .portfolio_excerpt,
.testimonial_customer_position,
.testimonial_customer_company,
.post_detail.single_post {
    font-family: "Jost";
    font-size: 13px;
    font-weight: 400;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: #222222;
}
#page_content_wrapper .sidebar .content .sidebar_widget li h2.widgettitle,
h2.widgettitle,
h5.widgettitle {
    font-family: "Jost";
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 0px;
    text-transform: none;
    color: #222222;
    border-color: #222222;
}
#page_content_wrapper .inner .sidebar_wrapper .sidebar .content,
.page_content_wrapper .inner .sidebar_wrapper .sidebar .content {
    color: #222222;
}
#page_content_wrapper .inner .sidebar_wrapper a:not(.button),
.page_content_wrapper .inner .sidebar_wrapper a:not(.button) {
    color: #222222;
}
#page_content_wrapper .inner .sidebar_wrapper a:hover:not(.button),
#page_content_wrapper .inner .sidebar_wrapper a:active:not(.button),
.page_content_wrapper .inner .sidebar_wrapper a:hover:not(.button),
.page_content_wrapper .inner .sidebar_wrapper a:active:not(.button) {
    color: #0067da;
}
#page_content_wrapper .inner .sidebar_wrapper a:not(.button):before {
    background-color: #0067da;
}
#footer {
    font-size: 15px;
}
.footer_bar_wrapper {
    font-size: 13px;
}
.footer_bar,
#footer {
    background-color: #222222;
}
#footer,
#copyright,
#footer_menu li a,
#footer_menu li a:hover,
#footer_menu li a:active,
#footer input[type="text"],
#footer input[type="password"],
#footer input[type="email"],
#footer input[type="url"],
#footer input[type="tel"],
#footer input[type="date"],
#footer textarea,
#footer select,
#footer blockquote {
    color: #999999;
}
#copyright a,
#copyright a:active,
#footer a,
#footer a:active,
#footer .sidebar_widget li h2.widgettitle,
#footer_photostream a {
    color: #ffffff;
}
#footer .sidebar_widget li h2.widgettitle {
    border-color: #ffffff;
}
#copyright a:hover,
#footer a:hover,
.social_wrapper ul li a:hover,
#footer a:hover,
#footer_photostream a:hover {
    color: #ffffff;
}
.footer_bar {
    background-color: #222222;
}
.footer_bar,
#copyright {
    color: #999999;
}
.footer_bar a,
#copyright a,
#footer_menu li a {
    color: #ffffff;
}
.footer_bar a:hover,
#copyright a:hover,
#footer_menu li a:hover {
    color: #ffffff;
}
.footer_bar_wrapper,
.footer_bar {
    border-color: #333333;
}
.footer_bar_wrapper .social_wrapper ul li a {
    color: #ffffff;
}
a#toTop {
    background: rgba(0, 0, 0, 0.1);
    color: #ffffff;
}
#page_content_wrapper.blog_wrapper,
#page_content_wrapper.blog_wrapper input:not([type="submit"]),
#page_content_wrapper.blog_wrapper textarea,
.post_excerpt.post_tag a:after,
.post_excerpt.post_tag a:before,
.post_navigation .navigation_post_content {
    background-color: #ffffff;
}
.post_info_cat,
.post_info_cat a {
    color: #444444;
    border-color: #444444;
}
.post_img_hover .post_type_icon {
    background: #0067da;
}
.blog_post_content_wrapper.layout_grid .post_content_wrapper,
.blog_post_content_wrapper.layout_masonry .post_content_wrapper,
.blog_post_content_wrapper.layout_metro .post_content_wrapper,
.blog_post_content_wrapper.layout_classic .post_content_wrapper {
    background: #ffffff;
}
.post_attribute a {
    color: #222222;
}
.post_attribute a a:before {
    background-color: #222222;
}
.post_attribute a,
.post_button_wrapper .post_attribute,
.post_attribute a:before {
    opacity: 0.5;
}
.post_header h5,
h6.subtitle,
.post_caption h1,
#page_content_wrapper .posts.blog li a,
.page_content_wrapper .posts.blog li a,
#post_featured_slider li .slider_image .slide_post h2,
.post_header.grid h6,
.post_info_cat,
.comment_date,
.post-date,
.post_navigation h7 {
    font-family: "Jost";
    font-weight: 700;
    letter-spacing: 0px;
    text-transform: none;
}
body.single-post #page_caption h1,
body.single-post #page_content_wrapper.blog_wrapper .page_title_content h1 {
    font-family: "Jost";
    font-size: 50px;
    font-weight: 700;
    line-height: 1.2;
    text-transform: none;
}
body.single-post #page_content_wrapper.blog_wrapper,
.post_related .post_header_wrapper {
    background: #ffffff;
}
.post_excerpt.post_tag a {
    background: #f0f0f0;
    color: #444;
}
.post_excerpt.post_tag a:after {
    border-left-color: #f0f0f0;
}
.woocommerce ul.products li.product .price ins,
.woocommerce-page ul.products li.product .price ins,
.woocommerce ul.products li.product .price,
.woocommerce-page ul.products li.product .price,
p.price ins span.amount,
.woocommerce #content div.product p.price,
.woocommerce #content div.product span.price,
.woocommerce div.product p.price,
.woocommerce div.product span.price,
.woocommerce-page #content div.product p.price,
.woocommerce-page #content div.product span.price,
.woocommerce-page div.product p.price,
.woocommerce-page div.product span.price {
    color: #0067da;
}
.woocommerce ul.products li.product .price ins,
.woocommerce-page ul.products li.product .price ins,
.woocommerce ul.products li.product .price,
.woocommerce-page ul.products li.product .price,
p.price ins span.amount,
.woocommerce #content div.product p.price,
.woocommerce #content div.product span.price {
    background: #f0f0f0;
}
.woocommerce .products .onsale,
.woocommerce ul.products li.product .onsale,
.woocommerce span.onsale {
    background-color: #0067da;
}
.woocommerce div.product .woocommerce-tabs ul.tabs li.active a,
.woocommerce-page div.product .woocommerce-tabs ul.tabs li.active a {
    color: #ffffff;
}
.woocommerce div.product .woocommerce-tabs ul.tabs li.active,
.woocommerce-page div.product .woocommerce-tabs ul.tabs li.active {
    background: #222222;
}
body.single-product div.product.type-product {
    background: #ffffff;
}
body .course-curriculum ul.curriculum-sections .section-content .course-item.item-preview .course-item-status,
body.learnpress-page.profile #learn-press-profile-nav .tabs > li.active > a,
body.learnpress-page.profile #learn-press-profile-nav .tabs > li a:hover,
body.learnpress-page.profile #learn-press-profile-nav .tabs > li:hover:not(.active) > a,
body ul.learn-press-courses .course .course-info .course-price .price {
    background: #0067da;
}
body .course-item-nav .prev span,
body .course-item-nav .next span,
body .course-curriculum ul.curriculum-sections .section-content .course-item.current a {
    color: #0067da;
}
#page_content_wrapper ul.learn-press-nav-tabs .course-nav a {
    background: #f9f9f9;
    color: #222222;
}
#page_content_wrapper ul.learn-press-nav-tabs .course-nav.active a,
body.learnpress-page.profile .lp-tab-sections .section-tab.active span {
    background: #000000;
    color: #ffffff;
}
body.learnpress-page.checkout .lp-list-table thead tr th,
body.learnpress-page.profile .lp-list-table thead tr th {
    background: #333333;
}
body.learnpress-page.checkout .lp-list-table {
    color: #222222;
}
body .lp-list-table th,
body .lp-list-table td,
body .lp-list-table tbody tr td,
body .lp-list-table tbody tr th {
    background: #ffffff;
}
body .lp-list-table tbody tr td,
body .lp-list-table tbody tr th,
body .lp-list-table td {
    border-color: #d8d8d8;
}
body .lp-list-table th,
body .lp-list-table td {
    color: #222222;
}
body.single-lp_course #lp-single-course .single_course_title h1,
body.single-meeting .single_course_title h1 {
    font-family: "Jost";
    font-size: 34px;
    font-weight: 700;
    line-height: 1.7;
    text-transform: none;
    color: #222222;
}
#single_course_meta ul.single_course_meta_data {
    background: #ffffff;
    color: #222222;
}


body.loaded #loftloader-wrapper .loader-section.section-fade {
	transition: all 0s 0s ease;
}

.slideUp2{
    animation-name: slideUp2;
    -webkit-animation-name: slideUp2;

    animation-duration: 0.2s;
    -webkit-animation-duration: 0.2s;

    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;

    -webkit-animation-fill-mode:forwards;
    -moz-animation-fill-mode:forwards;
    -ms-animation-fill-mode:forwards;
    animation-fill-mode:forwards;
}

@keyframes slideUp2 {
    0% {
    	opacity: 0;
    	transform: translateY(5%);
    }
    100% {
    	opacity: 1;
    	transform: translateY(0%);
    }
}

@-webkit-keyframes slideUp2 {
    0% {
    	opacity: 0;
    	transform: translateY(5%);
    }
    100% {
    	opacity: 1;
    	transform: translateY(0%);
    }
}

@keyframes subNavIn{
	from{
		transform: translateX(200px);
		-webkit-transform: translateX(200px);
		opacity: 0;
	}
	to{
		transform: translateX(0px);
		-webkit-transform: translateX(0px);
		opacity: 1;
	}
}

@-webkit-keyframes subNavIn{
	from{
		transform: translateX(200px);
		-webkit-transform: translateX(200px);
		opacity: 0;
	}
	to{
		transform: translateX(0px);
		-webkit-transform: translateX(0px);
		opacity: 1;
	}
}

@keyframes subNavOut{
	from{
		transform: translateX(0px);
		-webkit-transform: translateX(0px);
		opacity: 1;
	}
	to{
		transform: translateX(200px);
		-webkit-transform: translateX(200px);
		opacity: 0;
	}
}

@-webkit-keyframes subNavOut{
	from{
		transform: translateX(0px);
		-webkit-transform: translateX(0px);
		opacity: 1;
	}
	to{
		transform: translateX(200px);
		-webkit-transform: translateX(200px);
		opacity: 0;
	}
}

@keyframes mainNavOut{
	from{
		transform: translateX(0px);
		-webkit-transform: translateX(0px);
		opacity: 1;
	}
	to{
		transform: translateX(-200px);
		-webkit-transform: translateX(-200px);
		opacity: 0;
	}
}

@-webkit-keyframes mainNavOut{
	from{
		transform: translateX(0px);
		-webkit-transform: translateX(0px);
		opacity: 1;
	}
	to{
		transform: translateX(-200px);
		-webkit-transform: translateX(-200px);
		opacity: 0;
	}
}

@keyframes mainNavIn{
	from{
		transform: translateX(-200px);
		-webkit-transform: translateX(-200px);
		opacity: 0;
	}
	to{
		transform: translateX(0px);
		-webkit-transform: translateX(0px);
		opacity: 1;
	}
}

@-webkit-keyframes mainNavIn{
	from{
		transform: translateX(-200px);
		-webkit-transform: translateX(-200px);
		opacity: 0;
	}
	to{
		transform: translateX(0px);
		-webkit-transform: translateX(0px);
		opacity: 1;
	}
}

html, body
{
	min-height: 100%;
}

body
{
	background: #ffffff;
	font-family: 'Libre Baskerville', 'Helvetica Neue', Arial,Verdana,sans-serif;
	padding: 0;
	margin: 0;
	color: #444;
	font-weight: 400;
	-webkit-font-smoothing: antialiased;
	font-size: 14px;
	line-height: 1.7;
	word-wrap: break-word;
	-webkit-overflow-scrolling: touch;
}

body.js_nav
{
	width: 100%;
	height: 100%;
}

.width_half
{
	width: 50%;
	margin: auto;
}

.width_one_third
{
	width: 33%;
	margin: auto;
}

#perspective
{
	width: 100%;
	min-height: 100%;
	position: relative;
}

@-moz-document url-prefix() {
    #perspective {
        overflow-x: hidden;
    }
}

body.modalview #perspective
{
    position: fixed;
    perspective: 1500px;
    top: 0;
}

body.modalview #footer_wrapper
{
	display: none;
}

#wrapper
{
	float: left;
	width: 100%;
	background: #fff;

    transform-origin: 50% 50% 50%;
    transition: transform 0.4s;
    position: relative;
    left: 0;
    min-height: 100%;
}

body.tg_footer_reveal #wrapper
{
	z-index: 1;
}

body.right_clicked #wrapper
{
	filter: blur(7px);
}

body.leftmenu #wrapper
{
	padding-top: 0 !important;
	width: calc(100% - 350px);
	left: 350px;
}

body.leftmenu #wrapper #page_content_wrapper
{
	padding: 0 40px 0 40px;
}

body.leftmenu #wrapper #page_content_wrapper.blog_wrapper
{
	padding-top: 50px;
}

body.leftmenu .elementor-section.elementor-section-stretched
{
	max-width: 100% !important;
	left: 0 !important;
}

body.leftmenu .mobile_menu_wrapper
{
	left: 0;
	-webkit-transform: translate(0px, 0px);
	-ms-transform: translate(0px, 0px);
	transform: translate(0px, 0px);
	-o-transform: translate(0px, 0px);
	overflow-y: scroll;
	overflow-x: hidden;
	-webkit-overflow-scrolling: touch;
	width: 350px;
	padding: 50px;
}

body.modalview #wrapper
{
	position: absolute;
    overflow: hidden;
    cursor: pointer;
    height: 100%;
    width: 100%;
    backface-visibility: hidden;
}

body.js_nav #wrapper
{
	transform: translateZ(0px) translateX(10%) rotateY(-50deg);
}

body.hammenufull.js_nav #wrapper
{
	transform: none;
	transform: scale(1);
	opacity: 0;
}

body.tg_password_protected
{
	height: 100%;
}

body.tg_password_protected #page_caption,
body.tg_password_protected #footer_wrapper
{
	display: none;
}

body.tg_password_protected #perspective
{
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

body.tg_password_protected #wrapper
{
	background: transparent;
	height: 100%;
}

body.tg_password_protected #page_content_wrapper
{
	display: table;
	width: 100%;
	height: 100vh;
}

body.tg_password_protected.leftmenu #page_content_wrapper
{
	width: calc(100% - 350px);
}

body.tg_password_protected #page_content_wrapper .inner
{
	display: table-cell;
	vertical-align: middle;
	float: none;
}

body.tg_password_protected #page_content_wrapper .inner .inner_wrapper
{
	float: none;
	text-align: center;
	width: 50%;
	margin: auto;
}

body.tg_password_protected.leftmenu #page_content_wrapper .inner .inner_wrapper
{
	width: 70%;
}

body.tg_password_protected #page_content_wrapper .inner .inner_wrapper .sidebar_content
{
	padding: 70px 60px 60px 60px;
	box-sizing: border-box;

	opacity: 0;
    animation-name: slideUp2;
    -webkit-animation-name: slideUp2;
    animation-duration: 0.5s;
    -webkit-animation-duration: 0.5s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-fill-mode: forwards;
    -moz-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-delay: 0.5s;
    animation-delay: 0.5s;
}

body.tg_password_protected #page_content_wrapper .inner .inner_wrapper .sidebar_content .protected-post-header
{
	margin-bottom: 30px;
}

body.tg_password_protected #page_content_wrapper .inner .inner_wrapper .sidebar_content input[type=password]
{
	text-align: center;
}

#wrapper.hasbg.transparent
{
	padding-top: 0 !important;
}

hr
{
	height: 1px;
	border: 0;
	border-top: 2px solid #ebebeb;
	background: transparent;
	margin: auto;
	margin-top: 20px;
	margin-bottom: 20px;
	width: 100%;
	clear: both;
}

.themeborder
{
	border-color: #dce0e0;
}

.theme_radius
{
	border-radius: 3px;
}

a
{
	color: #222;
	text-decoration: none;
}

a:hover
{
	color: #222;
	text-decoration: none;
}

a:active
{
	color: #222;
	text-decoration: none;
}

h1
{
	font-size: 34px;
}

h2
{
	font-size: 30px;
}

h2.number
{
	font-size: 50px;
	line-height: 50px;
}

h3
{
	font-size: 26px;
}

h3#reply-title,
h3.comment_title,
h3.sub_title
{
	text-align: center;
	margin-top: 40px;
}

h3#reply-title
{
	margin-top: 0px;
}

body.logged-in h3#reply-title
{
	margin-bottom: 0;
}

h4
{
	font-size: 22px;
}

h5
{
	font-size: 18px;
}

h5.related_post, .fullwidth_comment_wrapper h5.comment_header
{
	text-align: center;
}

h6
{
	font-size: 16px;
}

h5.widgettitle
{
	font-size: 11px;
    display: inline-block;
    text-align: left;
    font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
    color: #222;
    font-weight: 400;
    letter-spacing: 2px;
    text-transform: uppercase;
    position: relative;
    border-bottom: 1px solid #222;
}

h7
{
	font-size: 18px;
}

h1, h2, h3, h4, h5, h6, h7
{
	color: #222;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
	font-weight: 400;
	line-height: 1.5em;
}

pre, code, tt
{
	font: 12px 'andale mono', 'lucida console', monospace;
	line-height:1.5;
	padding: 40px;
	display: block;
	overflow: auto;
	margin-top: 20px;
	margin: 20px 0 20px 0;
	width: 100%;
	border: 0;
	color: #000;
	background: #f0f0f0;
	box-sizing: border-box;
}

.clear
{
	clear: both;
}

img.mid_align
{
	vertical-align: middle;
	margin-right: 5px;
	border: 0;
}

.fullwidth_comment_wrapper
{
	width: 100%;
	float: left;
	margin-top: 30px;
	padding-top: 10px;
	border-top: 1px solid #d8d8d8;
}

.fullwidth_comment_wrapper:empty
{
	display: none;
}

#commentform
{
	margin-top: 0;
}

body.single.single-post .protected-post-header
{
	display: none;
}

#commentform label,
.wpcf7-form label
{
	margin-bottom: 0;
	font-size: 13px;
	letter-spacing: 2px;
	font-weight: 700;
	text-transform: uppercase;
}

#commentform .comment-form-cookies-consent
{
	margin-top: 10px;
	clear: both;
}

#commentform .comment-form-cookies-consent label
{
	margin-left: 5px;
	font-size: inherit;
	letter-spacing: 0;
	font-weight: 400;
	text-transform: none;
}

.logged-in-as
{
	text-align: center;
	padding-top: 0 !important;
	margin-bottom: 10px;
}

#commentform > p,
body.single-product .comment-form-rating
{
	box-sizing: border-box;
}

#commentform > p.input_wrapper
{
	margin: 24px 24px 24px 0;
}

#commentform > p.input_wrapper.comment-form-url,
#commentform > p.input_wrapper.comment-form-comment
{
	margin-right: 0;
}

#commentform > p.input_wrapper.comment-form-comment
{
	margin-top: 0px;
	padding: 0;
	line-height: 0;
	margin-bottom: 0;
}

#commentform > p.input_wrapper.comment-form-comment textarea
{
	margin-top: 20px;
}

#commentform > p.form-submit input#submit
{
	width: 100%;
}

#commentform > p.form-submit
{
	padding-top: 10px !important;
}

body.logged-in #commentform > p.form-submit
{
	padding-top: 30px !important;
}

#commentform > p.comment-form-rating
{
	width: 50%;
	float: left;
	display: block;
	clear: none;
	padding-top: 10px;
    padding-bottom: 10px;
}

#commentform > p.comment-form-rating label
{
	width: 50%;
	float: left;
	font-size: 13px;
	font-weight: 500;
}

#commentform > p.comment-form-rating  .br-widget
{
	width: 50%;
	float: left;
}

#commentform > p.comment-form-comment
{
	padding-bottom: 0;
	clear: both;
}

#commentform > p.comment-notes
{
	text-align: center;
	padding: 0 !important;
	display: none;
}

#commentform p.comment-form-author, #commentform p.comment-form-email, #commentform p.comment-form-url
{
	float: left;
	width: calc(33.33% - 16px);
}

#page_content_wrapper .inner .sidebar_content #commentform p.comment-form-author, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-email, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-url
{
	width: calc(33.33% - 16px);
}

body.single-product #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-author, body.single-product #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-email
{
	width: 50%;
}

#contact_form input[type="text"], #contact_form textarea, #commentform input[type="text"], #commentform input[type="email"], #commentform input[type="url"], #commentform textarea
{
  	width: 100%;
  	-moz-box-sizing: border-box;
  	box-sizing: border-box;
}

#commentform > p.form-submit
{
	padding-top: 15px;
    clear: both;
}

blockquote
{
	font-size: 24px;
    font-weight: 900;
    color: #222;
    margin: auto;
    padding-top: 20px;
    padding-bottom: 20px;
    margin-bottom: 0;
    text-align: center;
    position: relative;
    line-height: 1.5em;
    letter-spacing: 0;
    clear: both;
}

blockquote strong
{
	font-weight: 900;
}

blockquote cite
{
	font-size: 14px;
	font-weight: 600;
	font-style: normal;
}

.wp-block-quote.is-large, .wp-block-quote.is-style-large
{
	margin: 20px 0 20px 0;
}

.textwidget blockquote
{
	margin: 0;
}

blockquote p
{
	padding: 0 !important;
}

blockquote h2
{
	font-weight: normal;
	font-size: 22px;
}

blockquote h3
{
	font-weight: normal;
	font-size: 20px;
}

#respond
{
	width: 100%;
	float: left;
}

#respond.comment-respond
{
	padding-top: 30px;
}

.aligncenter
{
	text-align: center;
}

.mc4wp-form-fields input[type=email]
{
	width: 300px;
	margin-right: 10px;
}

.mc4wp-form-fields
{
	text-align: left;
}

.aligncenter .mc4wp-form-fields
{
	text-align: center;
}

.mc4wp-form-white .mc4wp-form-fields input[type=email]
{
	background: transparent !important;
	color: #fff;
	border: 0;
	border-bottom: 1px solid #fff;
	width: calc(100% - 250px);
	border-radius: 0 !important;
}

.mc4wp-form-white .mc4wp-form-fields input[type=email]::placeholder
{
	opacity: 0.4;
	color: #fff;
}

.mc4wp-form-white .mc4wp-form-fields input[type=submit]
{
	background: transparent !important;
	color: #fff;
	border: 1px solid #fff;
	margin-left: 10px;
}

.mc4wp-form-white.new-line .mc4wp-form-fields input[type=email]
{
	width: calc(100% - 70px);
}

.mc4wp-form-white.new-line .mc4wp-form-fields input[type=submit]
{
	clear: both;
	margin-top: 20px;
	margin-left: 0;
}

/*------------------------------------------------------------------
[2. Navigation]
*/

#logo_wrapper
{
	text-align: center;
	padding: 30px 0 30px 0;
}

.top_bar.scroll #logo_wrapper
{
	display: none;
}

.top_bar.hasbg
{
	border-bottom: 0;
}

.top_bar.hasbg #nav_wrapper
{
	border-color: rgba(256, 256, 256, .3);
}

body.centeralign .logo_container .logo_wrapper:not(.hidden)
{
	margin-top: 60px;
}

body.centeralign .top_bar
{
	border-bottom: 0;
}

.logo_container
{
	display: table;
	width: 100%;
	height: 100%;
}

.logo_align
{
	display: table-cell;
	vertical-align: middle;
	line-height: 0;
}

body.leftmenu .logo_container
{
	display: block;
	width: 100%;
	height: auto;
}

body.leftmenu.admin-bar .logo_container
{
	margin-top: 40px;
}

.logo_wrapper
{
	margin: 0;
	display: inline-block;
	line-height: 0;
}

.logo_wrapper img.custom_logo_no_info
{
	width: 50%;
	height: auto;
}

.logo_wrapper.hidden
{
	display: none;
}

.logo_wrapper img.zoom
{
	transform: scale(0.8) !important;
}

.above_top_bar
{
	height: 40px;
	background: #222;
	position: relative;
	z-index: 3;
}

.header_style_wrapper
{
	width: 100%;
	float: left;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 9;
	display: block;
}

#elementor_header.header_style_wrapper
{
	opacity: 0;
}

#elementor_header.header_style_wrapper.visible
{
	opacity: 1;
}

#elementor_header.header_style_wrapper.visible section.elementor-invisible
{
	visibility: visible;
}

#elementor_sticky_header.header_style_wrapper
{
	opacity: 0;
	z-index: -1;

	-webkit-transform: translate(0px,-140px);
    -moz-transform: translate(0px,-140px);
    transform: translate(0px,-140px);

    transition: all 0.2s ease-out;
    -webkit-transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
}

#elementor_sticky_header.header_style_wrapper.visible
{
	opacity: 1;
	z-index: 9;

	-webkit-transform: translate(0px,);
    -moz-transform: translate(0px,0px);
    transform: translate(0px,0px);
}

#searchform input[type=text] { width: 60%; }

body.admin-bar .header_style_wrapper
{
	padding-top: 32px;
}

body.leftmenu .header_style_wrapper
{
	display: none;
}

.header_style_wrapper.nofixed
{
	display: none;
}

.top_bar
{
	padding: 0;
	box-sizing: border-box;
	width: 100%;
	background: #fff;
	background: rgb(256,256,256,0.95);
	background: rgba(256,256,256,0.95);
	border-bottom: 1px solid #dce0e0;
	float: left;

	-webkit-transition: all 0.5s;
	-moz-transition: all 0.5s;
	transition: all 0.5s;
}

.top_bar.scroll
{
	box-shadow: 0 0 10px 0 rgba(1, 1, 1, 0.1);
}

.top_bar.hasbg
{
	background: transparent;
	-webkit-box-shadow: 0 1px 30px rgba(0, 0, 0, 0);
    -moz-box-shadow: 0 1px 30px rgba(0, 0, 0, 0);
    box-shadow: 0 1px 30px rgba(0, 0, 0, 0);
    border: 0;
}

html[data-menu=centeralign] body .top_bar.scroll #nav_wrapper
{
	border: 0;
}

.top_bar.noopacity
{
	background: #fff !important;
}

#mobile_menu
{
	display: none;
	cursor: pointer;
}

#menu_wrapper
{
	margin:auto;
	width: 960px;
	height: 100%;
}

body.centeralign #menu_wrapper
{
	margin-top: 20px;
}

body.centeralign .top_bar.scroll #menu_wrapper
{
	margin-top: 0;
}

#nav_wrapper
{
	float: left;
	display: table;
	width: 100%;
	height: 100%;
	text-align: center;
	border-top: 1px solid #ccc;
}

body.centeralign #nav_wrapper
{
	float: none;
	width: auto;
	margin: auto;
}

.nav_wrapper_inner
{
	display: table-cell;
    vertical-align: middle;
}

#menu_border_wrapper > div
{
	width: 100%;
	float: left;
}

#mobile_nav_icon
{
	display: none;
	font-size: 13px;
    position: relative;
    box-sizing: border-box;
}

body.tg_sidemenu_desktop #mobile_nav_icon
{
	display: inline-block;
}


#logo_right_button
{
	position: absolute;
	text-align: right;
	right: 30px;
}

.top_bar.hasbg #mobile_nav_icon
{
	border-color: #fff;
}

.header_client_wrapper
{
	display: inline-block;
	margin-right: 10px;
	position: relative;
}

body.leftmenu .mobile_menu_wrapper .header_client_wrapper
{
	position: absolute;
	bottom: 50px;
	margin: 0;
}

.header_client_wrapper span
{
	font-size: 16px;
	font-weight: 500;
	margin-right: 7px;
	vertical-align: baseline;
}

.header_client_wrapper .client_logout_link
{
	margin-left: 5px;
	margin-right: 5px;
}

.header_cart_wrapper
{
	display: inline-block;
	margin-right: 10px;
	position: relative;
}

.header_cart_wrapper .cart_count
{
	position: absolute;
	top: -10px;
	right: -10px;
	font-size: 10px;
	border-radius: 50px;
	background: #0067DA;
	color: #fff;
	z-index: 2;
	width: 16px;
	height: 16px;
	line-height: 18px;
	text-align: center;
}

.header_cart_wrapper span
{
	font-size: 16px;
	font-weight: 500;
}

body.admin-bar .mobile_menu_wrapper #mobile_menu_close.button
{
	top: 62px;
}

.mobile_menu_wrapper #mobile_menu_close.button
{
	position: fixed;
	top: 30px;
	right: 30px;
	border-radius: 250px;
	width: 40px;
	height: 40px;
	padding: 0;
    line-height: 42px !important;
    font-size: 16px;

    -ms-transform: scale(0);
    -moz-transform: scale(0);
    -o-transform: scale(0);
    -webkit-transform: scale(0);
    transform: scale(0);

    box-shadow: 0 8px 8px -6px rgba(0,0,0,.15);

    transition: all 0.1s ease;
	-webkit-transition: all 0.1s ease;
	-moz-transition: all 0.1s ease;
}

body.js_nav .mobile_menu_wrapper #mobile_menu_close.button
{
	-webkit-animation-delay: 1.5s;
    animation-delay: 1.5s;

	-ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
}

.mobile_menu_wrapper #mobile_menu_close.button:hover
{
	margin-top: -4px;
}

.mobile_menu_wrapper
{
	left: -10px;

	-webkit-transition: -webkit-transform 200ms ease;
	-moz-transition: transform 200ms ease;
	-o-transition: -o-transform 200ms ease;
	transition: transform 200ms ease;

	-webkit-transform: translate(-400px, 0px);
	-moz-transform: translate(-400px, 0px);
	transform: translate(-400px, 0px);

	-webkit-backface-visibility: hidden;
	-webkit-font-smoothing: subpixel-antialiased;
	 -webkit-overflow-scrolling: touch;

	 width: 400px;
	 padding: 90px;
	 box-sizing: border-box;
	 background: #111111;
	 position: fixed;
	 top: 0px;
	 height: 100%;
	 color: #999;
}

body.leftmenu .mobile_menu_wrapper
{
	z-index: 1;
}

.mobile_menu_content
{
	display: table;
	width: 100%;
	height: 100%;
}

.mobile_menu_wrapper .mobile_menu_content > div
{
	width: 100%;
	max-height: 100%;
    overflow: auto;
    display: table-cell;
    vertical-align: middle;
}

body.leftmenu .mobile_menu_wrapper .mobile_menu_content > div
{
	display: block;
	position: absolute;
	bottom: 100px;
	width: calc(100% - 100px);
    box-sizing: border-box;
}

.mobile_menu_wrapper .mobile_menu_content .social_wrapper
{
	margin-top: 20px;
}

.mobile_menu_wrapper .mobile_menu_content .social_wrapper ul li a i
{
	font-size: 24px;
	line-height: 24px;
}

body.admin-bar .mobile_menu_wrapper
{
	padding-top: 32px;
}

body.admin-bar #close_mobile_menu
{
	top: 32px;
}

body.js_nav .mobile_menu_wrapper, html[data-menu=leftmenu] body.js_nav .mobile_menu_wrapper
{
	-webkit-transform: translate(0px, 0px);
	-ms-transform: translate(0px, 0px);
	transform: translate(0px, 0px);
	-o-transform: translate(0px, 0px);
	overflow-y: scroll;
	overflow-x: hidden;
	-webkit-overflow-scrolling: touch;
	left: 0;
	z-index: 99;

	-webkit-box-shadow: -22px 0 40px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: -22px 0 40px rgba(0, 0, 0, 0.1);
    box-shadow: -22px 0 40px rgba(0, 0, 0, 0.1);
}

#close_mobile_menu
{
	position: fixed;
	top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

#close_mobile_menu.open
{
	z-index: 99;
}

.mobile_main_nav, #sub_menu
{
	margin-top: 40px;
	margin-bottom: 40px;
	list-style: none;
	overflow: hidden;
	width: 100%;
}

body.leftmenu .mobile_main_nav #sub_menu
{
	margin-top: 250px;
}

#sub_menu .sub-menu
{
	margin-left: 15px;
	margin-top: 5px;
    margin-bottom: 5px;
	list-style: none;
}

#sub_menu li
{
	width: 100%;
}

.mobile_main_nav li a, #sub_menu li a
{
	color: #777;
	width: 100%;
	display: block;
	font-size: 14px;
	font-weight: 600;
	line-height: 2em;
	text-transform: uppercase;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
}

.mobile_main_nav li a:hover, .mobile_main_nav li a:active, #sub_menu li a:active
{
	color: #fff;
}

.mobile_main_nav li ul.sub-menu
{
	display: none;
}

.mobile_main_nav.mainnav_in
{
	-webkit-animation: mainNavIn 0.4s;
	animation: mainNavIn 0.4s;
}

.mobile_main_nav.mainnav_out
{
	-webkit-animation: mainNavOut 0.4s;
	animation: mainNavOut 0.4s;
}

#sub_menu.subnav_out
{
	-webkit-animation: subNavOut 0.4s;
	animation: subNavOut 0.4s;
}

#sub_menu.subnav_in
{
	-webkit-animation: subNavIn 0.4s;
	animation: subNavIn 0.4s;
}

#sub_menu li ul a:after
{
	display: none;
}

#menu_back
{
	text-transform: uppercase !important;
	letter-spacing: 2px !important;
	font-size: 14px !important;
	margin-bottom: 5px;
	font-weight: 600 !important;
}

#menu_back:before
{
	font-size: 12px;
	margin-right: 0.7em;
	position: relative;
	display: inline;
	font-family: 'Font Awesome 5 Free';
	content: "\f104";
	font-weight: 900;
}

.overlay_background
{
	opacity: 0;
	visibility: hidden;
	background-color: rgba(256, 256, 256, 1);
	position: fixed;
	z-index: -2;
	top: 0;
	left:0;
	width: 100%;
	height: 100%;

	-ms-transform: scale(0.9);
    -moz-transform: scale(0.9);
    -o-transform: scale(0.9);
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
    transition: all 0.2s ease-out;
    -webkit-transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
}

#page_caption_overlay,
.content_overlay
{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1;
}

.ppb_wrapper .withbg .overlay_background
{
	z-index: 1;
	visibility: visible;
	opacity: 1;
	position: absolute;
	-ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
}

#side_menu_wrapper.overlay_background.share_open
{
	-ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    visibility: visible;
    z-index: 9;
    overflow: auto;
}

.overlay_background.share_open,
#side_menu_wrapper.overlay_background.share_open
{
	background-color: rgba(0, 0, 0, 0.9);
}

.overlay_background.visible
{
	opacity: 1;
	visibility: visible;
}

body #side_menu_wrapper
{
	background: transparent;
}

body.js_nav #side_menu_wrapper
{
  	display: none;
}

#close_share, .mobile_menu_wrapper #close_mobile_menu
{
	position: absolute;
    top: 42px;
    right: 40px;
    left: auto;
    z-index: -1;

    width: 20px;
    height: 40px;
    line-height: 42px;
    border-radius: 25px;
    background: #eee;
    color: #fff;
    text-align: center;
    padding: 0 10px 0 10px;
    cursor: pointer;
    z-index: 10;
    display: block;
    -webkit-transition: .3s ease-in-out;
    -moz-transition: .3s ease-in-out;
    -o-transition: .3s ease-in-out;
    transition: .3s ease-in-out;
}

#close_share:hover, .mobile_menu_wrapper #close_mobile_menu:hover
{
	-webkit-transform: translate(0px,-5px);
    -moz-transform: translate(0px,-5px);
    transform: translate(0px,-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    -webkit-transition: all 0.1s;
    -moz-transition: all 0.1s;
    transition: all 0.1s;
}

#close_share.open, .mobile_menu_wrapper #close_mobile_menu.open
{
    z-index: 99;
}

.nav, .subnav, .nav_page_number
{
	list-style: none;
	padding: 10px 0 10px 0;
}

.subnav
{
	background: none;
}

.nav > li, .nav_page_number > li
{
	display: inline-block;
}

.nav_page_number
{
	margin-top: 2px;
}

#menu_wrapper .nav ul, #menu_wrapper div .nav
{
	list-style: none;
	display: block;
	padding: 0;
	margin: 0;
	margin-bottom: 0;
}

#menu_wrapper .nav ul li, #menu_wrapper div .nav li
{
	display: inline-block;
	margin: 0;
	padding: 10px 8px 10px 8px;
}

body.centeralign #menu_wrapper div .nav > li
{
	padding: 10px 15px 10px 15px;
}

#menu_wrapper .nav ul li ul li, #menu_wrapper div .nav li ul li
{
	clear: both;
	width: 100%;
	margin: 0;
	text-align: left !important;
	padding: 5px 20px 5px 20px;
	box-sizing: border-box;
}

#menu_wrapper .nav ul li:last-child, #menu_wrapper div .nav li:last-child
{
	margin-right: 0;
}

#menu_wrapper .nav ul li a, #menu_wrapper div .nav li > a
{
	display: inline-block;
	padding: 0;
	margin: 0;
	color: #666;
	font-size: 12px;
	padding: 0;
	font-weight: 400;
	margin-right: 10px;
	font-family: "Jost", 'Helvetica Neue', Arial,Verdana,sans-serif;
	text-transform: uppercase;
	position: relative;
	padding: 0 0.5em;

	-webkit-transition: padding 0.3s;
	-moz-transition: padding 0.3s;
	transition: padding 0.3s;
}

#menu_wrapper .nav ul li a:before, #menu_wrapper div .nav li > a:before,
.post_attribute a:before
{
	content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: -1;
    opacity: 0.2;
    height: 50%;
    width: 100%;
    background-color: #666;
    transform-origin: right top;
    transform: scale(0, 1);
    transition: color 0.1s,transform 0.2s ease-out;
}

.post_attribute .ti_icon,
.post_detail .ti_icon
{
	margin-right: 5px;
	vertical-align: baseline;
}

#page_content_wrapper .inner .sidebar_wrapper a.no_effect:before
{
	display: none !important;
}

#menu_wrapper .nav ul li a:active:before, #menu_wrapper div .nav li > a:active:before
{
	background-color: #666;
}

#page_content_wrapper .inner .sidebar_wrapper a:not(.button)
{
	position: relative;
}

#menu_wrapper .nav ul li a:hover:before, #menu_wrapper div .nav li > a:hover:before,
.post_attribute a:hover:before
{
  	transform-origin: left top;
    transform: scale(1, 1);
}

#menu_wrapper .nav ul li a, #menu_wrapper div .nav li > a:first-child
{
	padding-left: 0;
}

#wrapper.transparent .top_bar:not(.scroll) #menu_wrapper div .nav > li > a,
#wrapper.transparent .top_bar:not(.scroll) #logo_right_button a#mobile_nav_icon,
#wrapper.transparent #logo_wrapper .social_wrapper ul li a, #wrapper.transparent .top_bar:not(.scroll) .header_cart_wrapper a,
#wrapper.transparent .top_bar:not(.scroll) .header_client_wrapper a,
#wrapper.transparent .top_bar:not(.scroll) .header_client_wrapper
{
	color: #fff !important;
}

#wrapper.transparent #menu_wrapper .nav ul li a:before,
#wrapper.transparent #menu_wrapper div .nav li > a:before
{
	background: #fff !important;
}

#menu_wrapper .nav li.arrow > a:after, #menu_wrapper div .nav li.arrow > a:after
{
	text-decoration: inherit;
	-webkit-font-smoothing: antialiased;
	display: inline;
	width: auto;
	height: auto;
	line-height: normal;
	vertical-align: 10%;
	background-image: none;
	background-position: 0% 0%;
	background-repeat: repeat;
	font-family: 'themify';
	content: "\e64b";
	float: right;
	margin-left: 8px;
	line-height: 3.4em;
}

#menu_wrapper .nav li.arrow > a:after, #menu_wrapper div .nav li.arrow > a:after
{

}

#menu_wrapper .nav ul li.arrow > a:after, #menu_wrapper div .nav li.arrow > a:after
{
	font-size: 7px;
}

#menu_wrapper .nav ul li a.hover, #menu_wrapper .nav ul li a:hover, #menu_wrapper div .nav li a.hover, #menu_wrapper div .nav li a:hover
{
	color: #444;
	z-index: 2;
	position: relative;
}

#menu_wrapper div .nav > li.current-menu-item > a, #menu_wrapper div .nav > li.current-menu-parent > a, #menu_wrapper div .nav > li.current-menu-ancestor > a
{
	color: #444;
	z-index: 2;
	position: relative;
}

#menu_wrapper .nav ul li ul, #menu_wrapper div .nav li ul
{
	list-style: none;
	background: transparent;
	position: absolute;
 	width: 220px;
 	height: 0;
 	padding: 0;
 	z-index: -1;
	margin: 0;
	margin-left: 0;
	margin-top: 10px;
	border: 0;
	-webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
	opacity: 0;
	overflow: hidden;

    -webkit-transform: translate(0px,-15px);
    -moz-transform: translate(0px,-15px);
    transform: translate(0px,-15px);

    transition: all 0.2s ease-out;
    -webkit-transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
}


#menu_wrapper .nav ul li ul li ul, #menu_wrapper div .nav li ul li ul
{
	position: absolute;
	left: 221px;
	margin-top: -39px;
	margin-left: 0;
	opacity: 0;
}

#menu_wrapper .nav ul li ul li ul:before, #menu_wrapper div .nav li ul li ul:before
{
	display: none;
}

#menu_wrapper .nav > li:hover > ul,
#menu_wrapper .nav > li > ul > li:hover > ul
{
    z-index: 9;
    opacity: 1;
    height: auto;
    -ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);

    -webkit-transform: translate(0px,0px);
    -moz-transform: translate(0px,0px);
    transform: translate(0px,0px);

    overflow: visible;
}

#menu_wrapper div .nav li ul li a, #1menu_wrapper div .nav li.current-menu-item ul li a, #menu_wrapper div .nav li ul li.current-menu-item a,#menu_wrapper .nav ul li ul li a, #menu_wrapper .nav ul li.current-menu-item ul li a, #menu_wrapper .nav ul li ul li.current-menu-item a, #menu_wrapper div .nav li.current-menu-parent ul li a, #menu_wrapper div .nav li ul li.current-menu-parent a
{
	display: block;
	background: transparent;
	height: auto;
}

#menu_wrapper .nav ul li ul li a, #menu_wrapper div .nav li ul li a, #menu_wrapper div .nav li.current-menu-parent ul li a
{
	border-top: 0;
	margin: 0;
	font-size: 11px;
	padding: 0;
	letter-spacing: 0;
	font-weight: 400;
	text-transform: none;
	box-sizing: border-box;

	-webkit-transition: color .2s linear, background .2s linear;
	-moz-transition: color .2s linear, background .2s linear;
	-ms-transition: color .2s linear, background .2s linear;
	-o-transition: color .2s linear, background .2s linear;
	transition: color .2s linear, background .2s linear;
}

#menu_wrapper .nav ul li:first-child > a,
#menu_wrapper .nav ul li ul li:first-child > a,
#menu_wrapper div .nav li.current-menu-parent ul li:first-child > a
{
	margin-top: 10px;
}

#menu_wrapper .nav ul li:last-child > a,
#menu_wrapper .nav ul li ul li:last-child > a,
#menu_wrapper div .nav li.current-menu-parent ul li:last-child > a
{
	margin-bottom: 10px;
}

#menu_wrapper .nav ul li.megamenu > ul, #menu_wrapper div .nav li.megamenu > ul
{
	position: absolute;
	width: 960px;
	left: 0;
	right: 0;
	margin-left:auto;
    margin-right:auto;
    padding: 0;
	box-sizing: border-box;
}

#menu_wrapper .nav ul li:not(.megamenu) > ul.sub-menu > li.arrow > a:after, #menu_wrapper div .nav li:not(.megamenu) > ul.sub-menu > li.arrow > a:after
{
	font-size: 7px;
	margin-left: 8px;
	text-decoration: inherit;
	-webkit-font-smoothing: antialiased;
	display: inline;
	width: auto;
	height: auto;
	line-height: normal;
	vertical-align: 10%;
	background-image: none;
	background-position: 0% 0%;
	background-repeat: repeat;
	margin-top: 0;
	font-family: 'themify';
    content: "\e649";
	float: right;
	margin-right: 0px;
	line-height: 3.4em;
}

#menu_wrapper div .nav li.megamenu ul li
{
	display: block;
	box-sizing: border-box;
	clear: none;
	float: left;
	border-left: 1px solid #eeeeee;
}

#menu_wrapper div .nav li.megamenu ul li > a
{
	display: none;
}

#menu_wrapper div .nav li.megamenu ul li:first-child
{
	border: 0;
}

#menu_wrapper div .nav li.megamenu.col2 ul > li.menu-item-has-children
{
	width: 50%;
	padding: 5px 15px 0 0;
}

#menu_wrapper div .nav li.megamenu.col3 ul > li.menu-item-has-children
{
	width: 33.3%;
	padding: 5px 15px 0 0;
}

#menu_wrapper div .nav li.megamenu.col4 ul > li.menu-item-has-children
{
	width: 25%;
	padding: 5px 15px 0 0;
}

#menu_wrapper .nav ul li.megamenu ul li ul, #menu_wrapper div .nav li.megamenu ul li ul
{
	position: relative;
	width: 100%;
	margin: 0;
	border: 0;
	box-shadow: 0 0 0;
	display: block !important;
	opacity: 1 !important;
	left: 0;
	height: auto;
    -ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
    overflow: visible;
}

#menu_wrapper .nav ul li.megamenu ul li ul li, #menu_wrapper div .nav li.megamenu ul li ul li
{
	width: 100% !important;
	border: 0 !important;
}

#menu_wrapper div .nav li.megamenu ul li > a, #menu_wrapper div .nav li.megamenu ul li > a:hover, #menu_wrapper div .nav li.megamenu ul li  > a:active
{
	color: #444;
	box-sizing: border-box;
	background: transparent;
}

#menu_wrapper .nav ul li.megamenu ul li ul li a, #menu_wrapper div .nav li.megamenu ul li ul li a
{
	color: #888;
	border-bottom: 0;
}

#menu_wrapper .nav ul li.megamenu ul li ul li, #menu_wrapper div .nav li.megamenu ul li ul li a
{
	width: auto;
	display: inline-block;
	margin-left: 5px;
	padding: 0;
}

@media only screen and (min-width: 1200px) {
	#menu_wrapper .nav ul li.megamenu > ul, #menu_wrapper div .nav li.megamenu > ul
	{
		max-width: 1425px;
		width: 100%;
		width: calc(100% - 180px);
		box-sizing: border-box;
	}
}

.top_contact_info_container
{
	display: table;
	float: right;
	height: 100%;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
}

.top_contact_info
{
	font-size: 12px;
	color: #222;
	float: right;
	line-height: 40px;
}

.top_contact_info i
{
	margin-right: 10px;
	color: #222;
	font-size: 12px;
	vertical-align: middle;
}

.top_contact_info span
{
	display: inline-block;
	margin-right: 10px;
}

.top_contact_info a
{
	color: #444;
}

#top_menu
{
	float: left;
	display: block;
	list-style: none;
}

#top_menu li
{
	float: left;
	margin-right: 20px;
}

#top_menu li a
{
	font-size: 13px;
	font-weight: 400;
	color: #222;
	line-height: 40px;
}

#footer_menu li ul
{
	display: none;
}

.elementor-megamenu-wrapper {
	text-align: left;
}

/*------------------------------------------------------------------
[3. Footer]
*/

.footer_photostream_wrapper
{
	margin-top: 20px;
	text-align: center;
}

.footer_photostream
{
	margin-top: 20px;
}

.footer_bar
{
	clear: both;
	width: 100%;
	margin: auto;
	padding: 0 0 10px 0;
	float: left;
	background: #fff;

	-webkit-transition: -webkit-transform 500ms ease;
	-o-transition: -o-transform 500ms ease;
	transition: -webkit-transform 500ms ease;
}

#footer_wrapper
{
	width: 100%;
	float: left;
}

body.tg_footer_reveal #footer_wrapper
{
	position: fixed;
	bottom: 0;
}

body.leftmenu #footer_wrapper
{
	width: calc(100% - 350px);
	margin-left: 350px;
}

#footer
{
	width: 100%;
	margin: auto;
	word-wrap: break-word;
	margin-bottom: 0;
	float: left;
    clear: both;
}

#footer.empty
{
	padding: 0;
}

#footer.ppb_wrapper
{
	margin-top: 0;
}

#footer a
{
	color: #000;
}

#footer a:hover, #footer a:active
{
	color: #000;
}

#footer .sidebar_widget li h2.widgettitle
{
	margin-top: 10px;
	margin-bottom: 10px;
}

#footer ul.sidebar_widget
{
	width: 960px;
	list-style: none;
	margin: 0;
	margin: auto;
	padding-top: 50px;
	padding-bottom: 50px;
}

#footer ul.sidebar_widget:after
	{
		content: '';
		display: block;
		clear: both;
	}

#footer ul.sidebar_widget li ul
{
	list-style: none;
	margin-left: 0;
}

#footer ul.sidebar_widget li ul li ul.children
{
	margin-left: 10px;
	margin-bottom: 0;
}

#footer ul.sidebar_widget li ul li ul.children li:last-child
{
	border: 0;
	margin-bottom: 0;
}

#footer .sidebar_widget.four > li
{
	width: 20%;
	float: left;
	margin-right: 5%;
}

#footer .sidebar_widget.four > li:nth-child(4)
{
	margin-right: 0;
	width: 25%;
}

#footer .sidebar_widget.three > li
{
	width: 31.33%;
	margin-right: 3%;
	float: left;
}

#footer .sidebar_widget.three > li:nth-child(3)
{
	margin-right: 0;
}

#footer .sidebar_widget.two > li
{
	width: 48%;
	float: left;
	margin-right: 2%;
}

#footer .sidebar_widget.two > li:nth-child(2)
{
	margin-right: 0;
	width: 50%;
}

#footer .sidebar_widget.one
{
	float: none;
}

#footer .sidebar_widget.one > li
{
	width: 100%;
	clear: both;
	float: left;
	margin-bottom: 30px;
	text-align: center;
}

#footer .sidebar_widget.one > li .social_wrapper.shortcode ul
{
	text-align: center;
}

#footer .sidebar_widget.one > li:last-child
{
	margin-bottom: 0;
}

#footer ul.sidebar_widget li ul.posts.blog li img
{
	width: 60px;
	float: left;
	margin: 0 15px 15px 0;
}

#footer ul.sidebar_widget li ul.posts.blog li
{
	clear: both !important;
	float: left;
	border: 0;
	width: 100%;
}

#footer ul.sidebar_widget li ul li
{
	margin: 0 0 10px 0;
}

#footer ul.sidebar_widget li ul li ul.sub-menu
{
	margin-left: 15px;
}

#footer ul.sidebar_widget li ul.flickr li
{
	margin: 0 2% 0 0;
	border-bottom: 0;
	width: 31%
}

#footer ul.sidebar_widget.one li ul.flickr li
{
	width: 8%;
	margin: 0 2% 1.2% 0;
}

#footer ul.sidebar_widget.two li ul.flickr li
{
	width: 15%;
	margin: 0 2% 1% 0;
}

#footer ul.sidebar_widget.three li ul.flickr li
{
	width: 20%;
	margin: 0 2% 0 0;
}

#footer ul.sidebar_widget li ul.flickr li img
{
	width: 100%;
	height: auto;
}

#copyright
{
	float: left;
	width: 50%;
	font-weight: normal;
	text-align: left;
}

.footer_bar.fullscreen #copyright
{
	color: #fff;
}

.footer_bar.fullscreen .footer_bar_wrapper, .footer_bar.wall .footer_bar_wrapper, .footer_bar.static .footer_bar_wrapper
{
	margin: auto;
	float: none;
}

.footer_bar_wrapper
{
	width: 960px;
	margin: auto;
	-webkit-transition: -webkit-transform 500ms ease;
	transition: -webkit-transform 500ms ease;
	-webkit-font-smoothing: antialiased;
	clear: both;
	color: #fff;
	border-top: 1px solid #444;
	padding-top: 20px;
}

#toTop
{
	width:45px;
	height: 45px;
	box-sizing: border-box;
    opacity: 0;
    text-align:center;
    padding:10px 10px 10px 10px;
    position:fixed; /* this is the magic */
    bottom: 10px;
    right: 10px;
    cursor:pointer;
	transition: color 300ms, background-color 300ms, opacity 300ms;
	-moz-transition: color 300ms, background-color 300ms, opacity 300ms;
	-o-transition: color 300ms, background-color 300ms, opacity 300ms;
	-webkit-transition: color 300ms, background-color 300ms, opacity 300ms;
	z-index: 9;
	background: rgba(0,0,0,0.1);
	color: #fff;
	display: block;
}

#toTop i
{
	font-size: 1.5em;
	line-height: 24px;
}

/*------------------------------------------------------------------
[4. Content]
*/

#content_wrapper
{
	width: 72%;
	float: left;
	margin: 0;
}

#page_content_wrapper:not(.wide), .page_content_wrapper:not(.wide)
{
	width: 960px;
	width: calc(100% - 180px);
	margin: auto;
	padding-bottom: 0;
	margin-top: 0;
}

body.home.blog #page_content_wrapper
{
	margin-top: 60px;
}

#page_content_wrapper.wide.nomargin, .page_content_wrapper.wide.nomargin
{
	margin-top: 0;
}

.page_content_wrapper,
.page_content_wrapper .inner,
.page_content_wrapper .inner .inner_wrapper
{
	width: 100%;
	float: left;
}

body:not(.elementor-page) #page_content_wrapper img
{
	max-width: 100%;
	height: auto;
}

body:not(.elementor-page) .comment_disable_clearer
{
	margin-bottom: 40px;
}

.standard_wrapper
{
	width: 960px;
	margin: auto;
	position: relative;
	float: none;
}

.standard_wrapper.withpadding
{
	padding-top: 30px;
	padding-bottom: 30px;
}

.page_content_wrapper.fullwidth, #page_content_wrapper.fullwidth
{
	width: 100%;
	padding: 0;
}

#page_content_wrapper .inner
{
	width: 100%;
	margin: auto;
	float: left;
}

#page_content_wrapper .inner .inner_wrapper
{
	padding-top: 0;
	width: 100%;
	float: left;
}

.type-post.classic.classic:first-child
{
	margin-top: 0;
}

.type-post.type-post.classic
{
	margin-bottom: 20px;
	margin-top: 20px;
}

.type-post.classic
{
	float: left;
}

.post.type-post.sticky .post_wrapper,
body:not(.single) .post:first-child.type-post.sticky .post_wrapper
{
	padding: 20px 30px 30px 30px;
	box-sizing: border-box;
}

body.page-template-blog-fg .post.type-post
{
	margin-bottom: 4%;
}

body.page-template-blog-fg .post.type-post .post_wrapper
{
	border: 0;
	padding: 0;
}

body.search-results .hentry
{
	float: left;
	width: 100%;
	margin-bottom: 35px;
	border: 0;
}

#page_content_wrapper .inner #blog_grid_wrapper.sidebar_content
{
	margin-right: 0;
	padding-right: 0;
	box-sizing: border-box;
	margin-bottom: 0;
}

#page_content_wrapper .inner #blog_grid_wrapper.sidebar_content.left_sidebar
{
	padding-right: 0;
}

#blog_grid_wrapper.sidebar_content:not(.full_width) .post.type-post, .post.type-post.grid_layout
{
	width: 48%;
	margin-top: 0;
	margin-bottom: 5%;
	float: left;
	border: 0;
}

#blog_grid_wrapper.sidebar_content:not(.full_width) .post.type-post:nth-child(even), .post.type-post.grid_layout.last
{
	float: right;
}

body.single .post.type-post
{
	border: 0;
	margin-bottom: 0;
}

.post.type-post.last-child
{
	border: 0;
	margin: 0;
	padding: 0;
}

#blog_grid_wrapper
{
	padding-top: 30px;
}

#blog_grid_wrapper.ppb_blog_posts, .blog_grid_wrapper.ppb_blog_posts
{
	padding-top: 0;
}

#blog_grid_wrapper .post.type-post, .blog_grid_wrapper .post.type-post
{
	margin-bottom: 20px;
	box-sizing: border-box;
	padding: 0;
	padding: 0;
	box-sizing: border-box;
	border: 0;
}

body.page-template-blog-g-php #blog_grid_wrapper .post.type-post,
body.error404 #blog_grid_wrapper .post.type-post,
.ppb_blog_posts .post.type-post,
body.archive #blog_grid_wrapper .post.type-post
{
	float: left;
	width: 31.66%;
	float: left;
	margin-right: 2.5%;
	margin-bottom: 3.5%;
	margin-top: 0;
}

body.page-template-blog-g-php #blog_grid_wrapper .post.type-post:nth-child(3n),
body.error404 #blog_grid_wrapper .post.type-post:nth-child(3n),
.ppb_blog_posts .post.type-post:nth-child(3n),
body.archive #blog_grid_wrapper .post.type-post:nth-child(3n)
{
	margin-right: 0;
}

body.page-template-blog-g-php #blog_grid_wrapper .post.type-post:nth-child(3n+1),
body.error404 #blog_grid_wrapper .post.type-post:nth-child(3n+1),
.ppb_blog_posts .post.type-post:nth-child(3n+1),
body.archive #blog_grid_wrapper .post.type-post:nth-child(3n+1)
{
	clear: both;
}

body.page-template-blog-g-php #blog_grid_wrapper .post.type-post.last,
.ppb_blog_posts .post.type-post.last
{
	margin-right: 0;
	float: right;
}

.post.type-post:last-child
{
	margin-bottom: 0;
}

.post_wrapper.grid_layout
{
	float: left;
}

body.single .post_wrapper
{
	padding-bottom: 0;
	word-break: break-word;
}

body.single .post_wrapper p:first-child
{
	padding-top: 0 !important;
}

body.single .post_related .post_wrapper
{
	width: 100%;
}

.post_related .post_img
{
	margin-bottom: 0;
}

.post_related .post_header_wrapper
{
	clear: both;
	float: left;
	width: 100%;
	box-sizing: border-box;
	text-align: left;
	padding: 25px 0 25px 0;
	background: #ffffff;
}

body:not(.single) .post:first-child .post_wrapper,
body.page-template-blog-gs-php .post:first-child .post_wrapper,
body.page-template-blog-gs-php .post:nth-child(2) .post_wrapper,
body.page-template-blog-gls-php .post:first-child .post_wrapper,
body.page-template-blog-gls-php .post:nth-child(2) .post_wrapper,
body.page-template-blog-g-php .post:first-child .post_wrapper,
body.page-template-blog-g-php .post:nth-child(2) .post_wrapper,
body.page-template-blog-g-php .post:nth-child(3) .post_wrapper,
body.error404 .post:first-child .post_wrapper,
body.error404 .post:nth-child(2) .post_wrapper,
body.error404 .post:nth-child(3) .post_wrapper,
.ppb_blog_grid .post:first-child .post_wrapper,
.ppb_blog_grid .post:nth-child(2) .post_wrapper,
.ppb_blog_grid .post:nth-child(3) .post_wrapper
{
	border-top: 0;
	padding-top: 0;
}

body.search .post_wrapper:first-child
{
	padding: 20px 0 25px 0;
	border: 0;
}

.post_wrapper.single
{
	width: 100%;
	margin: 0;
}

body.single .post-views
{
	display: none;
}

.post_header
{
	width: 100%;
	margin-bottom: 10px;
}

.post_header p
{
	text-align: left;
}

.post_header.search
{
	width: calc(100% - 50px);
	margin-bottom: 0;
	margin-left: 10px;
	text-align: left;
}

body.search-results .post_header.search
{
	width: calc(100% - 90px);
}

.post_header.quote
{
	margin-bottom: 0;
}

.readmore
{
	font-size: 12px;
	font-weight: 500;
	letter-spacing: 2px;
	text-transform: uppercase;
	display: inline-block;
	border-bottom: 1px solid #222;
}

.readmore span
{
	display: none;
}

.readmore:hover
{
	opacity: 1;
}

.post_header.grid
{
	margin-bottom: 15px;
	width: 100%;
	box-sizing: border-box;
}

.post_header.full
{
	width: 100%;
}

body.page-template-blog-f-php .post_excerpt_full
{
	margin-bottom: 0;
}

.post_header.grid h6
{
	font-size: 18px;
}

.post_header h3 a, .post_header.grid h6 a
{
	color: #222222;
}

.post_header h5
{
	font-size: 24px;
	margin: 10px 0 10px 0;
}

.post_header h6
{
	font-size: 20px;
}

.post_header h5 a, .post_header h6 a
{
	color: #222222;
}

.post_attribute a
{
	position: relative;
	padding: 3px 0 3px 0;
}

.post_attribute a:before
{
	opacity: 0.5;
}

.post_excerpt
{
	width: 100%;
	margin: auto;
}

.post_excerpt br:first:child
{
	display: none;
}

.post_excerpt.post_tag
{
	width: auto;
	float: left;
	margin: initial;
	text-align: left;
	margin-top: 20px;
}

.post_excerpt.post_tag a
{
	color: #444;
	display: inline-block;
	border: 0;
	background: #f0f0f0;
	padding: 3px 15px 3px 25px;
	margin-right: 10px;
	margin-bottom: 10px;
	position: relative;
	font-size: 12px;
	line-height: 2.3;
}

.post_excerpt.post_tag a:before
{
	background: #fff;
	border-radius: 10px;
	content: '';
	height: 6px;
	left: 10px;
	position: absolute;
	width: 6px;
	top: 14px;
}

.post_excerpt.post_tag a:after
{
	background: #fff;
	border-bottom: 17px solid transparent;
	border-left: 10px solid #f0f0f0;
	border-top: 17px solid transparent;
	content: '';
	position: absolute;
	right: 0;
	top: 0;
}

.post_share_text
{
	float: right;
	margin-top: 35px;
	cursor: pointer;
	font-size: 14px;
	color: #fff;
	background: #EFA697;
	border-radius: 250px;
	border: 2px solid #EFA697;
	width: 30px;
	height: 30px;
	text-align: center;
	line-height: 30px;

	-webkit-transition: color .2s linear, background .1s linear;
	-moz-transition: color .2s linear, background .1s linear;
	-ms-transition: color .2s linear, background .1s linear;
	-o-transition: color .2s linear, background .1s linear;
	transition: color .2s linear, background .1s linear;
}

.post_share_text:hover
{
	background: #000;
	border-color: #000;
}

.post_share_text i
{
	font-size: 14px;
}

#about_the_author
{
	margin: 60px 0 0 0;
	border-top: 1px solid #dce0e0;
	border-bottom: 1px solid #dce0e0;
	padding: 40px 0 40px 0;
	box-sizing: border-box;
}

#about_the_author:after
{
	content: " ";
	display: block;
	height: 0;
	clear: both;
}

#about_the_author .gravatar
{
	float: left;
	margin-right: 20px;
}

#about_the_author .author_detail
{
	float: left;
	width: calc(100% - 120px);
}

.author_content h4
{
	font-size: 20px;
	font-weight: 400;
	margin-bottom: 10px;
}

.post_related
{
	margin-top: 40px;
}

.post_related h5
{
	margin-bottom: 30px;
}

.post_content_wrapper
{
	margin: 0;
	clear: both;
	width: 100%;
	box-sizing: border-box;
}

.post_content_wrapper.fullwidth
{
	width: 100%;
}

.recent_post_detail, .post_detail, .thumb_content span .portfolio_excerpt, .testimonial_customer_position, .testimonial_customer_company
{
	width: 100%;
	padding: 0;
	float: left;
	margin: 5px 0 0 0;
	font-weight: 400;
}

.search_form_wrapper
{
	margin-bottom: 60px;
}

body.search .search_form_wrapper
{
	margin-top: 0;
}

.search_form_wrapper .content
{
	font-size: 16px;
	text-align: center;
	width: 60%;
	margin: auto;
	margin-bottom: 40px;
}

.post_header .post_detail.grid
{
	margin-top: 5px;
}

.post_header .post_detail.full
{
	width: 100%;
	margin-top: 7px;
	margin-bottom: 7px;
}

.post_header .post_detail.grid
{
	margin-bottom: 0;
}

.post_attribute, .comment_date, .post-date
{
	padding: 0;
	margin-top: 0;
	color: #9B9B9B;
	margin-top: 10px;
	font-size: 14px;
}

.post_button_wrapper .post_attribute
{
	opacity: 0.5;
	font-size: 14px;
}

.post_info_cat
{
	font-size: 11px;
	margin-bottom: 5px;
}

.post_detail.single_post a,
.post_detail.single_post a:hover,
.post_detail.single_post a:active
{
	text-transform: uppercase;
	border-bottom: 0;
	letter-spacing: 2px;
}

.post_detail.single_post
{
	text-transform: uppercase;
	margin-bottom: 5px;
	font-size: 11px;
	letter-spacing: 1px;
}

body.single .post_detail.single_post.related
{
	margin-bottom: 5px;
}

.search_thumb
{
	float: left;
	border-radius: 200px;
	width: 60px;
	height: 60px;
	overflow: hidden;
	position: relative;
	margin-top: 5px;
}

body.search-results .search_thumb
{
	margin-right: 15px;
}

.search_thumb img
{
	width: 60px;
	height: 60px;
}

.rev_slider_wrapper
{
	z-index: 1;
}

#page_caption.hasbg .post_detail, #page_caption.hasbg .post_detail a, #page_caption.hasbg .post_detail a:hover, #page_caption.hasbg .post_detail a:active, .page_tagline
{
	padding: 0;
	color: #fff;
	margin-top: 10px;
}

#page_caption.hasbg .post_attribute
{
	color: #fff;
	margin-top: 25px;
}

#page_caption .post_detail
{
	float: none;
}

.post_detail_wrapper
{
	float: left;
	width: 96%;
	margin: 0 0 10px 0;
}

.gravatar
{
	position: relative;
	z-index: 2;
	overflow: hidden;
	float: left;
	width: 60px;
	height: 60px;
	border-radius: 200px;
}

body.single-post #page_content_wrapper .inner .sidebar_content.full_width .gravatar
{
	width: 100px;
	height: 100px;
}

body.single-post #page_content_wrapper .inner .sidebar_content.full_width .gravatar img
{
	max-width: 100px !important;
}

.comment .gravatar
{
	width: 60px;
	height: 60px;
}

.gravatar img
{
	max-width: 60px !important;
}

#about_the_author .header span
{
	display: block;
	padding: 10px 0 10px 20px;
	font-size: 14px;
}

#about_the_author .thumb
{
	width: 80px;
	float: left;
	margin: 20px 0 0 20px;
}

#about_the_author .thumb img
{
	padding: 3px;
	width: 50px;
}

#about_the_author .description
{
	width: 550px;
	float: left;
	padding: 0 0 0 20px;
}

.comment .left img.avatar
{
	width: 50px;
	height: 50px;
}

.comment
{
	width: 100%;
	padding: 30px 0 25px 0;
	float: left;
	border-bottom: 1px solid #dce0e0;
}

.fullwidth_comment_wrapper .comment
{
	width: 100%;
}

.comment .left
{
	float: left;
	margin-right: 15px;
}

.comment .right
{
	width: calc(100% - 85px);
	float: left;
	margin-left: 25px;
	padding: 0;
}

.fullwidth_comment_wrapper .comment .right
{
	width: calc(100% - 85px);
}

.fullwidth_comment_wrapper.sidebar .comment .right
{
	width: calc(100% - 85px);
}

.fullwidth_comment_wrapper.sidebar .comment .right.fullwidth
{
	width: 100%;
	margin-left: 0;
}

.comment .right p
{
	margin: 0;
	padding: 0;
}

.comment .right strong
{
	font-weight: #fff;
}

#page_content_wrapper .inner .sidebar_content ul.children
{
	width: 100%;
	float: left;
	margin-left: 0;
	padding-left: 30px;
	box-sizing: border-box;
}

.comment_date
{
	margin: 0px;
}

ul.children .comment
{
	width: 100%;
	margin: 0;
	padding: 30px 0 25px 0;
	float: left;
}

ul.children .comment:first-child
{
	margin-top: 0px;
}

.social_wrapper
{
	width: 100%;
	margin: 20px 0 5px 0;
}

#logo_wrapper .social_wrapper
{
	width: auto;
	margin: 0;
	left: 30px;
	text-align: left;
	position: absolute;
}

.footer_bar_wrapper .social_wrapper
{
	width: 50%;
	float: right;
	text-align: right;
	margin: 0;
	margin-top: -2px;
}

.above_top_bar .social_wrapper
{
	margin: 0;
	float: right;
	width: auto;
}

.social_wrapper.shortcode, .social_wrapper.shortcode ul
{
	margin: 0;
}

#page_content_wrapper .sidebar .content .textwidget .social_wrapper.shortcode, .page_content_wrapper .sidebar .content .textwidget .social_wrapper.shortcode, #page_content_wrapper .sidebar .content .textwidget .social_wrapper.shortcode ul, .page_content_wrapper .sidebar .content .textwidget .social_wrapper.shortcode ul
{
	text-align: center;
}

.social_wrapper ul
{
	list-style: none;
	margin-left: 0 !important;
}

.above_top_bar .social_wrapper ul
{
	text-align: right;
}

.footer_bar_wrapper .social_wrapper ul
{
	text-align: right;
}

#page_content_wrapper .social_wrapper ul
{
	list-style: none;
	margin-top: 12px;
}

#page_content_wrapper .social_wrapper.shortcode ul
{
	margin-top: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li .social_wrapper.shortcode ul, .page_content_wrapper .sidebar .content .sidebar_widget li .social_wrapper.shortcode ul
{
	margin-bottom: 0;
}

.social_wrapper ul li, .social_wrapper.small ul li
{
	display: inline-block;
	margin-right: 15px;
}

#logo_wrapper .social_wrapper ul li
{
	margin-right: 10px;
}

.social_wrapper.shortcode ul li, .social_wrapper.small.shortcode ul li
{
	margin: 0 5px 10px 5px !important;
}

.footer_bar_wrapper .social_wrapper ul li
{
	background: transparent !important;
	border: 0 !important;
	width: 16px;
	height: 16px;
	margin-left: 20px;
	margin-right: 0;
	float: right;
}

.social_wrapper.light ul li
{
	border: 0 !important;
}

.social_wrapper.large ul li
{
	width: 50px;
	height: 50px;
}

.above_top_bar .social_wrapper ul li
{
	background: transparent;
	border: 0;
	width: auto;
	height: auto;
	margin-right: 0px;
}

.above_top_bar .social_wrapper ul li
{
	background: transparent;
	border: 0;
	width: auto;
	height: auto;
	margin-right: 0px;
}

.social_wrapper.shortcode ul li
{
	clear: none !important;
	float: none !important;
	width: auto !important;
	display: inline-block !important;
}

.social_wrapper.shortcode ul li a
{
	display: block;
	width: 40px;
	height: 40px;
	color: #fff !important;
	background: #000;
	border-radius: 250px;
	line-height: 42px;
	text-align: center;
}

.social_wrapper.shortcode ul li a i
{
	position: relative;
	top: 1px;
}

.social_wrapper.shortcode ul li.facebook a
{
	background: #2D5F9A;
}

.social_wrapper.shortcode ul li.twitter a
{
	background: #00C3F3;
}

.social_wrapper.shortcode ul li.pinterest a
{
	background: #bd081c;
}

.social_wrapper.shortcode ul li.google a
{
	background: #db4437;
}

.social_wrapper.shortcode ul li.youtube a
{
	background: #cc181e;
}

.social_wrapper.shortcode ul li.flickr a
{
	background: #FF0084;
}

.social_wrapper.shortcode ul li.vimeo a
{
	background: #00ADEF;
}

.social_wrapper.shortcode ul li.tumblr a
{
	background: #36465d;
}

.social_wrapper.shortcode ul li.dribbble a
{
	background: #EA4C89;
}

.social_wrapper.shortcode ul li.linkedin a
{
	background: #0077B5;
}

.social_wrapper.shortcode ul li.instagram a
{
	background: #405de6;
}

.social_wrapper.shortcode ul li.behance a
{
	background: #1769ff;
}

.sidebar_content ul li .social_wrapper.shortcode, .sidebar_content ul li .social_wrapper.shortcode ul
{
	text-align: center !important;
}

.mobile_menu_wrapper .sidebar_wrapper h2.widgettitle:before
{
	border: 0;
}

.mobile_menu_wrapper .sidebar_wrapper h2.widgettitle span
{
	background: transparent;
	padding-left: 0;
	padding-right: 0;
}

.above_top_bar .social_wrapper ul li:last-child a
{
	margin-right: 0;
}

.above_top_bar .social_wrapper ul li a
{
	display: inline-block;
	color: #fff;
	padding: 0;
	margin-left: 5px;
	margin-right: 5px;
	line-height: 30px;
}

.above_top_bar .page_content_wrapper
{
	margin: auto;
	width: 960px;
	float: none;
}

.footer_bar_wrapper .social_wrapper ul li a
{
	color: #fff;
}

.social_wrapper ul li a i, .social_wrapper.small ul li a i
{
	line-height: 18px;
	font-size: 18px;
}

.above_top_bar .social_wrapper ul li a i
{
	font-size: 14px;
	line-height: 18px;
}

.footer_bar_wrapper .social_wrapper ul li a i
{
	line-height: 20px;
	font-size: 18px;
}

.social_wrapper.large ul li a i
{
	line-height: 32px;
	font-size: 24px;
}

.above_top_bar .social_wrapper ul li:hover
{
	background: transparent !important;
	opacity: 1;
}

#page_caption
{
	padding: 25px 0 30px 0;
	width: 100%;
	margin-bottom: 65px;
	background: #ffffff;
}

body.single-product #page_caption
{
	padding-bottom: 120px;
}

body.single-product div.product.type-product
{
	background: #fff;
    padding: 40px;
    border-radius: 5px;
    margin-top: -100px;
    -webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.1);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.1);
}

body.ppb_enable #page_caption
{
	margin-bottom: 0;
}

#page_caption.single_gallery
{
	margin-bottom: 0;
	border: 0;
}

#page_caption.hasbg
{
	height: 60%;
	max-height: 750px;
	position: relative;
	top: 0;
	right: 0;
	width: 100%;
	border: 0;
	padding: 0;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
}

.post_caption
{
	text-align: center;
	margin: 50px 0 30px 0;
}

#page_content_wrapper.hasbg .post_caption
{
	margin-top: 30px;
}

.post_caption .post_detail
{
	font-size: 14px;
	display: inline-block;
	width: auto;
	float: none;
}

.parallax_overlay_header
{
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.4);
	position: absolute;
	top: 0;
	left: 0;
	z-index: 2;
}

#page_caption.nomargin
{
	margin-bottom: 0;
}

#page_caption .page_title_wrapper
{
	width: 100%;
	text-align: center;
}

body.single-post #page_caption .page_title_wrapper
{
	width: 100%;
}

body.single-post #page_caption .post_detail
{
	margin-top: 10px;
}

#page_caption.hasbg
{
	width: 100%;
    height: 600px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    position: relative;
}

#page_caption.hasbg .page_title_wrapper
{
    margin: auto;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

#page_caption.hasbg .page_title_wrapper .standard_wrapper
{
	display: table;
    width: 100%;
    height: 100%;
}

#page_caption.hasbg .page_title_wrapper .page_title_inner
{
    position: relative;
    display: table-cell;
    vertical-align: middle;
}

#page_caption.hasbg .page_title_wrapper .page_title_inner .page_title_content
{
	width: 80%;
	margin: auto;
}

#page_caption.hasbg .page_title_wrapper .page_title_inner .page_title_content.title_align_left
{
	margin: 0;
	width: 100%;
}

#page_caption.hasbg h1,
#page_caption.hasbg .page_tagline
{
	color: #fff;
}

.page_tagline
{
	width: auto;
	margin: auto;
	float: none;
	margin-top: 10px;
}

.overlay_gallery_content .page_tagline
{
	margin-bottom: 20px;
	max-width: 90%;
}

#page_caption.hasbg .page_tagline
{
	border: 0;
}

.page_title_content.title_align_center
{
	text-align: center;
}

.page_title_content.title_align_right
{
	text-align: right;
}

body.single-post #page_content_wrapper.blog_wrapper .page_title_content
{
	margin-bottom: 60px;
	padding-bottom: 60px;
	border-bottom: 1px solid #d8d8d8;
}

body.single-post #page_content_wrapper.blog_wrapper .page_title_content h1
{
	width: 80%;
	margin: auto;
	margin-top: 20px;
    margin-bottom: 20px;
}

body.single-post #page_content_wrapper.blog_wrapper .page_title_content.title_align_left h1
{
	width: 100%;
}

.one_half
{
	float: left;
	width: 48%;
	margin-right: 3.5%;
	box-sizing: border-box;
}

.one_half.last
{
	float: right;
	width: 48%;
	margin-right: 0;
	clear: right;
}

.one_third
{
	width: 30.66%;
	float: left;
	margin-right: 4%;
	margin-bottom: 2%;
	position: relative;
	box-sizing: border-box;
}

.one_third.last
{
	margin-right: 0 !important;
	float: right;
}

body.single-post .video_wrapper
{
	margin-bottom: 20px;
}

.video_wrapper {
	position: relative;
	padding-bottom: 56.25%; /* 16:9 */
	padding-top: 25px;
	height: 0;
}

.video_wrapper iframe {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.post_navigation
{
	position: fixed;
    top: calc(50% + 60px);
    z-index: 1;
}

.post_navigation h7
{
	font-size: 18px;
}

.post_navigation.previous
{
	left: 0;
}

.post_navigation.next
{
	right: 0;
}

.post_navigation.previous .navigation_anchor,
.post_navigation.next .navigation_anchor
{
	-webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    margin-left: -40px;

    font-size: 11px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.post_navigation.next .navigation_anchor
{
	-webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    margin-left: 0;
    margin-right: -20px;
}

.post_navigation .navigation_post_content
{
	position: absolute;
    background: #fff;
    width: 350px;
    min-height: 350px;
    box-sizing: border-box;
    -webkit-transition: ease -webkit-transform 500ms, opacity ease 500ms;
    transition: ease transform 500ms, opacity ease 500ms;
    opacity: 0;
    top: -170px;

    padding: 30px 30px 30px 100px;
    -webkit-transform: translateX(-350px);
    -ms-transform: translateX(-350px);
    transform: translateX(-350px);
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
}

.post_navigation.next .navigation_post_content
{
	padding: 30px 100px 30px 30px;
	-webkit-transform: translateX(350px);
    -ms-transform: translateX(350px);
    transform: translateX(350px);
}

.post_navigation.next:hover .navigation_post_content
{
	-webkit-transform: translateX(-250px);
    -ms-transform: translateX(-250px);
    transform: translateX(-250px);
    opacity: 1;
}

.post_navigation.previous:hover .navigation_post_content
{
	-webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
}

.post_navigation .navigation_post_content .post_img
{
	margin: 0;
	margin-bottom: 15px;
	width: 220px;
	float: none;
}

.post_navigation .navigation_post_content .post_img img
{
	width: auto;
	max-height: 220px;
}

#right_click_content
{
	position: fixed;
	opacity: 0;
	visibility: hidden;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: rgba(0,0,0,0.5);
	color: #fff;
	font-size: 22px;
	text-align: center;

	transition: all 0.5s linear;
}

#right_click_content.visible
{
	opacity: 1;
	z-index: 999;
	visibility: visible;

	transition: all 0.5s linear;
}

#right_click_content .right_click_content_table
{
	display: table;
	width: 100%;
	height: 100%;
}

#right_click_content .right_click_content_table .right_click_content_cell
{
	display: table-cell;
	vertical-align: middle;
}

#right_click_content .right_click_content_table .right_click_content_cell > div
{
	width: 40%;
	margin: auto;
}

.ba-slider .handle:after
{
	box-shadow: none !important;
	background: transparent !important;
    border: 2px solid #fff !important;
    font-family: 'themify' !important;
    font-size: 24px !important;
    line-height: 61px !important;
    content: '\e658' !important;
}

.ba-slider .handle.draggable:after
{
	font-size: 18px !important;
    line-height: 45px !important;
}

.ba-slider .handle
{
	width: 1px !important;
	font-family: 'themify' !important;
	background: rgba(256,256,256,0.2) !important;
}

.post_related .one_half,
.post_related .one_third
{
	text-align: center;
}

body.error404 .searchform input[type=text],
body.search .searchform input[type=text]
{
	width: 100%;
}

/*------------------------------------------------------------------
[5. Social Sharing]
*/

body.home.blog .type-post,
body.category .type-post,
body.archive .type-post,
body.tag .type-post
{
	margin-bottom: 60px;
}

.post_img
{
	position: relative;
	height: auto;
	line-height: 0;
	width: 100%;
	margin-bottom: 20px;
}

.post_img img
{
	max-width: 100%;
	height: auto !important;
}

.post_img.team
{
	width: 100%;
	margin: auto;
	float: none;
	margin-bottom: 0;
	overflow: hidden;
}

.post_img.team img
{
	max-width: 100%;
}

.post_ft_img
{
	opacity: 0;
}

.post_img.small.square_thumb
{
	width: auto;
}

body.single-post .post_img.post_ft img
{
	width: auto;
	margin-bottom: 30px;
}

#content_slider_wrapper
{
	width: 100%;
	height: 20px;
	background: transparent;
	position: fixed;
	bottom: 200px;
}

#social_share_wrapper, .social_share_wrapper
{
	margin-left: 0 !important;
	margin-top: 20px;
	padding-top: 20px;
	margin-bottom: 20px;
	text-align: center;
	clear: both;
}

#social_share_wrapper a i, .social_share_wrapper.shortcode a i
{
	font-size: 20px;
}

.social_share_wrapper.shortcode
{
	margin: 0;
}

#social_share_wrapper h5,  .social_share_wrapper.shortcode h5
{
	display: inline-block;
	margin-bottom: 5px;
}

#social_share_wrapper ul, .social_share_wrapper.shortcode ul, .social_share_bubble ul
{
	display: block;
	clear: both;
	list-style: none;
	margin: 0;
	margin-left: 0 !important;
}

#social_share_wrapper ul li, .social_share_wrapper.shortcode ul li, .social_share_bubble ul li
{
	display: block;
	margin: 0 0 5px 0;
}

.social_share_wrapper.shortcode ul li
{
	display: inline-block;
	margin: 0 10px 0 10px;
	border-left: 0 !important;
}

#overlay_background
{
	display: none;
}

.multi_share
{
  position: absolute;
  z-index: 999;
  right: 30px;
  top: -55px;
}
.multi_share_button {
  display: inline-flex;
  font-family: 'Roboto', sans-serif;
  text-decoration: none;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: pointer;
  white-space: nowrap;
  padding: 8px 20px;
  font-size: 14px;
  background-color: #E91E63;
  border-radius: 2px;
  color: white;
  text-transform: uppercase;
  border: none;
  transition: all 0.2s ease-in;
  background-position: 50%;
  outline: none !important;
}
.multi_share_button.multi_share_button_circle {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  padding: 0 !important;
  font-size: 18px;
}
.multi_share input {
  display: none;
}
.multi_share input:not(:checked) ~ label {
  transform: rotate(0);
}
.multi_share input:not(:checked) ~ label .multi_share_button {
  border-radius: 0;
  height: 30px;
  width: 30px;
  box-shadow: none;
}
.multi_share input:not(:checked) ~ label .multi_share_button:first-child {
  border-top-left-radius: 100%;
  transform: translate(25px, 25px);
}
.multi_share input:not(:checked) ~ label .multi_share_button:nth-child(2) {
  border-top-right-radius: 100%;
  transform: translate(55px, 25px);
}
.multi_share input:not(:checked) ~ label .multi_share_button:nth-child(3) {
  border-bottom-left-radius: 100%;
  transform: translate(25px, 55px);
}
.multi_share input:not(:checked) ~ label .multi_share_button:nth-child(4) {
  border-bottom-right-radius: 100%;
  transform: translate(55px, 55px);
}
.multi_share input:not(:checked) ~ label .multi_share_button .icon {
  opacity: 0;
}
.multi_share input:not(:checked) ~ label > .icon {
  opacity: 1;
  cursor: pointer;
}
.multi_share label {
  position: relative;
  height: 80px;
  width: 80px;
  display: block;
  transform: rotate(45deg);
  transition: all 0.2s ease-in;
  cursor: default;
}
.multi_share label .multi_share_button {
  position: absolute;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
.multi_share label .multi_share_button:nth-child(2) {
  transform: translate(60px, 0);
}
.multi_share label .multi_share_button:nth-child(3) {
  transform: translate(0, 60px);
}
.multi_share label .multi_share_button:nth-child(4) {
  transform: translate(60px, 60px);
}
.multi_share label .multi_share_button .icon {
  opacity: 1;
  transition: all 0.15s ease-in-out;
  transform: rotate(-45deg);
}
.multi_share label > .icon {
  color: white;
  position: absolute;
  border-radius: 50%;
  display: flex;
  font-size: 24px;
  align-items: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  top: 30px;
  left: 30px;
  opacity: 0;
  transition: all 0.25s ease-in-out;
}
.multi_share label .icon {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/*------------------------------------------------------------------
[6. Sidebar]
*/

#page_content_wrapper .inner .sidebar_content, .page_content_wrapper .inner .sidebar_content
{
	width: 68%;
	padding: 0;
	float: left;
	padding-right: 0px;
	padding-top: 0;
	margin-right: 10px;
}

#page_content_wrapper .inner .sidebar_content.page_content, .page_content_wrapper .inner .sidebar_content.page_content
{
	box-sizing: border-box;
}

.page_content_wrapper .inner .sidebar_content.page_content
{
	background: transparent;
	padding: 0;
}

#page_content_wrapper .inner #portfolio_filter_wrapper.sidebar_content
{
	width: 100%;
	padding-right: 0;
	margin-right: 0;
	border: 0;
}

#page_content_wrapper .inner .sidebar_content.nopadding, .page_content_wrapper .inner .sidebar_content.nopadding
{
	padding-top: 0 !important;
}

#page_content_wrapper .inner .sidebar_content.left_sidebar, .page_content_wrapper .inner .sidebar_content.left_sidebar
{
	border-right: 0;
	float: right;
	margin-right: 0;
}

#page_content_wrapper .inner .sidebar_content.full_width, .page_content_wrapper .inner .sidebar_content.full_width
{
	width: 100%;
	margin-top: 0;
	margin-right: 0;
	border: 0;
}

#page_content_wrapper.hasbg.nomargintop
{
	margin-top: 0 !important;
}

#page_content_wrapper .inner .sidebar_content.full_width.nopadding, .page_content_wrapper .inner .sidebar_content.full_width.nopadding
{
	padding: 0 !important;
}

body:not(.elementor-page) #page_content_wrapper .inner .sidebar_content img,
body:not(.elementor-page) .page_content_wrapper .inner .sidebar_content img
{
	max-width: 100%;
	height: auto;
}

#page_content_wrapper .inner .sidebar_content .map_shortcode_wrapper img, .page_content_wrapper .inner .sidebar_content .map_shortcode_wrapper img
{
	max-width: none;
}

#page_content_wrapper .inner .sidebar_content div.wp-caption, .page_content_wrapper .inner .sidebar_content div.wp-caption
{
	max-width: 100%;
}

#page_content_wrapper .inner .sidebar_content.full_width#blog_grid_wrapper, .page_content_wrapper .inner .sidebar_content.full_width.blog_grid_wrapper
{
	width: 100%;
	padding: 0;
}

#page_content_wrapper .inner .sidebar_wrapper,
.page_content_wrapper .inner .sidebar_wrapper
{
	width: 27.99%;
	float: right;
	margin-top: 0;
	margin-left: 0;
	padding-top: 0;
}

#page_content_wrapper .inner .sidebar_wrapper.adjust, #page_content_wrapper .inner .sidebar_wrapper.left_sidebar.adjust
{
	padding-top: 50px;
}

#page_content_wrapper .inner .sidebar_wrapper.left_sidebar, .page_content_wrapper .inner .sidebar_wrapper.left_sidebar
{
	margin-right: 0;
	margin-left: 0;
	float: left;
}

body.woocommerce #page_content_wrapper .inner .sidebar_wrapper.left_sidebar, body.woocommerce .page_content_wrapper .inner .sidebar_wrapper.left_sidebar
{
	margin-top: 15px;
}

#page_content_wrapper .inner .sidebar_wrapper a:not(.button), .page_content_wrapper .inner .sidebar_wrapper a:not(.button)
{
	color: #222;
}

#page_content_wrapper .inner .sidebar_wrapper .sidebar, .page_content_wrapper .inner .sidebar_wrapper .sidebar
{
	width: 100%;
	float: left;
	margin-bottom: 40px;
}

#page_content_wrapper .inner .sidebar_wrapper .sidebar .content, .page_content_wrapper .inner .sidebar_wrapper .sidebar .content
{
	width: 100%;
	margin: 0 0 0 0;
}

.mobile_menu_wrapper .sidebar_wrapper h2.widgettitle
{
	color: #fff;
}

.mobile_menu_wrapper .sidebar_wrapper a
{
	color: #777;
}

.mobile_menu_wrapper .page_content_wrapper
{
	width: 100%;
}

.mobile_menu_wrapper .sidebar_wrapper
{
	width: 100%;
	float: left;
}

.mobile_menu_wrapper .sidebar_wrapper .sidebar .content .sidebar_widget li
{
	margin-bottom: 20px;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget_rss ul li
{
	margin-bottom: 40px;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget_rss ul li:last-child
{
	margin-bottom: 0px;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget_rss ul li a.rsswidget
{
	font-weight: 600;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget_rss ul li .rss-date
{
	opacity: 0.5;
	clear: both;
	display: block;
	font-size: 14px;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget_rss ul li .rssSummary
{
	font-size: 14px;
	margin: 15px 0 15px 0;
}

/*------------------------------------------------------------------
[7. Form & Input]
*/

input[type=text], input[type=password], input[type=email], input[type=url], input[type=date], input[type=tel], input.wpcf7-text, .woocommerce table.cart td.actions .coupon .input-text, .woocommerce-page table.cart td.actions .coupon .input-text, .woocommerce #content table.cart td.actions .coupon .input-text, .woocommerce-page #content table.cart td.actions .coupon .input-text
{
	padding: 10px;
	font-size: 100%;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
	margin: 0;
	background: #fff;
	border: 1px solid #222222;
	outline: none;
	-webkit-transition: border-color linear .3s;
	-moz-transition: border-color linear .3s;
	-o-transition: border-color linear .3s;
	transition: border-color linear .3s;
	box-sizing: border-box;
	-webkit-appearance: none;
}

p.input_wrapper
{
	position: relative;
	padding: 0 !important;
}

.input_effect ~ .focus-border
{
    display: none;
}

select
{
    padding: 10px;
    background: #fff;
    min-width: 100%;
    outline: none;
    box-sizing: border-box;
}

.woocommerce select.orderby
{
	height: auto;
}

input[type=text]:focus, input[type=password]:focus, input[type=email]:focus, input[type=url]:focus, input[type=tel]:focus, input[type=date]:focus, input.wpcf7-text:focus, .woocommerce table.cart td.actions .coupon .input-text:focus, .woocommerce-page table.cart td.actions .coupon .input-text:focus, .woocommerce #content table.cart td.actions .coupon .input-text:focus, .woocommerce-page #content table.cart td.actions .coupon .input-text:focus
{
	outline: 0;
}

.contact_form_wrapper input[type=text], .contact_form_wrapper input[type=email], .contact_form_wrapper input[type=date]
{
	width: 46%;
	display: inline;
	box-sizing: border-box;
	margin-right: 3%;
	margin-bottom: 3%;
}

.contact_form_wrapper label
{
	display: none;
}

.contact_form_response ul
{
	margin-left: 0 !important;
	margin-bottom: 20px;
	list-style: none;
	font-size: 12px;
}

.contact_form_response ul li.error, .password_container .error
{
	padding: 10px 20px 10px 40px;
	position: relative;
	color: #b13c3c;
    background: #ffe9e9;
    margin-bottom: 10px;
    display: inline-block;
    font-size: 12px;
}

.password_container .error
{
	margin-bottom: 20px;
}

.contact_form_response ul li.error:before, .password_container .error:before
{
	font-size: 12px;
	position: absolute;
	font-family: 'Font Awesome 5 Free';
	content: "\f06a";
	left: 20px;
	font-weight: 900;
}

.contact_form_response ul:empty
{
	margin: 0;
}

.contact_form_wrapper
{
	margin-top: 30px;
}

.page_content_wrapper .contact_form_wrapper
{
	margin-top: 0;
}

#contact_form input[type="text"], #contact_form textarea
{
	margin-bottom: 15px;
	width: 97%;
}

div.wpcf7-response-output
{
	padding: 20px;
}

.wpcf7-form input.wpcf7-text,
.wpcf7-form textarea,
.wpcf7-form input.wpcf7-date,
.wpcf7-form input.wpcf7-dynamictext,
.wpcf7-form select
{
	width: 100%;
	box-sizing: border-box;
}

.wpcf7-form textarea
{
	height: 120px;
}

.fullwidth_comment_wrapper #commentform textarea
{
	width: 100%;
}

.fullwidth_comment_wrapper .post_content_wrapper
{
	background: transparent !important;
	padding: 30px 0 30px 0 !important;
	width: 100% !important;
}

.input_wrapper
{
	position: relative;
}

form.post-password-form label
{
	width: auto;
	float: left;
	margin-right: 5px;
}

form.post-password-form input[type=password]
{
	margin-left: 5px;
}

form.post-password-form input[type=submit]
{
	padding: .5em 1.5em .45em 1.5em;
}

.form-allowed-tags
{
	display: none !important;
}

p.comment-notes
{
	margin-bottom: 40px;
}

textarea
{
	font-size: 100%;
	border: 1px solid #dce0e0;
	margin: 0;
	overflow: auto;
	padding: 6px 10px 6px 10px;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
	outline: none;
	-webkit-transition: border-color linear .2s;
	-moz-transition: border-color linear .2s;
	-o-transition: border-color linear .2s;
	transition: border-color linear .2s;
	box-sizing: border-box;
	-webkit-appearance: none;
}

input[type=submit], input[type=button], a.button, .button, body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"], body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"]
{
	display: inline-block;
	outline: none;
	cursor: pointer;
	text-align: center;
	text-decoration: none;
	padding: 10px 30px 10px 30px;
	color: #fff;
	background: #222;
	border: 2px solid #222;
	font-size: 15px;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
	text-shadow: none;
	-webkit-appearance: none;
	box-shadow: 0 0 0 0;
	font-style: normal;
	font-weight: normal;
	text-transform: none;

	-webkit-transition: color .2s linear, background .3s linear, opacity .2s linear;
	-moz-transition: color .2s linear, background .3s linear, opacity .2s linear;
	-ms-transition: color .2s linear, background .3s linear, opacity .2s linear;
	-o-transition: color .2s linear, background .3s linear, opacity .2s linear;
	transition: color .2s linear, background .3s linear, opacity .2s linear;
}

.comment-form .form-submit input[type=submit]
{
	padding: .7em 2.5em .7em 2.5em;
}

a.comment-reply-link
{
	margin: 0;
	float:right;
	font-size: 13px;
	text-align: center;
	border-radius: 25px;
	margin-top: -40px;
	display: block;
	font-weight: 600;
}

.comment:hover a.comment-reply-link
{
	display: block;
}

input[type=submit].medium, input[type=button].medium, a.button.medium
{
	font-size: 16px;
}

input[type=submit].large, input[type=button].large, a.button.large
{
	font-size: 18px;
}

#cancel-comment-reply-link
{
	margin: 0 20px 0 10px;
	font-size: 13px;
}

/*------------------------------------------------------------------
[8. Pagination]
*/

.pagination {
	margin: 40px 0 60px 0;
	float: left;
    clear: both;
}

.pagination p a:first-child {
	float: left;
}

.pagination p a:last-child {
	float: right;
}

.pagination a, .pagination span
{
	height: 30px;
	width: 30px;
	line-height: 30px;
	display: inline-block;
	text-align: center;
	color: #777;
	background: #f9f9f9;
	margin-right: 5px;
	overflow: hidden;
	font-size: 12px;
}

body.single-post .fullwidth_comment_wrapper .pagination a,
body.single-post .fullwidth_comment_wrapper .pagination a:hover,
body.page .fullwidth_comment_wrapper .pagination a,
body.page .fullwidth_comment_wrapper .pagination a:hover
{
	width: auto;
	background: transparent !important;
	color:#222;
	font-weight: bold;
	font-size: 16px;
}

body.single-post .fullwidth_comment_wrapper .pagination,
body.single-post .fullwidth_comment_wrapper .pagination p,
body.page .fullwidth_comment_wrapper .pagination,
body.page .fullwidth_comment_wrapper .pagination p
{
	display: block;
	width: 100%;
}

body.single-post .fullwidth_comment_wrapper .pagination p a:first-child,
body.page .fullwidth_comment_wrapper .pagination p a:first-child
{
	float: left;
}

body.single-post .fullwidth_comment_wrapper .pagination p a:first-child:before,
body.page .fullwidth_comment_wrapper .pagination p a:first-child:before
{
	font-family: 'Font Awesome 5 Free';
	content: "\f100";
	margin-right: 5px;
}

body.single-post .fullwidth_comment_wrapper .pagination p a:nth-child(2),
body.page .fullwidth_comment_wrapper .pagination p a:nth-child(2)
{
	float: right;
}

body.single-post .fullwidth_comment_wrapper .pagination p a:nth-child(2):after,
body.page .fullwidth_comment_wrapper .pagination p a:nth-child(2):after
{
	font-family: 'Font Awesome 5 Free';
	content: "\f101";
	margin-left: 5px;
}

body.page .fullwidth_comment_wrapper #respond,
body.comment_close #page_content_wrapper
{
	margin-bottom: 60px;
}

.pagination span, .pagination a:hover
{
	font-weight: bold;
	background: #888;
}

.pagination_detail
{
	margin-top: 40px;
	float: right;
    font-size: 11px;
    text-transform: uppercase !important;
    letter-spacing: 2px;
}

#page_content_wrapper ul
{
	margin: 0;
}

#page_content_wrapper ol
{
	margin-left: 20px;
}

@-webkit-keyframes lazy_color_change {
	from { background-color: #cccccc; }
	to { background-color: #f0f0f0; }
}
@-moz-keyframes lazy_color_change {
	from { background-color: #cccccc; }
	to { background-color: #f0f0f0; }
}
@-o-keyframes lazy_color_change {
	from { background-color: #cccccc; }
	to { background-color: #f0f0f0; }
}
@keyframes lazy_color_change {
	from { background-color: #cccccc; }
	to { background-color: #f0f0f0; }
}

.post_img_hover.lazy
{
	-webkit-animation: lazy_color_change 1s infinite alternate;
	-moz-animation: lazy_color_change 1s infinite alternate;
	-ms-animation: lazy_color_change 1s infinite alternate;
	-o-animation: lazy_color_change 1s infinite alternate;
	animation: lazy_color_change 1s infinite alternate;
}

.post_img_hover
{
  position: relative;
  display: inline-block;
}

.post_img_hover:not(.lazy)
 {
  background: #222222;
  background: -moz-linear-gradient(90deg, #222222 0%, #444444 100%, #666666 100%);
  background: -webkit-linear-gradient(90deg, #222222 0%, #444444 100%, #666666 100%);
  background: linear-gradient(90deg, #222222 0%, #444444 100%, #666666 100%);
}

.post_img_hover.classic
{
	min-height: 350px;
}

.post_related .post_img_hover.classic
{
	min-height: 130px;
}

.post_img_hover img,
.post_img_hover:before,
.post_img_hover:after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.post_img_hover img {
  max-width: 100%;
  backface-visibility: hidden;
  vertical-align: top;
}

.post_img_hover:before,
.post_img_hover:after {
  content: '';
  background-color: #fff;
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  opacity: 0;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.post_img_hover:before {
  width: 60px;
  height: 1px;
  left: 100%;
}

.post_img_hover:after {
  height: 60px;
  width: 1px;
  top: 0%;
}

.post_img_hover a
{
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.post_img_hover:hover img,
.post_img_hover.hover img
{
  zoom: 1;
  filter: alpha(opacity=30);
  -webkit-opacity: 0.3;
  opacity: 0.3;
}

.post_img_hover:hover:before,
.post_img_hover.hover:before,
.post_img_hover:hover:after,
.post_img_hover.hover:after
{
  opacity: 1;
  top: 50%;
  left: 50%;
}

.post_img_hover .post_type_icon
{
	position: absolute;
	bottom: -30px;
	right: 30px;
	z-index: 2;
	display: inline-block;
	border-radius: 50px;
	line-height: 62px;
	width: 60px;
	height: 60px;
	background: #0067DA;
	text-align: center;
	box-shadow: 0 10px 40px rgba(0,0,0,0.15);

	-ms-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);

    transition: all .21s cubic-bezier(.5,.5,.4,.9);
}

.post_related h3
{
	text-align: center;
	margin-bottom: 20px;
}

.post_related .post_img_hover .post_type_icon
{
	width: 50px;
	height: 50px;
	right: 20px;
	bottom: -20px;
	line-height: 50px;
}

.post_img_hover:hover .post_type_icon
{
	-ms-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -o-transform: scale(1.2);
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
}

.post_img_hover .post_type_icon i
{
	color: #fff;
	font-size: 17px;
}

.post_related .post_img_hover .post_type_icon i
{
	font-size: 15px;
}

/*------------------------------------------------------------------
[9. Widgets]
*/

#page_content_wrapper ul.flickr
{
	margin: 0 0 10px 0;
}

#page_content_wrapper ul.posts.blog
{
	list-style: none;
	margin-left: 0 !important;
	float: left;
	padding: 0;
	width: 100%;
	box-sizing: border-box;
}

#page_content_wrapper ul.flickr li img
{
	width: 65px;
	height: auto;
}

#page_content_wrapper .sidebar .content .posts.blog li img,
.page_content_wrapper .sidebar .content .posts.blog li img,
#page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li img,
.page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li img,
#footer ul.sidebar_widget li ul.posts.blog li img
{
	float: left;
	margin: 0 10px 2px 0;
 	width: 80px;
}

#page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li,
.page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li
{
	clear: both;
	margin-bottom: 20px;
	float: left;
}

#page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li a,
.page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li a
{
	font-weight: 900;
	letter-spacing: -1px;
}

#page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li .post-excerpt,
.page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li .post-excerpt
{
	color: #999;
    letter-spacing: 0;
    font-size: 11px;
    font-weight: 600;
}

#page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li:last-child,
.page_content_wrapper .sidebar .content ul.sidebar_widget li.widget_post_views_counter_list_widget ul li:last-child
{
	margin-bottom: 0;
}

.post_circle_thumb
{
	position: relative;
	overflow: hidden;
	float: left;
	width: 95px;
	height: 95px;
	float: left;
	margin-right: 0;
}

.post_circle_thumb img
{
	width: 60px;
	height: auto;
}

.post_circle_thumb.flickr
{
	margin-right: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget, #page_content_wrapper .sidebar .content .posts.blog, .page_content_wrapper .sidebar .content .sidebar_widget, .page_content_wrapper .sidebar .content .posts.blog
{
	list-style: none;
	margin-left: 0;
	margin-top: 0;
	padding: 0;
}

#page_content_wrapper .sidebar .content .posts.blog li, .page_content_wrapper .sidebar .content .posts.blog li
{
	padding: 0 0 10px 0;
}

#page_content_wrapper .sidebar .content .posts.blog li:last-child, .page_content_wrapper .sidebar .content .posts.blog li:last-child
{
	padding-bottom: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget, .page_content_wrapper .sidebar .content .sidebar_widget li.widget
{
	margin: 40px 0 0px 0;
	padding: 0;
	float: left;
	clear: both;
	width: 100%;
	padding: 0;
	box-sizing: border-box;
	border: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget:first-child
{
	margin-top: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget #useronline-count,
.page_content_wrapper .sidebar .content .sidebar_widget li.widget #useronline-count
{
	margin-top: 10px;
}

#page_content_wrapper .sidebar .content .sidebar_widget li select, .textwidget select, .page_content_wrapper .sidebar .content .sidebar_widget li select
{
	margin: 10px 0 10px 0;
	max-width: 100%;
}

#page_content_wrapper .sidebar .content .sidebar_widget li h2.widgettitle, h2.widgettitle
{
	font-size: 11px;
	display: inline-block;
	text-align: left;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
	color: #222;
	font-weight: 400;
	letter-spacing: 2px;
	text-transform: uppercase;
	position: relative;
	border-bottom: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li h2.widgettitle:after, h2.widgettitle:after
{
	position: absolute;
	width: 55px;
	border-top: 1px solid #e7e7e7;
}

h2.widgettitle.photostream
{
	display: inline-block;
	margin-top: 30px;
	margin-bottom: 30px;
}

#page_content_wrapper.blog_wrapper .sidebar .content .sidebar_widget li:first-child
{
	margin-top: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul, .page_content_wrapper .sidebar .content .sidebar_widget li ul
{
	list-style: none;
	padding: 20px 0 0 0;
	margin: 0 0 15px 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul li ul.children, .page_content_wrapper .sidebar .content .sidebar_widget li ul li ul.children
{
	padding-top: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul li ul.children, .page_content_wrapper .sidebar .content .sidebar_widget li ul li ul.children
{
	margin-left: 10px;
	margin-bottom: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul li ul.children li:last-child, .page_content_wrapper .sidebar .content .sidebar_widget li ul li ul.children li:last-child
{
	border: 0;
	margin-bottom: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget_pages ul li ul.children, .page_content_wrapper .sidebar .content .sidebar_widget li.widget_pages ul li ul.children
{
	margin-left: 20px;
	padding-top: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul li, #footer .sidebar_widget li ul li, .page_content_wrapper .sidebar .content .sidebar_widget li ul li, #footer .sidebar_widget li ul li
{
	padding: 0;
	margin-top: 0;
	margin-bottom: 5px;
}

#footer .sidebar_widget li.widget_nav_menu ul li, #page_content_wrapper .sidebar .content .sidebar_widget li.widget_pages ul li
{
	border: 0;
}

#footer .sidebar_widget li ul li
{
	border-color: #000000;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul li:first-child, .page_content_wrapper .sidebar .content .sidebar_widget li ul li:first-child, #footer ul.sidebar_widget li ul li:first-child, #footer ul.sidebar_widget li .textwidget
{
	padding-top: 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr li:first-child, .page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr li:first-child, #footer ul.sidebar_widget li ul.flickr li:first-child
{
	border: 0;
	padding-top: 0;
}

#page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul.menu li a, .page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul.menu li a
{
	width: 100%;
	box-sizing: border-box;
	display: block;
}

#page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul li ul.sub-menu,
.page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul li ul.sub-menu
{
	padding: 0 0 0 10px;
}

#page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul li:last-child,
.page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul li:last-child,
#page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul,
.page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul
{
	margin-bottom: 0;
}

#wp-calendar tr td, #wp-calendar tr th
{
	padding: 5px 0 5px 0;
	text-align: center;
	background: transparent;
}

#wp-calendar tr td#prev
{
	text-align: left;
}

#wp-calendar tr td#next
{
	text-align: right;
}

#wp-calendar tr td.pad, #wp-calendar tr td#prev, #wp-calendar tr td#next
{
	background: transparent !important;
	font-weight: 900;
	padding: 12px 0 10px 0;
}

#wp-calendar caption
{
	display:none;
}

#wp-calendar
{
	margin: 20px 0 0 0;
	width: 100%;
}

.mobile_menu_wrapper .widget_calendar table tr th, .mobile_menu_wrapper .widget_calendar table tr td
{
	border-color: #555;
}

table
{
	margin: 10px 0 30px 0;
}

table tr td
{
	border-bottom: 1px solid #dce0e0;
}

table tr th
{
	font-weight: bold;
}

table tr th, table tr td
{
	padding: 20px;
	text-align: left;
	border-bottom: 1px solid #dce0e0;
}

#footer table tr th, #footer table tr td
{
	border-bottom: 1px solid #000000 !important;
}

#footer .widget_tag_cloud div a
{
	border-color: #000000 !important;
}

#footer_menu
{
	float: right;
	display: block;
	list-style: none;
}

#footer_menu li
{
	float: left;
	margin-left: 20px;
}

#footer_menu li ul
{
	display: none;
}

table thead tr th
{
	background: transparent;
}

table#wp-calendar thead tr th
{
	text-align: center;
}

table tfoot tr, table tfoot tr td
{
	background: transparent;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr, .page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr
{
	list-style: none;
	margin: 5px 0 30px 0;
	float: left;
	display: block;
	padding: 15px 0 3px 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr li, .page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr li
{
	display: block;
	float: left;
	margin: 0 10px 10px 0;
	padding: 0;
	border: 0;
	clear: none;
	width: calc(33% - 10px);
	padding-right: 10px;
	box-sizing: border-box;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr li img, .page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr li img
{
	width: 100%;
	height: auto;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.twitter, .page_content_wrapper .sidebar .content .sidebar_widget li ul.twitter
{
	margin: 0;
	margin-top: 20px;
	list-style:none;
	padding: 0 0 0 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.twitter li, .page_content_wrapper .sidebar .content .sidebar_widget li ul.twitter li
{
	padding-left:32px;
	padding-bottom: 15px;
	border: 0;
	position: relative;
	box-sizing: border-box;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.twitter li:before, .page_content_wrapper .sidebar .content .sidebar_widget li ul.twitter li:before
{
	position: absolute;
	left: 0;
	top: -5px;
	font-family: 'Font Awesome 5 Free';
	content: "\f099";
	font-size: 22px;
	font-weight: 900;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.social_media, .page_content_wrapper .sidebar .content .sidebar_widget li ul.social_media
{
	list-style: none;
	margin: 10px 0 15px 0;
	float: left;
	display: block;
	padding: 0 0 3px 0;
	margin-left: -5px;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.social_media li, .page_content_wrapper .sidebar .content .sidebar_widget li ul.social_media li
{
	display: block;
	float: left;
	margin: 0 5px 0 0;
}

#page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr, ul.flickr, .page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr, ul.flickr
{
	list-style: none;
	margin: 10px 0 -10px 0;
	float: left;
	display: block;
	clear: both;
	width: 104%;
}

ul.flickr li
{
	float: left;
	margin: 0 5px 0 0;
}

#page_content_wrapper .sidebar .content .posts.blog, .page_content_wrapper .sidebar .content .posts.blog
{
	list-style: none;
	margin-top: 30px;
}

#page_content_wrapper .sidebar .content .posts.blog.withthumb, .page_content_wrapper .sidebar .content .posts.blog.withthumb
{
	margin-top: 30px;
	margin-bottom: 0;
}

#footer ul.sidebar_widget li ul.posts.blog.withthumb
{
	margin-top: 20px;
}

#page_content_wrapper .sidebar .content .posts.blog li, .page_content_wrapper .sidebar .content .posts.blog li
{
	line-height: 1em;
	clear: both;
	border:0;
	background: none;
	width: 100%;
	float: left;
	margin: 0;
}

#page_content_wrapper .posts.blog li, .page_content_wrapper .posts.blog li
{
	float: left;
	width: 49.4%;
	margin-right: 1.2%;
	margin-bottom: 1.2%;
	padding: 20px;
	box-sizing: border-box;
	border: 1px solid #dce0e0;
	display: table;
	text-align: left;

	-webkit-transition: background .2s linear;
	-moz-transition: background .2s linear;
	-ms-transition: background .2s linear;
	-o-transition: background .2s linear;
	transition: background .2s linear;
}

#page_content_wrapper .posts.blog li:nth-child(2n), .page_content_wrapper .posts.blog li:nth-child(2n)
{
	margin-right: 0;
	float: right;
}

#page_content_wrapper .posts.blog li a,
.page_content_wrapper .posts.blog li a,
#footer ul.sidebar_widget li ul.posts.blog li a
{
	width: 100%;
	line-height: 1.2em;
	font-size: 16px;
}

#page_content_wrapper .sidebar .content .posts.blog.withthumb li, #page_content_wrapper .posts.blog.withthumb li, .page_content_wrapper .sidebar .content .posts.blog.withthumb li, .page_content_wrapper .posts.blog.withthumb li
{
	line-height: 1.5em;
	border: 0;
	display: block;
}

#page_content_wrapper .sidebar .content .posts.blog li, .page_content_wrapper .sidebar .content .posts.blog li
{
	padding-top: 10px;
}

#page_content_wrapper .sidebar .content .posts.blog li strong.header, #page_content_wrapper .posts.blog li strong.header, .page_content_wrapper .sidebar .content .posts.blog li strong.header, .page_content_wrapper .posts.blog li strong.header
{
	font-weight: normal;
}

#page_content_wrapper .sidebar .content .textwidget, .page_content_wrapper .sidebar .content .textwidget
{
	margin-top: 0;
	padding: 10px 0 0 0;
	text-align: left;
}

.textwidget img
{
	max-width: 95%;
	height: auto;
}

#searchsubmit
{
	margin: -7px;
	line-height: 19px;
	-webkit-border-top-left-radius: 0px !important;
	-webkit-border-bottom-left-radius: 0px !important;
	-moz-border-radius-topleft: 0px !important;
	-moz-border-radius-bottomleft: 0px !important;
	border-top-left-radius: 0px !important;
	border-bottom-left-radius: 0px !important;
	padding: .6em 1.5em 0.5em 1.5em !important;
}

.widget_search input#searchsubmit
{
	margin: 10px 0 0 -10px;
	top: 0;
    position: relative;
    padding: 14px 20px 12px 20px !important;
}

.widget_tag_cloud div, .tag_cloud
{
	margin: 20px 0 0 0;
}

.widget_tag_cloud div a, .meta-tags a, .tag_cloud a
{
	display: inline-block;
	-webkit-transition: background .2s linear, border .1s linear;
	-moz-transition: background .2s linear, border .1s linear;
	-ms-transition: background .2s linear, border .1s linear;
	-o-transition: background .2s linear, border .1s linear;
	transition: background .2s linear, border .1s linear;

    color: #444;
    display: inline-block;
    border: 1px solid #e0e0e0;
    padding: 3px 15px 3px 15px;
    margin-right: 8px;
    margin-bottom: 12px;
}

.widget_tag_cloud div a:hover
{
	background: #EFA697;
    border-color: #EFA697;
    color: #fff !important;
}

.widget_mc4wp_widget input
{
	text-align: center;
}

.widget_mc4wp_widget input[type=submit]
{
	width: 100%;
	margin-top: 10px;
}

.portfolio_recent_link h6, .blog_recent_link h6
{
	display: inline;
	font-size: 11px;
}

.widget_mc4wp_form_widget input[type=email]
{
	width: 100%;
}

.widget_mc4wp_form_widget input[type=submit]
{
	margin-top: 20px;
}

#page_content_wrapper .widget_mc4wp_form_widget p
{
	padding-bottom: 0.7em;
}

#page_content_wrapper.blog_wrapper
{
	box-sizing: border-box;
	position: relative;
	z-index: 2;
	background: #fff;
	margin-bottom: 100px;
}

body.single-post #page_content_wrapper.blog_wrapper
{
	margin-top: -100px;
	padding: 60px;
	margin-bottom: 80px;
}

body.single-post #page_caption:not(.hasbg)
{
	padding-bottom: 100px;
}

#page_content_wrapper.blog_wrapper .pagination
{
	margin-bottom: 20px;
}

form.protected-post-form
{
	text-align: center;
}

form.protected-post-form p
{
	padding: 0 !important;
}

form.protected-post-form .protected-post-text
{
	margin-bottom: 20px;
}

form.protected-post-form input[type=submit].button
{
	padding: .4em 2.5em .3em 2.5em;
	margin-left: 10px;
}

/*------------------------------------------------------------------
[10. Contact Form Captcha]
*/

div.wpcf7-validation-errors,
div.wpcf7-mail-sent-ok
{
	border: 0;
	background: #FFCC00;
	font-size: 13px;
}

div.wpcf7-mail-sent-ok
{
	background: #4CD964;
	color: #fff;
}

/*------------------------------------------------------------------
[12. Woocommerce Elements]
*/

h1.page-title
{
	display: none;
}

.woocommerce .woocommerce-ordering,
.woocommerce .woocommerce-result-count, .woocommerce-page .woocommerce-result-count
{
	margin-bottom: 60px;
}

.woocommerce .woocommerce-ordering, .woocommerce-page form.woocommerce-ordering
{
	border: 1px solid #e7e7e7;
	overflow: hidden;
	padding: 0px 40px 0px 10px;
	position: relative;
}

.woocommerce-page form.woocommerce-ordering:after
{
	font-family: 'themify';
	content: "\e64b";
	display: inline;
	position: absolute;
	top: 10px;
	right: 20px;
}

.woocommerce .woocommerce-ordering, .woocommerce-page form.woocommerce-ordering select
{
	border: 0;
	-webkit-appearance: none;
	background: transparent !important;
}

.woocommerce ul.products li.product .ajax_add_to_cart, .woocommerce-page ul.products li.product .ajax_add_to_cart
{
	display: none !important;
}

.woocommerce ul.products li.product, .woocommerce-page ul.products li.product, .woocommerce ul.products.columns-3 li.product, .woocommerce-page ul.products.columns-3 li.product
{
	width: 30%;
	margin-right: 5%;
	margin-bottom: 5%;
	padding: 0 0 10px 0;
	box-sizing: border-box;
    clear: none;
    border: 0;
    text-align: left;
    background: #fff;
    border-radius: 5px;
}

.woocommerce ul.products li.product:nth-child(3n+1),
.woocommerce-page ul.products li.product:nth-child(3n+1),
.woocommerce ul.products.columns-3 li.product:nth-child(3n+1),
.woocommerce-page ul.products.columns-3 li.product:nth-child(3n+1)
{
	clear: both;
}

.woocommerce ul.products li.last, .woocommerce-page ul.products li.last
{
	margin-right: 0;
}

.woocommerce ul.products li.product .price
{
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
	color: #444;
	font-size: 1.1em;
	font-weight: 900;
}

.woocommerce ul.products li.product .button
{
	background: transparent;
	padding: 0;
	text-transform: none;
	font-size: 12px;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
	letter-spacing: 0;
	font-weight: 400;
}

.woocommerce div.product form.cart .variations
{
	margin: 0;
}

.woocommerce #content .quantity input.qty, .woocommerce .quantity input.qty, .woocommerce-page #content .quantity input.qty, .woocommerce-page .quantity input.qty
{
	outline: 0;
	border: 1px solid #dce0e0 !important;
	height: 35px;
	width: 80px;
	border-radius: 5px;
}

.woocommerce-page div.product form.cart .button
{
	width: auto !important;
	margin-left: 5px;
	padding: .65em 1.5em .65em 1.5em !important;
	border: 0 !important;
	border-radius: 0;
}

.woocommerce ul.products li.product a img, .woocommerce-page ul.products li.product a img
{
	box-shadow: 0px 0px 0px black !important;
	width: 100% !important;
	text-align: center !important;
	margin: auto !important;
}

.woocommerce ul.products li.product h2.woocommerce-loop-product__title,
.woocommerce-page ul.products li.product h2.woocommerce-loop-product__title
{
	margin: 30px 30px 0 30px;
	font-size: 20px;
	padding: 0;
	box-sizing: border-box;
}

.woocommerce #page_content_wrapper .inner .sidebar_content.left_sidebar ul.products li.product h2.woocommerce-loop-product__title,
.woocommerce-page #page_content_wrapper .inner .sidebar_content.left_sidebar ul.products li.product h2.woocommerce-loop-product__title
{
	margin-top: 20px !important;
	font-size: 20px;
}

.woocommerce #page_content_wrapper .inner .sidebar_content.left_sidebar ul.products li.product .price
{
	font-size: 1em;
}

.woocommerce div.product div.images figure, .woocommerce div.product div.summary figure
{
	margin: 0;
}

.woocommerce div.product div.images .woocommerce-product-gallery__image:nth-child(n+2)
{
	padding: 3px;
	box-sizing: border-box;
	width: 16.66%;
	margin-top: 3px;
}

.woocommerce ul.products li.product .price del, .woocommerce-page ul.products li.product .price del
{
	font-size: 0.9em !important;
}

.woocommerce ul.cart_list li ins, .woocommerce ul.product_list_widget li ins, .woocommerce-page ul.cart_list li, .woocommerce-page ul.product_list_widget li ins
{
	text-decoration: none;
	font-weight: bold;
}

.woocommerce a.added_to_cart, .woocommerce-page a.added_to_cart
{
	display: none !important;
}

.woocommerce ul.cart_list li img, .woocommerce-page ul.cart_list li img, .woocommerce ul.product_list_widget li img, .woocommerce-page ul.product_list_widget li img
{
	box-shadow: 0px 0px 0px black !important;
	-webkit-box-shadow: 0px 0px 0px black !important;
	-moz-box-shadow: 0px 0px 0px black !important;
	width: 45px;
	height: auto;
}

.woocommerce div.product div.images img, .woocommerce-page div.product div.images img, .woocommerce #content div.product div.images img, .woocommerce-page #content div.product div.images img
{
	box-shadow: 0px 0px 0px black !important;
	-webkit-box-shadow: 0px 0px 0px black !important;
	-moz-box-shadow: 0px 0px 0px black !important;
}

.woocommerce div.product .woocommerce-tabs ul.tabs, .woocommerce-page div.product .woocommerce-tabs ul.tabs, .woocommerce #content div.product .woocommerce-tabs ul.tabs, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs
{
	margin-left: 0 !important;
}

.woocommerce #content_wrapper .inner .inner_wrapper .sidebar_content h1
{
	font-size: 30px !important;
}

.woocommerce #content_wrapper .inner .inner_wrapper .sidebar_content h2
{
	font-size: 18px !important;
}

.woocommerce #content_wrapper .inner .inner_wrapper .sidebar_content h3
{
	font-size: 16px !important;
	padding-bottom: 0;
}

.woocommerce #content_wrapper .inner .inner_wrapper .sidebar_content h4
{
	font-size: 14px !important;
}

.woocommerce #content_wrapper .inner .inner_wrapper .sidebar_content h5
{
	font-size: 13px !important;
}

.woocommerce #content_wrapper .inner .inner_wrapper .sidebar_content h6
{
	font-size: 12px !important;
}

.woocommerce div.product div.images div.thumbnails a, .woocommerce-page div.product div.images div.thumbnails a, .woocommerce #content div.product div.images div.thumbnails a, .woocommerce-page #content div.product div.images div.thumbnails a
{
	margin-bottom: 3.8%;
}

.entry-summary div[itemprop="description"]
{
	margin: 10px 0 0 0;
	padding-left: 0 !important;
	font-size: 18px;
}

.review-summary
{
	display: none !important;
}

.woocommerce .widget_shopping_cart .total, .woocommerce-page .widget_shopping_cart .total
{
	font-size: 14px !important;
	color: #222;
	padding-top: 10px !important;
}

.woocommerce .widget_shopping_cart .total, .woocommerce-page .widget_shopping_cart .total
{
	border: 0;
}

.woocommerce .widget_price_filter .ui-slider .ui-slider-range, .woocommerce-page .widget_price_filter .ui-slider .ui-slider-range
{
	background: #444 !important;
	box-shadow: inset 0 0 0 0 rgba(0,0,0,0.5) !important;
	-webkit-box-shadow: inset 0 0 0 0 rgba(204, 204, 204, 0.5) !important;
	-moz-box-shadow: inset 0 0 0 0 rgba(0,0,0,0.5) !important;
}

.woocommerce .widget_price_filter .ui-slider .ui-slider-handle, .woocommerce-page .widget_price_filter .ui-slider .ui-slider-handle
{
	border: 1px solid #444 !important;
	background: #fff !important;
	box-shadow: inset 0 0 0 4px #fff;
}

.woocommerce #page_content_wrapper a.button, .woocommerce.columns-4 ul.products li.product a.add_to_cart_button, .woocommerce.columns-4 ul.products li.product a.add_to_cart_button:hover
{
	border: 0 !important;
	padding: 0 !important;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif !important;
	position: relative !important;
	background: transparent !important;
	margin-top: 0;
	display: block;
	margin: auto;
	top: 3px;
}

.woocommerce #page_content_wrapper a.button:hover, .woocommerce-page #page_content_wrapper a.button:hover, .woocommerce.columns-4 ul.products li.product a.add_to_cart_button:hover, .woocommerce.columns-4 ul.products li.product a.add_to_cart_button:hover
{
	opacity: 1;
}

.woocommerce.columns-4 ul.products li.product a.add_to_cart_button
{
	color: #222 !important;
	font-size: 100% !important;
}

.woocommerce #page_content_wrapper a.button.product_type_variable:before, .woocommerce-page #page_content_wrapper a.button.product_type_variable:before
{
	content: '\f03a';
}

.woocommerce div.product form.cart .button, .woocommerce-page div.product form.cart .button, .woocommerce #content div.product form.cart .button, .woocommerce-page #content div.product form.cart .button
{
	opacity: 1 !important;
}

.woocommerce ul.cart_list li a, .woocommerce ul.product_list_widget li a, .woocommerce-page ul.cart_list li a, .woocommerce-page ul.product_list_widget li a
{
	font-weight: normal !important;
}

.woocommerce ul.cart_list li img, .woocommerce ul.product_list_widget li img, .woocommerce-page ul.cart_list li img, .woocommerce-page ul.product_list_widget li img
{
	width: 70px !important;
	float: left !important;
	margin-right: 15px !important;
	margin-left: 0 !important;
}

.woocommerce #reviews #comments ol.commentlist, .woocommerce-page #reviews #comments ol.commentlist
{
	margin-top: 10px;
}

.cart-collaterals .cart_totals table {
	width: 100% !important;
}

.woocommerce .related ul.products li.product, .woocommerce .related ul li.product, .woocommerce .upsells.products ul.products li.product, .woocommerce .upsells.products ul li.product, .woocommerce-page .related ul.products li.product, .woocommerce-page .related ul li.product, .woocommerce-page .upsells.products ul.products li.product, .woocommerce-page .upsells.products ul li.product {
	border: 0;

	-webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    -o-transition-duration: 0.2s;
    transition-duration: 0.2s;
}

.woocommerce table.cart a.remove, .woocommerce #content table.cart a.remove, .woocommerce-page table.cart a.remove, .woocommerce-page #content table.cart a.remove
{
	color: #999 !important;
}

.woocommerce table.cart a.remove:hover, .woocommerce #content table.cart a.remove:hover, .woocommerce-page table.cart a.remove:hover, .woocommerce-page #content table.cart a.remove:hover
{
	color: #fff !important;
}

body[data-shop=three_cols].woocommerce ul.products li.product, body[data-shop=three_cols].woocommerce-page ul.products li.product
{
	width: 31.2% !important;
}

.woocommerce ul.products li.last, .woocommerce-page ul.products li.last
{
	margin-right: 0 !important;
}

p.price
{
	padding-top: 0 !important;
}

p.price ins span.amount, p.price span.amount, .woocommerce #content div.product p.price, .woocommerce #content div.product span.price, .woocommerce div.product p.price, .woocommerce div.product span.price, .woocommerce-page #content div.product p.price, .woocommerce-page #content div.product span.price, .woocommerce-page div.product p.price, .woocommerce-page div.product span.price
{
	font-weight: 900;
}

.woocommerce #page_content_wrapper div.product p.price, .woocommerce-page #page_content_wrapper div.product p.price
{
	padding-bottom: 0;
}

p.price del span.amount
{
	color: #ccc !important;
	font-size: 16px;
}

span.price del span.amount
{
	font-size: 13px;
}

#tab-description h2, .woocommerce #reviews #comments h2, .woocommerce-page #reviews #comments h2
{
	display: none;
}

body.learnpress-page.checkout h3
{
	font-size: 18px;
	margin: 15px 0 15px 0;
}

body.learnpress-page.checkout .learnpress a:last-child {
	display: block;
	clear: both;
}

.related.products h2, .cart_totals h2, .shipping_calcuLator h2, .upsells.products h2, .cross-sells h2
{
	margin-bottom: 40px;
	margin-top: 10px;
	font-size: 24px;
}

.shipping_calcuLator h2
{
	text-align: left;
}

.related.products
{
	padding-top: 80px;
    clear: both;
    float: left;
    width: 100%;
    margin-top: 10px;
}

.woocommerce div.product .woocommerce-tabs ul.tabs:before, .woocommerce-page div.product .woocommerce-tabs ul.tabs:before, .woocommerce #content div.product .woocommerce-tabs ul.tabs:before, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs:before
{
	border: 0;
	box-shadow: 0 0 0;
	border-radius: 0px !important;
}

.woocommerce #content div.product .woocommerce-tabs ul.tabs li.active:after, .woocommerce div.product .woocommerce-tabs ul.tabs li.active:after, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li.active:after, .woocommerce-page div.product .woocommerce-tabs ul.tabs li.active:after, .woocommerce #content div.product .woocommerce-tabs ul.tabs li:after, .woocommerce div.product .woocommerce-tabs ul.tabs li:after, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li:after, .woocommerce-page div.product .woocommerce-tabs ul.tabs li:after, .woocommerce #content div.product .woocommerce-tabs ul.tabs li.active:before, .woocommerce div.product .woocommerce-tabs ul.tabs li.active:before, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li.active:before, .woocommerce-page div.product .woocommerce-tabs ul.tabs li.active:before
{
	border: 0;
	box-shadow: 0 0 0;
	border-radius: 0px !important;
}

.woocommerce div.product .woocommerce-tabs .panel, .woocommerce-page div.product .woocommerce-tabs .panel
{
	border: 0;
	padding: 0;
	box-sizing: border-box;
}

.woocommerce #reviews #comments ol.commentlist li, .woocommerce-page #reviews #comments ol.commentlist li
{
	width: 100%;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li, .woocommerce-page div.product .woocommerce-tabs ul.tabs li
{
	background: transparent;
	box-shadow: none !important;
	visibility: visible !important;
	border: 0;
	padding: 0;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li:first-child, .woocommerce-page div.product .woocommerce-tabs ul.tabs li:first-child
{
	margin-left: 0 !important;
}

.woocommerce div.product .woocommerce-tabs .panel
{
	padding-top: 40px;
}

.woocommerce #page_content_wrapper div.product .woocommerce-tabs .panel p
{
	padding: 0;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li a, .woocommerce-page div.product .woocommerce-tabs ul.tabs li a
{
	padding: 5px 30px 5px 30px !important;
	font-weight: normal !important;
	text-shadow: none !important;
	font-weight: 700;
}

.woocommerce-page div.product .woocommerce-tabs
{
	padding-top: 40px;
    margin-bottom: 0;
    border-top: 1px solid #e1e1e1;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li.active, .woocommerce-page div.product .woocommerce-tabs ul.tabs li.active, .woocommerce #content div.product .woocommerce-tabs ul.tabs li.active, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li.active
{
	background: #000000;
    color: #ffffff;
    border-radius: 25px;
	height: auto !important;
	padding: 0 !important;
	border: 0;
	margin-left: 0 !important;
	margin-right: 0 !important;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li.active a, .woocommerce-page div.product .woocommerce-tabs ul.tabs li.active a, .woocommerce #content div.product .woocommerce-tabs ul.tabs li.active a, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li.active a
{
	color: #fff;
	font-weight: bold !important;
	border-radius: 0px !important;
}

.woocommerce div.product .product_title
{
	display: none;
}

.woocommerce div.product .woocommerce-tabs ul.tabs, .woocommerce-page div.product .woocommerce-tabs ul.tabs
{
	padding-left: 0 !important;
}

.widget_shopping_cart_content a.button
{
	margin-right: 5px !important;
}

.woocommerce #page_content_wrapper div.product p.price, .woocommerce-page #page_content_wrapper div.product p.price
{
	font-size: 22px !important;
	color: #222;
	padding: 0 !important;
}

.woocommerce-page div.product p.price ins
{
	text-decoration: none !important;
}

.woocommerce-page.woocommerce #page_content_wrapper .product_type_variable.add_to_cart_button
{
	display: none;
}

.product_meta > span
{
	clear: both;
	display: block;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li:before, .woocommerce div.product .woocommerce-tabs ul.tabs li:after, .woocommerce #content div.product .woocommerce-tabs ul.tabs li:before, .woocommerce #content div.product .woocommerce-tabs ul.tabs li:after, .woocommerce-page div.product .woocommerce-tabs ul.tabs li:before, .woocommerce-page div.product .woocommerce-tabs ul.tabs li:after, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li:before, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li:after
{
	border: 0 !important;
	box-shadow: 0 0 0;
}

.woocommerce-message, .woocommerce-error, .woocommerce-info
{
	box-shadow: 0 0 0 #222 !important;
	border-radius: 5px;
}

.woocommerce-message
{
	background: #e9f3d2 !important;
	border-color: #e9f3d2 !important;
	color: #6a863b !important;
	text-shadow: none !important;
}

.woocommerce-info
{
	background: #d1edf5 !important;
	border-color: #d1edf5 !important;
	color: #4e7883 !important;
	text-shadow: none !important;
}

.woocommerce-error
{
	background: #ffe9e9 !important;
	border-color: #ffe9e9 !important;
	color: #b13c3c !important;
	text-shadow: none !important;
}

.woocommerce-message:before, .woocommerce-error:before, .woocommerce-info:before
{
	box-shadow: none !important;
	top: 0.8em !important;
}

.woocommerce .woocommerce-message a.button
{
	display: none;
}

.woocommerce .cart-collaterals, .woocommerce-page .cart-collaterals, #order_review
{
	margin-bottom: 0;
}

.woocommerce table.shop_table th, .woocommerce-page table.shop_table th
{
	text-align: left;
}

.woocommerce table.shop_table, .woocommerce-page table.shop_table
{
	border-bottom: 0;
}

.post_carousel.flexslider .slides > li
{
	margin-right: 20px;
}

.woocommerce-page .products .star-rating
{
	float: right;
	margin-top: 28px;
}

.woocommerce-page #page_content_wrapper .inner .sidebar_content .products .star-rating
{
	float: none;
	clear: both;
	margin-top: 10px;
}

.woocommerce ul.products li.product .price,
.woocommerce-page ul.products li.product .price,
.woocommerce #page_content_wrapper div.product p.price, .woocommerce-page #page_content_wrapper div.product p.price
{
	border: 0;
	display: block;
    float: none;
    clear: both;
    padding: 3px 25px 3px 25px;
    font-size: 13px;
    border-radius: 25px;
    margin: 10px 30px 20px 30px;
    display: inline-block;
}

.woocommerce #page_content_wrapper div.product p.price, .woocommerce-page #page_content_wrapper div.product p.price
{
	margin-top: 0;
}

.woocommerce #page_content_wrapper div.product p.price, .woocommerce-page #page_content_wrapper div.product p.price span
{
	position: relative;
	top: 2px;
	margin: 0;
}

.woocommerce #content nav.woocommerce-pagination, .woocommerce nav.woocommerce-pagination, .woocommerce-page #content nav.woocommerce-pagination, .woocommerce-page nav.woocommerce-pagination
{
	text-align: left;
	margin-top: 30px;
	margin-bottom: 30px;
	color: #FF4A52;
}

.woocommerce-notices-wrapper
{
	position: fixed;
    bottom: 0px;
    right: 90px;
    z-index: 9;
}

.woocommerce-notices-wrapper .woocommerce-message a.button
{
	margin-left: 10px !important;
	top: 8px !important;
	font-size: 13px;
}

.woocommerce #content nav.woocommerce-pagination ul, .woocommerce nav.woocommerce-pagination ul, .woocommerce-page #content nav.woocommerce-pagination ul, .woocommerce-page nav.woocommerce-pagination ul, .woocommerce #content nav.woocommerce-pagination ul li, .woocommerce nav.woocommerce-pagination ul li, .woocommerce-page #content nav.woocommerce-pagination ul li, .woocommerce-page nav.woocommerce-pagination ul li
{
	border: 0;
}

.woocommerce #content nav.woocommerce-pagination ul li, .woocommerce nav.woocommerce-pagination ul li, .woocommerce-page #content nav.woocommerce-pagination ul li, .woocommerce-page nav.woocommerce-pagination ul li
{
	height: 30px;
	width: 30px;
	line-height: 30px;
	display: inline-block;
	text-align: center;
	color: #888;
	background: #fff;
	border: 1px solid #dce0e0;
	margin-right: 10px;
	overflow: hidden;
}

.woocommerce-page nav.woocommerce-pagination ul li a
{
	color: #888;
	line-height: 1.3em;
}

.woocommerce-page nav.woocommerce-pagination ul li span.current, .woocommerce-page nav.woocommerce-pagination ul li a:hover, .woocommerce-page nav.woocommerce-pagination ul li a:active
{
	border-color: #dce0e0;
	background: #fcfcfc;
	color: #222 !important;
	font-weight: bold;
	line-height: 1.3em;
}

.woocommerce .woocommerce-pagination ul.page-numbers, .woocommerce-page .woocommerce-pagination ul.page-numbers
{
	margin-left: 0 !important;
}

.woocommerce-page nav.woocommerce-pagination ul li span.current, .woocommerce-page nav.woocommerce-pagination ul li a
{
	text-align: center;
}

.price_slider_wrapper
{
	margin-top: 30px;
}

.woocommerce-page ul.product_list_widget li .star-rating
{
	display: none;
}

.woocommerce-page ul.product_list_widget li a
{
	font-weight: 600 !important;
}

.woocommerce-page ul.product_list_widget li .amount
{
	font-weight: 500;
}

.woocommerce-page ul.product_list_widget li del .amount
{
	color: #ccc;
}

.woocommerce-page .price_label
{
	font-weight: 600;
}

.woocommerce-page ul.product_list_widget li ins
{
	background: transparent;
}

.woocommerce .widget_price_filter .price_slider_amount .button, .woocommerce-page .widget_price_filter .price_slider_amount .button
{
	float: left !important;
}

ul.product_list_widget li
{
	padding: 10px 0 10px 0 !important;
}

.woocommerce #content div.product, .woocommerce div.product, .woocommerce-page #content div.product, .woocommerce-page div.product
{
	clear: both;
}

body.single-product #page_content_wrapper
{
	margin-top: 40px;
}

.woocommerce .woocommerce-product-rating
{
	line-height: 1.7;
}

.woocommerce-review-link
{
	color: #222 !important;
}

.woocommerce-page div.product form.cart
{
	margin-top: 1em;
	margin-bottom: 1em;
}

.woocommerce div.product div.images, .woocommerce div.product div.summary
{
	margin-top: 20px;
	margin-bottom: 40px;
}

.woocommerce .woocommerce-error:after, .woocommerce .woocommerce-error:before, .woocommerce .woocommerce-info:after, .woocommerce .woocommerce-info:before, .woocommerce .woocommerce-message:after, .woocommerce .woocommerce-message:before, .woocommerce-page .woocommerce-error:after, .woocommerce-page .woocommerce-error:before, .woocommerce-page .woocommerce-info:after, .woocommerce-page .woocommerce-info:before, .woocommerce-page .woocommerce-message:after, .woocommerce-page .woocommerce-message:before
{
	display: block;
	clear: none;
}

.woocommerce #content table.cart img, .woocommerce table.cart img, .woocommerce-page #content table.cart img, .woocommerce-page table.cart img
{
	width: 70px;
}

.woocommerce table.shop_table th, .woocommerce-page table.shop_table th
{
	padding: 12px 15px 12px 15px;
}

.woocommerce .woocommerce-error:before, .woocommerce .woocommerce-info:before, .woocommerce .woocommerce-message:before, .woocommerce-page .woocommerce-error:before, .woocommerce-page .woocommerce-info:before, .woocommerce-page .woocommerce-message:before
{
	line-height: 1em;
	margin-top: 10px;
}

.woocommerce .cart-collaterals .shipping_calcuLator .shipping-calcuLator-button:after, .woocommerce-page .cart-collaterals .shipping_calcuLator .shipping-calcuLator-button:after, .wc-forward a:after, .wc-forward:after
{
	display: none;
}

#page_content_wrapper .inner .sidebar_content ul.products
{
	margin-left: 0 !important;
	padding: 0;
	box-sizing: border-box;
}

#page_content_wrapper .inner .sidebar_content.left_sidebar ul.products
{
	padding: 0;
}

#page_content_wrapper .inner .sidebar_content .related.products ul.products
{
	padding: 0;
	margin-top: 20px;
}

.woocommerce table.shop_table td, .woocommerce-page table.shop_table td
{
	border-top: 0;
	padding: 15px 15px;
}

.shipping-calcuLator-button
{
	color: #222;
}

.product-subtotal .amount
{
	color: #222;
	font-weight: 600;
}

.woocommerce .cart-collaterals .cross-sells, .woocommerce-page .cart-collaterals .cross-sells, .woocommerce .cart-collaterals .cart_totals, .woocommerce-page .cart-collaterals .cart_totals, .woocommerce .cart-collaterals .shipping_calcuLator, .woocommerce-page .cart-collaterals .shipping_calcuLator
{
	width: 100%;
	float: none;
}

.woocommerce .cart-collaterals .shipping_calcuLator, .woocommerce-page .cart-collaterals .shipping_calcuLator
{
	margin-top: 10px;
}

.woocommerce-page table.cart th, table.shop_table thead tr th
{
	border-bottom: 1px solid #dce0e0;
	border-top: 1px solid #dce0e0;
	color: #fff;
	font-weight: 500;
	background: #000;
	text-transform: uppercase;
	letter-spacing: 0;
}

.woocommerce table.shop_table, .woocommerce-page table.shop_table
{
	border-bottom: 1px solid #dce0e0;
}

.woocommerce table.shop_table.cart,
.woocommerce-page table.shop_table.cart
{
	border-top: 0;
	-webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
}

.woocommerce #content table.cart td.actions, .woocommerce table.cart td.actions, .woocommerce-page #content table.cart td.actions, .woocommerce-page table.cart td.actions
{
	border-bottom: 0 !important;
}

.woocommerce table.cart td.actions .coupon .input-text#coupon_code
{
	width: 200px;
	box-shadow: 0 0 0 0;
	margin-right: 10px;
	border-color: #dce0e0;
}

.woocommerce .cart-collaterals .cart_totals table tr:first-child td, .woocommerce .cart-collaterals .cart_totals table tr:first-child th, .woocommerce-page .cart-collaterals .cart_totals table tr:first-child td, .woocommerce-page .cart-collaterals .cart_totals table tr:first-child th, .woocommerce-page .cart-collaterals .cart_totals tr td, .woocommerce-page .cart-collaterals .cart_totals tr th
{
	border-bottom: 0;
}

.woocommerce-page #payment ul.payment_methods label
{
	display: inline-block;
	clear: none;
	width: auto;
	margin-bottom: 10px;
}

.woocommerce #payment div.payment_box, .woocommerce-page #payment div.payment_box
{
	margin-top: 0;
	margin-bottom: 20px;
}

.woocommerce #payment ul.payment_methods, .woocommerce-page #payment ul.payment_methods
{
	border-bottom: 0;
}

.woocommerce #payment div.form-row, .woocommerce-page #payment div.form-row
{
	border-top: 0;
}

.woocommerce .star-rating span:before, .woocommerce-page .star-rating span:before
{
	color: #444;
}

.woocommerce #reviews #comments ol.commentlist li .comment-text p.meta, .woocommerce-page #reviews #comments ol.commentlist li .comment-text p.meta
{
	font-size: 0.8em;
}

strong[itemprop="author"]
{
	font-size: 16px;
	font-weight: 600;
	color: #222;
	font-family: 'Jost', 'Helvetica Neue', Arial,Verdana,sans-serif;
	letter-spacing: -1px;
}

.woocommerce #payment div.payment_box:after, .woocommerce-page #payment div.payment_box:after
{
	display: none !important;
}

.woocommerce #reviews #comments ol.commentlist li .comment-text, .woocommerce-page #reviews #comments ol.commentlist li .comment-text
{
	border: 0 !important;
	padding: 0;
	margin: 0 0 0 80px;
}

.woocommerce #review_form #respond
{
	width: 100%;
}

.woocommerce #reviews h3
{
	text-align: left;
}

.woocommerce .product_meta
{
	font-size: 14px;
	font-weight: 900;
}

.woocommerce .widget_shopping_cart .cart_list li a.remove, .woocommerce.widget_shopping_cart .cart_list li a.remove
{
	display: none;
}

.woocommerce-cart .wc-proceed-to-checkout
{
	padding: 2em 0 4em 0;
}

.woocommerce-checkout #payment div.form-row
{
	padding: 1em 0 4em 0;
}

.return-to-shop
{
	padding: 0 0 5em 0 !important;
}

.woocommerce-checkout #payment
{
	background: transparent;
}

.woocommerce-checkout #payment ul
{
	margin-left: 0 !important;
	padding: 0 !important;
}

.woocommerce-checkout #payment .payment_method_paypal .about_paypal
{
	margin-left: 10px;
}

.woocommerce .woocommerce-error:before, .woocommerce .woocommerce-info:before, .woocommerce .woocommerce-message:before
{
	margin-top: 8px;
}

.woocommerce .top_bar.scroll .logo_wrapper img, .woocommerce-page .top_bar.scroll .logo_wrapper img
{
	max-width: 100%;
}

.woocommerce #respond input#submit.alt:hover, .woocommerce a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover
{
	background: #222;
}

.woocommerce .products .onsale, .woocommerce ul.products li.product .onsale, .woocommerce span.onsale
{
  	width: 45px;
    height: 45px;
    position: absolute;
    left: -10px;
    top: -10px;
    background: #b13c3c;
    border-radius: 250px;
    text-align: center;
    font-weight: 600;
    line-height: 47px;
    -webkit-box-shadow: 0 8px 8px -6px rgba(0,0,0,.15);
    -moz-box-shadow: 0 8px 8px -6px rgba(0,0,0,.15);
    box-shadow: 0 8px 8px -6px rgba(0,0,0,.15);
}

.woocommerce ul.products li.product .price ins
{
	text-decoration: none;
}

.woocommerce ul.products li.product .price del, .woocommerce div.product p.price del, .woocommerce div.product span.price del
{
	display: inline-block;
}

.woocommerce #respond input#submit, .woocommerce a.button, .woocommerce button.button, .woocommerce input.button
{
	font-weight: 500;
}

.woocommerce #respond input#submit, .woocommerce a.button, .woocommerce button.button, .woocommerce input.button, .woocommerce button.button:disabled, .woocommerce button.button:disabled[disabled]
{
	padding: .8em .9em;
}

.woocommerce #payment #place_order, .woocommerce-page #payment #place_order
{
	width: 100%;
	float: none;
	margin-top: 20px;
}

.woocommerce-privacy-policy-text p
{
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}

.woocommerce .term-description89
{
	display: none;
}

.woocommerce table.shop_table.woocommerce-checkout-review-order-table
{
	border-top: 0;
	-webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
}

#add_payment_method .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button
{
	font-size: 15px;
	margin-bottom: 0;
	display: inline-block;
	padding: .6em 1.5em .7em 1.5em;
	float: right;
}

#page_content_wrapper .sidebar .content .sidebar_widget li.widget_products ul li:last-child,
.page_content_wrapper .sidebar .content .sidebar_widget li.widget_products ul li:last-child,
#footer ul.sidebar_widget li.widget_products ul li:last-child
{
	margin-bottom: 0;
	padding-bottom: 0;
}

.sidebar_widget li.widget_products .product-title
{
	font-weight: 400;
	font-size: 16px;
}

#reviews.woocommerce-Reviews #comments
{
	width: 66.66%;
	float: left;
}

#reviews.woocommerce-Reviews #review_form_wrapper
{
	width: 33.33%;
	float: left;
}

.woocommerce #reviews #comments ol.commentlist li .comment-text p.description
{
	margin: 0;
	padding-top: 0;
	padding-bottom: 30px;
}

.woocommerce #reviews #comments ol.commentlist li .comment-text p.meta
{
	margin-bottom: 0;
	    padding-bottom: 0;
    padding-top: 0;
}

.woocommerce #reviews #comments ol.commentlist li img.avatar
{
	border-radius: 250px;
	border: 0;
	padding:0;
	background: transparent;
	width: 60px;
}

.woocommerce .star-rating
{
	font-size: 12px;
}

.woocommerce .star-rating span:before, .woocommerce-page .star-rating span:before
{
	color: #50E3C2;
}

.woocommerce #reviews #comments ol.commentlist li .comment_container
{
	border-bottom: 1px dashed #dce0e0;
	margin-bottom: 10px;
}

.woocommerce #reviews #comments ol.commentlist li:last-child .comment_container
{
	border: 0;
	margin-bottom: 0px;
}

.woocommerce #review_form #respond p
{
	padding-bottom: 0;
}

body.woocommerce-order-received .woocommerce h2,
body.woocommerce-order-received .woocommerce h3
{
	font-size: 18px;
}

body.woocommerce-order-received .woocommerce
{
	margin-bottom: 30px;
}

#page_content_wrapper .inner .sidebar_content ul.woocommerce-thankyou-order-details
{
	margin-left: 0;
}

.woocommerce-account #page_content_wrapper .inner .sidebar_content.full_width
{
	margin-bottom: 20px;
}

.woocommerce-MyAccount-navigation ul
{
	list-style: none;
	margin-left: 0 !important;
}

.woocommerce-MyAccount-navigation ul li
{
	margin-bottom: 10px;
	padding-left: 10px;
	border-left: 5px solid transparent;
}

.woocommerce-MyAccount-navigation ul li.is-active
{
	border-left: 5px solid #FF4A52;
	color: #FF4A52;
	font-weight: 600;
}

.woocommerce .woocommerce-Message a.button
{
	background: transparent;
	padding: 0;
	border-radius: 0;
}

.woocommerce #content div.product div.images, .woocommerce div.product div.images, .woocommerce-page #content div.product div.images, .woocommerce-page div.product div.images
{
	width: 40%;
}

.woocommerce-page div.product div.summary
{
	width: 55%;
}

.woocommerce #respond input#submit, .woocommerce a.button, .woocommerce button.button, .woocommerce input.button
{
	border-radius: 5px;
}

.woocommerce form .form-row textarea
{
	height: 10em;
}

body.woocommerce-checkout #page_content_wrapper .inner .sidebar_content
{
	padding-top: 20px;
	padding-bottom: 60px;
}

body.woocommerce-checkout .woocommerce .col2-set, body.woocommerce-checkout.woocommerce-page .col2-set
{
	float: left;
	width: 63%;
}

body.woocommerce-checkout .woocommerce .col2-set .col-1,
body.woocommerce-checkout.woocommerce-page .col2-set .col-1,
body.woocommerce-checkout .woocommerce .col2-set .col-2,
body.woocommerce-checkout.woocommerce-page .col2-set .col-2
{
	width: 100%;
}

body.woocommerce-checkout .woocommerce #order_review
{
	float: right;
	width: 32%;
}

body.page-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content dl.variation
{
	margin: 0 0 0 0;
	font-style: italic;
	font-size: smaller;
}

#add_payment_method #payment div.payment_box::before, .woocommerce-cart #payment div.payment_box::before, .woocommerce-checkout #payment div.payment_box::before
{
	display: none;
}

.woocommerce #payment div.payment_box, .woocommerce-page #payment div.payment_box
{
	background: #d1edf5 !important;
	border-radius: 5px;
}

/*
	13. Grid Rotator Plugin
*/

.ri-grid{
	margin: auto;
	position: relative;
	max-height: 0;
	-webkit-transition: all 1s ease-in-out;
	-moz-transition: all 1s ease-in-out;
	-o-transition: all 1s ease-in-out;
	-ms-transition: all 1s ease-in-out;
	transition: all 1s ease-in-out;
	overflow: hidden;
}

.ri-grid.visible
{
	max-height: 100%;
}

.ri-grid ul {
	list-style: none;
	display: block;
	width: 100%;
	margin: 0;
	padding: 0;
	overflow: hidden;
}

/* Clear floats by Nicolas Gallagher: http://nicolasgallagher.com/micro-clearfix-hack/ */

.ri-grid ul:before,
.ri-grid ul:after{
	content: '';
    display: table;
}

.ri-grid ul:after {
    clear: both;
}

.ri-grid ul {
    zoom: 1; /* For IE 6/7 (trigger hasLayout) */
}

.ri-grid ul li {
	-webkit-perspective: 400px;
	-moz-perspective: 400px;
	-o-perspective: 400px;
	-ms-perspective: 400px;
	perspective: 400px;
	margin: 0;
	padding: 0;
	float: left;
	position: relative;
	display: block;
	overflow: hidden;
	width: 0;
	height: 0;
}

.ri-grid ul li a{
	display: block;
	outline: none;
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-o-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transform-style: preserve-3d;
	-moz-transform-style: preserve-3d;
	-o-transform-style: preserve-3d;
	-ms-transform-style: preserve-3d;
	transform-style: preserve-3d;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	background-color: #333;
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}

/* Grid wrapper sizes */
.ri-grid-size-1{
	width: 55%;
}
.ri-grid-size-2{
	width: 100%;
}
.ri-grid-size-3{
	width: 100%;
	margin-top: 0px;
}

/* Shadow style */
.ri-shadow:after,
.ri-shadow:before{
	content: "";
	position: absolute;
	z-index: -2;
	bottom: 15px;
	left: 10px;
	width: 50%;
	height: 20%;
	max-width: 300px;
	max-height: 100px;
	box-shadow: 0 15px 10px rgba(0, 0, 0, 0.7);
	-webkit-transform: rotate(-3deg);
	-moz-transform: rotate(-3deg);
	-ms-transform: rotate(-3deg);
	-o-transform: rotate(-3deg);
	transform: rotate(-3deg);
}
.ri-shadow:after{
	right: 10px;
	left: auto;
	-webkit-transform: rotate(3deg);
	-moz-transform: rotate(3deg);
	-ms-transform: rotate(3deg);
	-o-transform: rotate(3deg);
	transform: rotate(3deg);
}

.ri-grid-loading:after,
.ri-grid-loading:before{
	display: none;
}

.ri-loading-image{
	display: none;
}

.ri-grid-loading .ri-loading-image{
	position: relative;
	left: 100%;
	margin: auto;
	margin-top: 50px;
	margin-bottom: 50px;
	display: block;
	text-align: center;
}

/*
	14. Login Plugin Elements
*/
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"]
{
	padding: 50px 40px 50px 40px;
	background: #f9f9f9;
	border-radius: 0;
}

body .ui-widget-overlay
{
	background: rgba(0,0,0,0.7) !important;
	opacity: 1;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] button.ui-dialog-titlebar-close,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] button.ui-dialog-titlebar-close
{
	display: none;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .ui-dialog-titlebar,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .ui-dialog-titlebar
{
	background: transparent;
	border: 0;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .ui-dialog-titlebar .ui-dialog-title,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .ui-dialog-titlebar .ui-dialog-title
{
	font-weight: normal;
	font-size: 28px;
	text-shadow: none;
	width: 100%;
	display: block;
	margin-bottom: 20px;
	text-align: center;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper label,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper label
{
	font-weight: normal;
	font-size: 16px;
	margin-bottom: 0;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="text"],
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="password"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="text"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="password"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="email"]
{
	font-size: 16px;
	letter-spacing: 0;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="text"]::-webkit-input-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="password"]::-webkit-input-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="text"]::-webkit-input-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="password"]::-webkit-input-placeholder
,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="email"]::-webkit-input-placeholder
{
	color: transparent;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="text"]::-moz-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="password"]::-moz-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="text"]::-moz-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="password"]::-moz-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="email"]::-moz-placeholder
{
	color: transparent;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="text"]:-ms-input-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="password"]:-ms-input-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="text"]:-ms-input-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="password"]:-ms-input-placeholder,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="email"]:-ms-input-placeholder
{
	color: transparent;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper .zm_alr_form_field_container,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper .zm_alr_form_field_container
{
	margin-bottom: 30px;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper .zm_alr_ul_container,
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper .zm_alr_ul_container
{
	font-size: 16px;
}

body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"]
{
	border-radius: 0;
	text-shadow: none;
}

.ui-dialog .zm_alr_login_dialog.ui-dialog-content
{
	border: 0;
}

/*
*
* 15. LearnPress Plugin Elements
*
*/

.learn-press-breadcrumb,
#learn-press-course .course-meta
{
	display: none;
}

#single_course_header
{
	padding: 60px 0 60px 0;
	width: 100%;
	float: left;
	box-sizing: border-box;
}

.single_course_title
{
	float: left;
	width: 70%;
}

.single_course_join
{
	float: right;
	width: 30%;
	text-align: right;
	margin-top: 20px;
}

.single_course_excerpt p
{
	padding: 0 !important;
	margin-top: 10px;
}

#single_course_bgimage
{
	height: 500px;
	background-size: cover;
	background-position: center center;
	width: 100vw;
	position: relative;
	left: 50%;
	right: 50%;
	margin-left: -50vw;
	margin-right: -50vw;
}

#learn-press-course-tabs
{
	margin: 40px 0 40px 0;
}

#page_content_wrapper ul.learn-press-nav-tabs
{
	border-bottom: 0;
	text-align: center;
}

body.learnpress-page.profile .lp-tab-sections
{
	margin: 0 0 20px 0 !important;
	background: transparent;
	z-index: 3;
    position: relative;
}

body.learnpress-page.profile .learn-press-subtab-content
{
	margin: 20px 0 0 0;
}

body.learnpress-page.profile .lp-sub-menu,
body.learnpress-page.profile ul.learn-press-courses
{
	margin: 20px 0 20px 0 !important;
}

body.learnpress-page.profile ul.learn-press-courses,
body.learnpress-page.profile .learn-press-message
{
	clear: both;
}

#page_content_wrapper ul.learn-press-nav-tabs .course-nav,
body.learnpress-page.profile .lp-tab-sections li
{
	display: inline-block;
	float: none;
}

body.learnpress-page.profile .lp-sub-menu li a,
body.learnpress-page.profile .lp-sub-menu li span
{
	font-size: 12px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 2px;
}

body.learnpress-page.profile .lp-tab-sections .section-tab.active span
{
	border: 0;
	line-height: initial;
}

body.learnpress-page.profile .profile-heading
{
	float: left;
	width: 50%;
	margin: 20px 0 20px 0;
}

body.learnpress-page.profile .lp-sub-menu
{
	float: right;
	width: 50%;
	text-align: right;
}

#page_content_wrapper .course-landing-summary
{
	margin-bottom: 80px;
}

#learn-press-course
{
	width: 70%;
	margin: auto;
}

#learn-press-course-tabs .course-tab-panel
{
	padding: 40px 0 0 0;
}

#page_content_wrapper ul.learn-press-nav-tabs .course-nav.active:after,
#page_content_wrapper ul.learn-press-nav-tabs .course-nav:hover:after
{
	display: none;
}

#page_content_wrapper ul.learn-press-nav-tabs .course-nav a,
body.learnpress-page.profile .lp-tab-sections li a,
body.learnpress-page.profile .lp-tab-sections li span,
body.learnpress-page.profile .lp-tab-sections .section-tab.active span
{
	font-weight: 700;
	border-radius: 50px;
	padding: 5px 30px 5px 30px;
}

#page_content_wrapper ul.learn-press-nav-tabs .course-nav.active a,
body.learnpress-page.profile .lp-tab-sections .section-tab.active span
{
	background: #333;
	color: #fff;
}

body.learnpress-page.profile .lp-list-table tr.list-table-nav td
{
	font-size: 10px;
	text-transform: uppercase;
	letter-spacing: 1px;
}

.lp-label.label-finished,
.lp-label.label-completed,
.lp-label.label-cancelled,
.lp-label.label-pending
{
	padding: 5px 30px 5px 30px;
	font-size: 10px;
	text-transform: uppercase;
	letter-spacing: 1px;
}

body .profile-recover-order
{
	border: 0;
	padding: 0;
	margin-top: 40px;
}

.order-recover input[name="order-key"]::placeholder
{
	opacity: 0.5;
}

body .profile-recover-order p
{
	padding: 0 !important;
}

body.learnpress-page.profile .lp-list-table tbody tr td.column-order-action
{
	font-size: 12px;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: 700;
}

body.learnpress-page.profile .lp-list-table tbody tr td.column-order-action a
{
	margin-right: 10px;
}

#profile-content-order-details h3
{
	font-size: 18px;
}

#page_content_wrapper .lp-single-course .course-price
{
	margin: 0;
	float: right;
	margin-bottom: 80px;
}

#page_content_wrapper .lp-single-course .lp-course-buttons
{
	float: left;
	margin: 0;
	margin-bottom: 80px;
}

.learnpress-page #page_content_wrapper .lp-button,
.learnpress-page #learn-press-profile-basic-information button,
.learnpress-page #profile-content-settings form button[type=submit]
{
	line-height: normal;
	height: intrinsic;
	padding: .6em 2.5em .6em 2.5em;
	background: transparent;

	border-radius: 0;
	-webkit-border-top-right-radius: 5px;
	-webkit-border-bottom-right-radius: 5px;
	-moz-border-radius-topright: 5px;
	-moz-border-radius-bottomright: 5px;
	border-top-right-radius: 5px;
	border-bottom-right-radius: 5px;
}

#page_content_wrapper .lp-single-course .course-author .author-bio
{
	font-style: normal;
	padding: 1.6em 0 1.6em 1.6em;
}

#page_content_wrapper .lp-single-course .course-author .author-name a
{
	font-size: 11px;
	text-transform: uppercase;
	font-weight: 900;
}

.course-author h3
{
	display: none;
}

#page_content_wrapper .course-curriculum ul.curriculum-sections .section-header
{
	border-bottom: 1px solid #999;
}

#page_content_wrapper .course-curriculum ul.curriculum-sections .section-header .section-title
{
	font-weight: 700;
}

body .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .item-meta,
body .lp-label.label-enrolled, .lp-label.label-started
{
	padding: 0 15px;
	font-size: 10px;
	text-transform: uppercase;
	letter-spacing: 1px;
}

body .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta:not(.trans) .course-item-status:before,
body .lp-label.label-enrolled, .lp-label.label-started,
body .course-remaining-time .lp-label.label-enrolled
{
	font-size: 10px;
}

body .lp-label.label-enrolled, .lp-label.label-started
{
	height: 20px;
    line-height: 20px;
    color: #fff;
}

body .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .duration
{
	background: transparent;
	color: #222;
	padding: 0;
}

body .course-curriculum ul.curriculum-sections .section-content .course-item.item-locked .course-item-meta .course-item-status,
body .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_lesson .section-item-link:before,
body .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_quiz .section-item-link:before,
body .course-curriculum ul.curriculum-sections .section-content .course-item:not(.item-preview) .course-item-status
{
	font-family: 'themify';
}

body .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.status-completed .course-item-status:before,
body .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.status-started .course-item-status:before
{
	font-size: 16px;
	content: "\e64c";
}

body .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_lesson .section-item-link:before
{
	font-size: 20px;
	content: "\e6c7";
}

body .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_quiz .section-item-link:before
{
	font-size: 20px;
	content: "\e72b";
}

body .course-curriculum ul.curriculum-sections .section-content .course-item.item-locked .course-item-meta .course-item-status:before
{
	color: #222;
	font-size: 16px;
	content: "\e62b";
}

#single_course_meta
{
	margin-top: -60px;
	position: relative;
	z-index: 2;
	border-radius: 5px;
}

#single_course_meta ul.single_course_meta_data
{
	display: flex;
	list-style: none;
	background: #fff;
	margin-bottom: 60px;
	-webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.05);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.05);
    border-radius: 5px;
}

#single_course_meta ul.single_course_meta_data li
{
	justify-content: center;
	padding: 0 1em;
    flex-grow: 1;
    display: flex;
    margin: 2.2rem 0;
}

#single_course_meta ul.single_course_meta_data li:first-child
{
	padding-left: 0;
}

#single_course_meta ul.single_course_meta_data li:last-child
{
	display: none !important;
}

#single_course_meta ul.single_course_meta_data li.single_course_meta_data_separator
{
	flex: 0;
	border-left: solid 1px #e1e1e1;
    display: flex;
    max-height: 2.7em;
    padding: 0;
}

#single_course_meta ul.single_course_meta_data li .single_course_meta_data_icon
{
	padding-right: .5em;
    display: flex;
    font-size: 2rem;
    line-height: 1.5;
}

#single_course_meta ul.single_course_meta_data li .single_course_meta_data_text
{
	display: flex;
    flex-direction: column;
}

#single_course_meta ul.single_course_meta_data li .single_course_meta_data_text .single_course_meta_data_title
{
	font-size: 14px;
	font-weight: 700;
	text-transform: uppercase;
	letter-spacing: 2px;
}

#single_course_meta ul.single_course_meta_data li .single_course_meta_data_text .single_course_meta_data_content
{
	font-size: 14px;
	font-weight: 700;
}

body .course-curriculum ul.curriculum-sections .section-content .course-item:before
{
	display: none;
}

body #course-item-content-header .form-button.lp-button-back button,
body.learnpress-page .lp-button
{
	height: auto;
	line-height: normal;
	padding: 6px 30px 4px 30px;
}

body.learnpress-page .lp-button.button-complete-lesson
{
	margin-top: 30px;
}

body #course-item-content-header .course-item-search input
{
	font-style: normal;
}

body #course-item-content-header
{
	background: #f9f9f9;
}

body #course-item-content-header .toggle-content-item:before
{
	font: 20px/60px 'themify';
    content: "\e67a";
}

body.single.full-screen-content-item #course-item-content-header .toggle-content-item:before
{
	font: 20px/60px 'themify';
    content: "\e679";
}

body.single.course-item-popup #learn-press-content-item .content-item-wrap
{
	margin: 20px auto;
}

body .course-item-nav .prev span,
body .course-item-nav .next span
{
	font-size: 13px;
	font-weight: 900;
	text-transform: uppercase;
	letter-spacing: 2px;
}

body .course-item-nav .prev a,
body .course-item-nav .next a
{
	position: relative;
    top: -5px;
}

.learn-press-message:before
{
	display: none;
}

body .course-curriculum ul.curriculum-sections .section.section-empty .learn-press-message
{
	margin-left: 0;
	margin-right: 0;
}

body .lp-label
{
	background: transparent;
	color: #222;
}

body .answer-options .answer-option .option-title:before
{
	display: none;
}

.lp-quiz-buttons .lp-form.form-button button[type=submit]
{
	background: transparent;
    border: none;
    text-decoration: none;
    margin-right: 10px;
    cursor: pointer;
    font-size: 12px;
	font-weight: 900;
	text-transform: uppercase;
	letter-spacing: 2px;
	margin-top: 10px;
	outline: none;
}

body.learnpress-page.checkout:not(.elementor-page) #page_content_wrapper .inner .sidebar_content.full_width,
body.learnpress-page.profile:not(.elementor-page) #page_content_wrapper .inner .sidebar_content.full_width
{
	padding-top: 40px;
	padding-bottom: 80px;
}

body.learnpress-page.profile:not(.admin-bar):not(.elementor-page) #page_content_wrapper .inner .sidebar_content.full_width
{
	padding-top: 60px;
}

.lp-list-table tbody tr td, .lp-list-table tbody tr th
{
	line-height: 1.7;
}

body.learnpress-page.checkout .lp-list-table thead tr th,
body.learnpress-page.profile .lp-list-table thead tr th
{
	background: #333;
}

body.learnpress-page.checkout #learn-press-order-review
{
	float: right;
	width: 32%;
	-webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
}

body.learnpress-page.checkout #learn-press-order-review h4
{
	display: none;
}

body.learnpress-page.checkout .learn-press-checkout-comment h4,
body.learnpress-page.checkout  #learn-press-payment h4
{
	font-size: 16px;
	margin-bottom: 10px;
}

body.learnpress-page.checkout .learn-press-checkout-comment .order-comments::placeholder
{
	opacity: 0;
}

body.learnpress-page.checkout .learn-press-checkout-comment,
body.learnpress-page.checkout  #learn-press-payment
{
	float: left;
	width: 63%;
}

body.learnpress-page.checkout .lp-list-table
{
	margin: 0;
}

body.learnpress-page.checkout #learn-press-payment .payment-methods
{
	margin: 0 !important;
}

body.learnpress-page.checkout #learn-press-payment .payment-methods .lp-payment-method.selected > label
{
	background: transparent;
	padding: 0;
	display: inline-block;
}

body.learnpress-page.checkout #learn-press-payment .payment-methods .payment-method-form
{
	background: transparent;
	border: 0;
	padding: 0;
	font-size: 13px;
	display: inline-block !important;
	margin-left: 10px;
}

body.learnpress-page.checkout #checkout-order-action
{
	margin-top: 30px;
}

body.learnpress-page.profile #learn-press-profile-header
{
	background: transparent;
	margin-bottom: 20px;
	width: 10%;
}

body.learnpress-page.profile #learn-press-profile-header .lp-profile-avatar img
{
	border-radius: 250px;
	position: relative;
    top: -50px;
}

body.learnpress-page.profile .lp-user-profile .profile-name
{
	font-size: 16px;
	font-weight: 700;
	margin-top: -40px;
}

body.learnpress-page.profile #learn-press-profile-nav
{
	padding: 0;
}

body.learnpress-page.profile #learn-press-profile-nav .tabs
{
	margin: 0 !important;
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li a
{
	padding: 10px 20px;
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li ul
{
	margin: 0 !important;
	padding: 0;
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li.active ul
{
	background: transparent;
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li.active ul li a
{
	color: inherit;
	padding-left: 40px;
}

body.learnpress-page.profile #learn-press-profile-content
{
	width: calc(100% - 250px);
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li.active ul li a:hover
{
	color: #fff;
}

body.learnpress-page.profile #learn-press-profile-nav:before
{
	background: #f9f9f9;
}

body.learnpress-page.profile #learn-press-profile-nav,
body.learnpress-page.profile #learn-press-profile-nav .tabs > li:hover:not(.active) ul
{
	background: transparent;
	font-size: 15px;
}

body.learnpress-page.profile .learn-press-form .form-fields
{
	margin: 40px 0 0 0 !important;
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li:hover:not(.active) ul
{
	background: #fff;
	border-radius: 5px;
	-webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li
{
	margin-bottom: 5px;
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li.active > a
{
	color: #fff;
	font-weight: 700;
	border-radius: 5px;
}

body.learnpress-page.profile #learn-press-profile-nav .tabs > li:hover a
{
	border-radius: 5px;
}

body.learnpress-page.profile #learn-press-profile-content
{
	margin-top: -120px;
}

body ul.learn-press-courses .course .course-thumbnail
{
	margin-bottom: 20px;
}

body ul.learn-press-courses .course .course-title
{
	font-weight: 700;
}

body ul.learn-press-courses .course
{
	position: relative;
}

body ul.learn-press-courses .course .lp-course-buttons form button
{
	display: none;
}

body ul.learn-press-courses .course .course-info .course-price .price
{
	position: absolute;
    top: 12px;
    right: 12px;
    font-size: 13px;
    display: inline-block;
    background: #000;
    color: #fff;
    font-weight: 900;
    text-transform: uppercase;
    border-radius: 50%;
    width: 60px;
    text-align: center;
    line-height: 60px;
}

body ul.learn-press-courses .course .course-info .course-instructor
{
	float: none;
	clear: both;
	margin-top: -20px;
}

ul.learn-press-courses .course .course-info .course-instructor:before
{
	font: 16px/60px 'themify';
    content: "\e623";
    margin-right: 5px;
    vertical-align: middle;
}

body.learnpress-page #page_content_wrapper .lp-button
{
	cursor: pointer;
}

body.learnpress-page #page_content_wrapper .order-recover input[name="order-key"]
{
	width: 100%;
}

body.learnpress-page #page_content_wrapper .order-recover .lp-button
{
	clear: both;
	margin-top: 15px;
}

.learnpress-page #learn-press-profile-basic-information p
{
	padding: 0 !important;
}

.learnpress-page #learn-press-profile-basic-information button
{
	cursor: pointer;
	margin-top: 30px;
	outline: none;
}

body.learnpress-page.profile #learn-press-profile-header
{
	z-index: 2;
}

body .lp-avatar-preview .profile-picture
{
	margin-top: 0;
}

body .learn-press-form .form-fields .form-field input[type="text"],
body .learn-press-form .form-fields .form-field input[type="email"],
body .learn-press-form .form-fields .form-field input[type="number"],
body .learn-press-form .form-fields .form-field input[type="password"],
body .learn-press-form .form-fields .form-field textarea
{
	padding: 6px 10px 6px 10px;
}

body #page_content_wrapper p#lp-avatar-actions
{
	padding: 0;
}

body #page_content_wrapper p#lp-avatar-actions button
{
	padding: .6em 2.5em .6em 2.5em;
	cursor: pointer;
	outline: none;
}

body ul.learn-press-courses .course .course-title
{
	line-height: 1.5em;
}

body #learn-press-profile-nav #profile-mobile-menu
{
	font: 20px/60px 'themify';
	text-align: right;
}

body #learn-press-profile-nav #profile-mobile-menu.fa-bars:before
{
	content: "\e60f";
}

#lp-archive-courses form.learn-press-search-course-form
{
	margin: 10px 0 50px 0;
}

#lp-archive-courses ul.learn-press-courses .course,
#profile-content-courses ul.learn-press-courses .course
{
	border-radius: 5px;
    overflow: hidden;
	background: #fff;
}

body ul.learn-press-courses .course .course-title
{
	margin: 25px 25px 0 25px;
}

body ul.learn-press-courses .course .course-info
{
	margin: 10px 25px 10px 25px;
}

body ul.learn-press-courses .course .lp-course-buttons
{
	display: none;
}

body ul.learn-press-courses .course .course-thumbnail img
{
	width: 100%;
}

body ul.learn-press-courses .course
{
	width: 30.66%;
	margin: 0 4% 40px 0;
}

body ul.learn-press-courses .course:nth-child(3n)
{
	margin-right: 0;
}

body .course-curriculum ul.curriculum-sections .section-content .course-item
{
	background: transparent;
}

body.single-lp_course .fullwidth_comment_wrapper
{
	width: 70%;
	margin: 0;
	margin: auto;
    float: none;
}

body.single-lp_course .fullwidth_comment_wrapper #respond.comment-respond
{
	padding-top: 0;
	margin-bottom: 80px;
}

body.archive.post-type-archive-lp_course #page_content_wrapper
{
	padding-bottom: 40px;
}

form[name="search-course"] .search-course-input::placeholder
{
	opacity: 0.5;
}

.lp-label.grade.failed
{
	color: #fff;
}

@font-face {
    font-family: "Jost";
    src: url(../fonts/Jost-300-Light.woff) format("woff");
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: "Jost";
    src: url(../fonts/Jost-400-Book.woff) format("woff");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "Jost";
    src: url(../fonts/Jost-500-Medium.woff) format("woff");
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: "Jost";
    src: url(../fonts/Jost-700-Bold.woff) format("woff");
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: "HK Grotesk";
    src: url(../fonts/HKGrotesk-Regular.woff) format("woff");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "HK Grotesk";
    src: url(../fonts/HKGrotesk-SemiBold.woff) format("woff");
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: "HK Grotesk";
    src: url(../fonts/HKGrotesk-Bold.woff) format("woff");
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: "Inter";
    src: url(../fonts/Inter-Medium.woff) format("woff");
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: "Inter";
    src: url(../fonts/Inter-Bold.woff) format("woff");
    font-weight: 700;
    font-style: normal;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(10%);
    }
    100% {
        opacity: 1;
        transform: translateY(0%);
    }
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-10%);
    }
    100% {
        opacity: 1;
        transform: translateY(0%);
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        transform: translateX(10%);
    }
    100% {
        opacity: 1;
        transform: translateX(0%);
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        transform: translateX(-10%);
    }
    100% {
        opacity: 1;
        transform: translateX(0%);
    }
}

#loftloader-wrapper .loader-section {
    background: #ffffff !important;
}

#loftloader-wrapper .loader-section {
    opacity: 1;
}

#loftloader-wrapper.pl-imgloading #loader {
    width: 130px;
}
#loftloader-wrapper.pl-imgloading #loader span {
    background-size: cover;
    background-image: url(../upload/logo_jessica.png);
}

.lp-label.grade.failed {
    color: #fff;
}

.purchase_theme_button .button {
    border-radius: 5px !important;
}

.tg_navigation_wrapper .nav li.arrow > a:after {
    line-height: 3.2em;
}

.nonfixed {
    position: static !important;
}

.input {
    width: 100%;
    border:1px solid #d8d8d8 !important;
}

textarea.input {
    height: 200px;
}


 #right_click_content {
                background: rgba(0, 0, 0, 0.5);
                color: #ffffff;
            }
            body,
            input[type="text"],
            input[type="password"],
            input[type="email"],
            input[type="url"],
            input[type="date"],
            input[type="tel"],
            input.wpcf7-text,
            .woocommerce table.cart td.actions .coupon .input-text,
            .woocommerce-page table.cart td.actions .coupon .input-text,
            .woocommerce #content table.cart td.actions .coupon .input-text,
            .woocommerce-page #content table.cart td.actions .coupon .input-text,
            select,
            textarea,
            .ui-widget input,
            .ui-widget select,
            .ui-widget textarea,
            .ui-widget button,
            .ui-widget label,
            .ui-widget-header,
            .zm_alr_ul_container {
                font-family: "Jost";
                font-size: 16px;
                font-weight: 400;
                letter-spacing: 0px;
                line-height: 1.7;
                text-transform: none;
            }
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            h7,
            .post_quote_title,
            strong[itemprop="author"],
            #page_content_wrapper .posts.blog li a,
            .page_content_wrapper .posts.blog li a,
            #filter_selected,
            blockquote,
            .sidebar_widget li.widget_products,
            #footer ul.sidebar_widget li ul.posts.blog li a,
            .woocommerce-page table.cart th,
            table.shop_table thead tr th,
            .testimonial_slider_content,
            .pagination,
            .pagination_detail {
                font-family: "Jost";
                font-weight: 700;
                line-height: 1.7;
                text-transform: none;
            }
            h1 {
                font-size: 34px;
            }
            h2 {
                font-size: 30px;
            }
            h3 {
                font-size: 26px;
            }
            h4 {
                font-size: 24px;
            }
            h5 {
                font-size: 22px;
            }
            h6 {
                font-size: 20px;
            }
            body,
            #wrapper,
            #page_content_wrapper.fixed,
            #gallery_lightbox h2,
            .slider_wrapper .gallery_image_caption h2,
            #body_loading_screen,
            h3#reply-title span,
            .overlay_gallery_wrapper,
            .pagination a,
            .pagination span,
            #captcha-wrap .text-box input,
            .flex-direction-nav a,
            .blog_promo_title h6,
            #supersized li,
            #horizontal_gallery_wrapper .image_caption,
            body.tg_password_protected #page_content_wrapper .inner .inner_wrapper .sidebar_content,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] {
                background-color: #f9f9f9;
            }
            body,
            .pagination a,
            #gallery_lightbox h2,
            .slider_wrapper .gallery_image_caption h2,
            .post_info a,
            #page_content_wrapper.split #copyright,
            .page_content_wrapper.split #copyright,
            .ui-state-default a,
            .ui-state-default a:link,
            .ui-state-default a:visited,
            .readmore,
            .woocommerce-MyAccount-navigation ul a,
            .woocommerce #page_content_wrapper div.product p.price,
            .woocommerce-page #page_content_wrapper div.product p.price {
                color: #222222;
            }
            ::selection,
            .verline {
                background-color: #222222;
            }
            ::-webkit-input-placeholder {
                color: #222222;
            }
            ::-moz-placeholder {
                color: #222222;
            }
            :-ms-input-placeholder {
                color: #222222;
            }
            a,
            .gallery_proof_filter ul li a {
                color: #222222;
            }
            .flex-control-paging li a.flex-active,
            .post_attribute a:before,
            #menu_wrapper .nav ul li a:before,
            #menu_wrapper div .nav li > a:before,
            .post_attribute a:before {
                background-color: #222222;
            }
            .flex-control-paging li a.flex-active,
            .image_boxed_wrapper:hover,
            .gallery_proof_filter ul li a.active,
            .gallery_proof_filter ul li a:hover {
                border-color: #222222;
            }
            a:hover,
            a:active,
            .post_info_comment a i {
                color: #0067da;
            }
            input[type="button"]:hover,
            input[type="submit"]:hover,
            a.button:hover,
            .button:hover,
            .button.submit,
            a.button.white:hover,
            .button.white:hover,
            a.button.white:active,
            .button.white:active,
            #menu_wrapper .nav ul li a:hover:before,
            #menu_wrapper div .nav li > a:hover:before,
            .post_attribute a:hover:before {
                background-color: #0067da;
            }
            input[type="button"]:hover,
            input[type="submit"]:hover,
            a.button:hover,
            .button:hover,
            .button.submit,
            a.button.white:hover,
            .button.white:hover,
            a.button.white:active,
            .button.white:active {
                border-color: #0067da;
            }
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            h7,
            pre,
            code,
            tt,
            blockquote,
            .post_header h5 a,
            .post_header h3 a,
            .post_header.grid h6 a,
            .post_header.fullwidth h4 a,
            .post_header h5 a,
            blockquote,
            .site_loading_logo_item i,
            .ppb_subtitle,
            .woocommerce .woocommerce-ordering select,
            .woocommerce #page_content_wrapper a.button,
            .woocommerce.columns-4 ul.products li.product a.add_to_cart_button,
            .woocommerce.columns-4 ul.products li.product a.add_to_cart_button:hover,
            .ui-accordion .ui-accordion-header a,
            .tabs .ui-state-active a,
            .post_header h5 a,
            .post_header h6 a,
            .flex-direction-nav a:before,
            .social_share_button_wrapper .social_post_view .view_number,
            .social_share_button_wrapper .social_post_share_count .share_number,
            .portfolio_post_previous a,
            .portfolio_post_next a,
            #filter_selected,
            #autocomplete li strong,
            .themelink,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .ui-dialog-titlebar .ui-dialog-title,
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .ui-dialog-titlebar .ui-dialog-title {
                color: #222222;
            }
            body.page.page-template-gallery-archive-split-screen-php #fp-nav li .active span,
            body.tax-gallerycat #fp-nav li .active span,
            body.page.page-template-portfolio-fullscreen-split-screen-php #fp-nav li .active span,
            body.page.tax-portfolioset #fp-nav li .active span,
            body.page.page-template-gallery-archive-split-screen-php #fp-nav ul li a span,
            body.tax-gallerycat #fp-nav ul li a span,
            body.page.page-template-portfolio-fullscreen-split-screen-php #fp-nav ul li a span,
            body.page.tax-portfolioset #fp-nav ul li a span {
                background-color: #222222;
            }
            #social_share_wrapper,
            hr,
            #social_share_wrapper,
            .post.type-post,
            .comment .right,
            .widget_tag_cloud div a,
            .meta-tags a,
            .tag_cloud a,
            #footer,
            #post_more_wrapper,
            #page_content_wrapper .inner .sidebar_content,
            #page_content_wrapper .inner .sidebar_content.left_sidebar,
            .ajax_close,
            .ajax_next,
            .ajax_prev,
            .portfolio_next,
            .portfolio_prev,
            .portfolio_next_prev_wrapper.video .portfolio_prev,
            .portfolio_next_prev_wrapper.video .portfolio_next,
            .separated,
            .blog_next_prev_wrapper,
            #post_more_wrapper h5,
            #ajax_portfolio_wrapper.hidding,
            #ajax_portfolio_wrapper.visible,
            .tabs.vertical .ui-tabs-panel,
            .ui-tabs.vertical.right .ui-tabs-nav li,
            .woocommerce div.product .woocommerce-tabs ul.tabs li,
            .woocommerce #content div.product .woocommerce-tabs ul.tabs li,
            .woocommerce-page div.product .woocommerce-tabs ul.tabs li,
            .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li,
            .woocommerce div.product .woocommerce-tabs .panel,
            .woocommerce-page div.product .woocommerce-tabs .panel,
            .woocommerce #content div.product .woocommerce-tabs .panel,
            .woocommerce-page #content div.product .woocommerce-tabs .panel,
            .woocommerce table.shop_table,
            .woocommerce-page table.shop_table,
            .woocommerce .cart-collaterals .cart_totals,
            .woocommerce-page .cart-collaterals .cart_totals,
            .woocommerce .cart-collaterals .shipping_calculator,
            .woocommerce-page .cart-collaterals .shipping_calculator,
            .woocommerce .cart-collaterals .cart_totals tr td,
            .woocommerce .cart-collaterals .cart_totals tr th,
            .woocommerce-page .cart-collaterals .cart_totals tr td,
            .woocommerce-page .cart-collaterals .cart_totals tr th,
            table tr th,
            table tr td,
            .woocommerce #payment,
            .woocommerce-page #payment,
            .woocommerce #payment ul.payment_methods li,
            .woocommerce-page #payment ul.payment_methods li,
            .woocommerce #payment div.form-row,
            .woocommerce-page #payment div.form-row,
            .ui-tabs li:first-child,
            .ui-tabs .ui-tabs-nav li,
            .ui-tabs.vertical .ui-tabs-nav li,
            .ui-tabs.vertical.right .ui-tabs-nav li.ui-state-active,
            .ui-tabs.vertical .ui-tabs-nav li:last-child,
            #page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul.menu li.current-menu-item a,
            .page_content_wrapper .inner .sidebar_wrapper ul.sidebar_widget li.widget_nav_menu ul.menu li.current-menu-item a,
            .ui-accordion .ui-accordion-header,
            .ui-accordion .ui-accordion-content,
            #page_content_wrapper .sidebar .content .sidebar_widget li h2.widgettitle:before,
            h2.widgettitle:before,
            #autocomplete,
            .ppb_blog_minimal .one_third_bg,
            .tabs .ui-tabs-panel,
            .ui-tabs .ui-tabs-nav li,
            .ui-tabs li:first-child,
            .ui-tabs.vertical .ui-tabs-nav li:last-child,
            .woocommerce .woocommerce-ordering select,
            .woocommerce div.product .woocommerce-tabs ul.tabs li.active,
            .woocommerce-page div.product .woocommerce-tabs ul.tabs li.active,
            .woocommerce #content div.product .woocommerce-tabs ul.tabs li.active,
            .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li.active,
            .woocommerce-page table.cart th,
            table.shop_table thead tr th,
            hr.title_break,
            .overlay_gallery_border,
            #page_content_wrapper.split #copyright,
            .page_content_wrapper.split #copyright,
            .post.type-post,
            .events.type-events,
            h5.event_title,
            .post_header h5.event_title,
            .client_archive_wrapper,
            #page_content_wrapper .sidebar .content .sidebar_widget li.widget,
            .page_content_wrapper .sidebar .content .sidebar_widget li.widget,
            hr.title_break.bold,
            blockquote,
            .social_share_button_wrapper,
            .social_share_button_wrapper,
            body:not(.single) .post_wrapper,
            .themeborder,
            #about_the_author,
            .related.products,
            .woocommerce div.product div.summary .product_meta,
            #single_course_meta ul.single_course_meta_data li.single_course_meta_data_separator,
            body .course-curriculum ul.curriculum-sections .section-header,
            body.single-post #page_content_wrapper.blog_wrapper .page_title_content {
                border-color: #d8d8d8;
            }
            input[type="text"],
            input[type="password"],
            input[type="email"],
            input[type="url"],
            input[type="tel"],
            input[type="date"],
            textarea,
            select {
                background-color: #ffffff;
                color: #222222;
                border-color: #d8d8d8;
                -webkit-border-radius: 5px;
                -moz-border-radius: 5px;
                border-radius: 5px;
            }
            input[type="submit"],
            input[type="button"],
            a.button,
            .button,
            .woocommerce .page_slider a.button,
            a.button.fullwidth,
            .woocommerce-page div.product form.cart .button,
            .woocommerce #respond input#submit.alt,
            .woocommerce a.button.alt,
            .woocommerce button.button.alt,
            .woocommerce input.button.alt,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"] {
                font-family: "Jost";
                font-size: 16px;
                font-weight: 400;
                line-height: 1.7;
                text-transform: none;
            }
            input[type="text"]:focus,
            input[type="password"]:focus,
            input[type="email"]:focus,
            input[type="url"]:focus,
            input[type="date"]:focus,
            textarea:focus {
                border-color: #0067da;
            }
            .input_effect ~ .focus-border {
                background-color: #0067da;
            }
            input[type="submit"],
            input[type="button"],
            a.button,
            .button,
            .woocommerce .page_slider a.button,
            a.button.fullwidth,
            .woocommerce-page div.product form.cart .button,
            .woocommerce #respond input#submit.alt,
            .woocommerce a.button.alt,
            .woocommerce button.button.alt,
            .woocommerce input.button.alt,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
            body.learnpress-page #page_content_wrapper .order-recover .lp-button,
            .learnpress-page #learn-press-profile-basic-information button,
            body #page_content_wrapper p#lp-avatar-actions button,
            .learnpress-page #profile-content-settings form button[type="submit"] {
                font-family: "Jost";
                font-size: 13px;
                font-weight: 700;
                letter-spacing: 1px;
                line-height: 1.7;
                text-transform: uppercase;
            }
            input[type="submit"],
            input[type="button"],
            a.button,
            .button,
            .woocommerce .page_slider a.button,
            a.button.fullwidth,
            .woocommerce-page div.product form.cart .button,
            .woocommerce #respond input#submit.alt,
            .woocommerce a.button.alt,
            .woocommerce button.button.alt,
            .woocommerce input.button.alt,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
            a#toTop,
            .pagination span,
            .widget_tag_cloud div a,
            .pagination a,
            .pagination span,
            body.learnpress-page #page_content_wrapper .order-recover .lp-button,
            .learnpress-page #learn-press-profile-basic-information button,
            body #page_content_wrapper p#lp-avatar-actions button,
            .learnpress-page #profile-content-settings form button[type="submit"],
            .learnpress-page #page_content_wrapper .lp-button {
                -webkit-border-radius: 25px;
                -moz-border-radius: 25px;
                border-radius: 25px;
            }
            input[type="submit"],
            input[type="button"],
            a.button,
            .button,
            .pagination span,
            .pagination a:hover,
            .woocommerce .footer_bar .button,
            .woocommerce .footer_bar .button:hover,
            .woocommerce-page div.product form.cart .button,
            .woocommerce #respond input#submit.alt,
            .woocommerce a.button.alt,
            .woocommerce button.button.alt,
            .woocommerce input.button.alt,
            .post_type_icon,
            .filter li a:hover,
            .filter li a.active,
            #portfolio_wall_filters li a.active,
            #portfolio_wall_filters li a:hover,
            .comment_box,
            .one_half.gallery2 .portfolio_type_wrapper,
            .one_third.gallery3 .portfolio_type_wrapper,
            .one_fourth.gallery4 .portfolio_type_wrapper,
            .one_fifth.gallery5 .portfolio_type_wrapper,
            .portfolio_type_wrapper,
            .post_share_text,
            #close_share,
            .widget_tag_cloud div a:hover,
            .ui-accordion .ui-accordion-header .ui-icon,
            .mobile_menu_wrapper #mobile_menu_close.button,
            .mobile_menu_wrapper #close_mobile_menu,
            .multi_share_button,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
            .learnpress-page #page_content_wrapper .lp-button,
            .learnpress-page #learn-press-profile-basic-information button,
            .learnpress-page #profile-content-settings form button[type="submit"] {
                background-color: #0067da;
            }
            .pagination span,
            .pagination a:hover,
            .button.ghost,
            .button.ghost:hover,
            .button.ghost:active,
            blockquote:after,
            .woocommerce-MyAccount-navigation ul li.is-active,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
            .learnpress-page #page_content_wrapper .lp-button,
            .learnpress-page #learn-press-profile-basic-information button,
            .learnpress-page #profile-content-settings form button[type="submit"] {
                border-color: #0067da;
            }
            .comment_box:before,
            .comment_box:after {
                border-top-color: #0067da;
            }
            .button.ghost,
            .button.ghost:hover,
            .button.ghost:active,
            .infinite_load_more,
            blockquote:before,
            .woocommerce-MyAccount-navigation ul li.is-active a,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"] {
                color: #0067da;
            }
            input[type="submit"],
            input[type="button"],
            a.button,
            .button,
            .pagination a:hover,
            .woocommerce .footer_bar .button,
            .woocommerce .footer_bar .button:hover,
            .woocommerce-page div.product form.cart .button,
            .woocommerce #respond input#submit.alt,
            .woocommerce a.button.alt,
            .woocommerce button.button.alt,
            .woocommerce input.button.alt,
            .post_type_icon,
            .filter li a:hover,
            .filter li a.active,
            #portfolio_wall_filters li a.active,
            #portfolio_wall_filters li a:hover,
            .comment_box,
            .one_half.gallery2 .portfolio_type_wrapper,
            .one_third.gallery3 .portfolio_type_wrapper,
            .one_fourth.gallery4 .portfolio_type_wrapper,
            .one_fifth.gallery5 .portfolio_type_wrapper,
            .portfolio_type_wrapper,
            .post_share_text,
            #close_share,
            .widget_tag_cloud div a:hover,
            .ui-accordion .ui-accordion-header .ui-icon,
            .mobile_menu_wrapper #mobile_menu_close.button,
            #toTop,
            .multi_share_button,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
            .pagination span.current,
            .mobile_menu_wrapper #close_mobile_menu,
            body.learnpress-page #page_content_wrapper .lp-button,
            .learnpress-page #learn-press-profile-basic-information button,
            .learnpress-page #profile-content-settings form button[type="submit"] {
                color: #ffffff;
            }
            input[type="submit"],
            input[type="button"],
            a.button,
            .button,
            .pagination a:hover,
            .woocommerce .footer_bar .button,
            .woocommerce .footer_bar .button:hover,
            .woocommerce-page div.product form.cart .button,
            .woocommerce #respond input#submit.alt,
            .woocommerce a.button.alt,
            .woocommerce button.button.alt,
            .woocommerce input.button.alt,
            .infinite_load_more,
            .post_share_text,
            #close_share,
            .widget_tag_cloud div a:hover,
            .mobile_menu_wrapper #close_mobile_menu,
            .mobile_menu_wrapper #mobile_menu_close.button,
            body .ui-dialog[aria-describedby="ajax-login-register-login-dialog"] .form-wrapper input[type="submit"],
            body .ui-dialog[aria-describedby="ajax-login-register-dialog"] .form-wrapper input[type="submit"],
            .learnpress-page #learn-press-profile-basic-information button,
            .learnpress-page #profile-content-settings form button[type="submit"] {
                border-color: #0067da;
            }
            input[type="button"]:hover,
            input[type="submit"]:hover,
            a.button:hover,
            .button:hover,
            .button.submit,
            a.button.white:hover,
            .button.white:hover,
            a.button.white:active,
            .button.white:active,
            .black_bg input[type="submit"],
            .learnpress-page #page_content_wrapper .lp-button:hover,
            .learnpress-page #learn-press-profile-basic-information button:hover,
            .learnpress-page #profile-content-settings form button[type="submit"]:hover {
                background-color: #ffffff;
            }
            input[type="button"]:hover,
            input[type="submit"]:hover,
            a.button:hover,
            .button:hover,
            .button.submit,
            a.button.white:hover,
            .button.white:hover,
            a.button.white:active,
            .button.white:active,
            .black_bg input[type="submit"],
            body.learnpress-page #page_content_wrapper .lp-button:hover,
            .learnpress-page #learn-press-profile-basic-information button:hover,
            .learnpress-page #profile-content-settings form button[type="submit"]:hover {
                color: #0067da;
            }
            input[type="button"]:hover,
            input[type="submit"]:hover,
            a.button:hover,
            .button:hover,
            .button.submit,
            a.button.white:hover,
            .button.white:hover,
            a.button.white:active,
            .button.white:active,
            .black_bg input[type="submit"],
            .learnpress-page #learn-press-profile-basic-information button:hover,
            .learnpress-page #profile-content-settings form button[type="submit"]:hover {
                border-color: #0067da;
            }
            .frame_top,
            .frame_bottom,
            .frame_left,
            .frame_right {
                background: #222222;
            }
            #menu_wrapper .nav ul li a,
            #menu_wrapper div .nav li > a,
            .header_client_wrapper {
                font-family: "Jost";
                font-size: 14px;
                font-weight: 700;
                line-height: 1.7;
                text-transform: none;
            }
            #menu_wrapper .nav ul li,
            html[data-menu="centeralogo"] #logo_right_button {
                padding-top: 26px;
                padding-bottom: 26px;
            }
            .top_bar,
            html {
                background-color: #ffffff;
            }
            #menu_wrapper .nav ul li a,
            #menu_wrapper div .nav li > a,
            #mobile_nav_icon,
            #logo_wrapper .social_wrapper ul li a,
            .header_cart_wrapper a {
                color: #222222;
            }
            #mobile_nav_icon {
                border-color: #222222;
            }
            #menu_wrapper .nav ul li a.hover,
            #menu_wrapper .nav ul li a:hover,
            #menu_wrapper div .nav li a.hover,
            #menu_wrapper div .nav li a:hover,
            .header_cart_wrapper a:hover,
            #page_share:hover,
            #logo_wrapper .social_wrapper ul li a:hover {
                color: #0067da;
            }
            #menu_wrapper .nav ul li a:before,
            #menu_wrapper div .nav li > a:before {
                background-color: #0067da;
            }
            #menu_wrapper div .nav > li.current-menu-item > a,
            #menu_wrapper div .nav > li.current-menu-parent > a,
            #menu_wrapper div .nav > li.current-menu-ancestor > a,
            #menu_wrapper div .nav li ul:not(.sub-menu) li.current-menu-item a,
            #menu_wrapper div .nav li.current-menu-parent ul li.current-menu-item a,
            #logo_wrapper .social_wrapper ul li a:active {
                color: #0067da;
            }
            .top_bar,
            #nav_wrapper {
                border-color: #ffffff;
            }
            .header_cart_wrapper .cart_count {
                background-color: #0067da;
                color: #ffffff;
            }
            #menu_wrapper .nav ul li ul li a,
            #menu_wrapper div .nav li ul li a,
            #menu_wrapper div .nav li.current-menu-parent ul li a {
                font-family: "Jost";
                font-size: 14px;
                font-weight: 700;
                text-transform: none;
            }
            #menu_wrapper .nav ul li ul li a,
            #menu_wrapper div .nav li ul li a,
            #menu_wrapper div .nav li.current-menu-parent ul li a,
            #menu_wrapper div .nav li.current-menu-parent ul li.current-menu-item a,
            #menu_wrapper .nav ul li.megamenu ul li ul li a,
            #menu_wrapper div .nav li.megamenu ul li ul li a {
                color: #222222;
            }
            #menu_wrapper .nav ul li ul li a:hover,
            #menu_wrapper div .nav li ul li a:hover,
            #menu_wrapper div .nav li.current-menu-parent ul li a:hover,
            #menu_wrapper .nav ul li.megamenu ul li ul li a:hover,
            #menu_wrapper div .nav li.megamenu ul li ul li a:hover,
            #menu_wrapper .nav ul li.megamenu ul li ul li a:active,
            #menu_wrapper div .nav li.megamenu ul li ul li a:active,
            #menu_wrapper div .nav li.current-menu-parent ul li.current-menu-item a:hover {
                color: #0067da;
            }
            #menu_wrapper .nav ul li ul li a:before,
            #menu_wrapper div .nav li ul li > a:before,
            #wrapper.transparent .top_bar:not(.scroll) #menu_wrapper div .nav ul li ul li a:before {
                background-color: #0067da;
            }
            #menu_wrapper .nav ul li ul,
            #menu_wrapper div .nav li ul {
                background: #ffffff;
                border-color: #d8d8d8;
            }
            #menu_wrapper div .nav li.megamenu ul li > a,
            #menu_wrapper div .nav li.megamenu ul li > a:hover,
            #menu_wrapper div .nav li.megamenu ul li > a:active,
            #menu_wrapper div .nav li.megamenu ul li.current-menu-item > a {
                color: #222222;
            }
            #menu_wrapper div .nav li.megamenu ul li {
                border-color: #eeeeee;
            }
            .above_top_bar {
                background: #222222;
            }
            #top_menu li a,
            .top_contact_info,
            .top_contact_info i,
            .top_contact_info a,
            .top_contact_info a:hover,
            .top_contact_info a:active {
                color: #ffffff;
            }
            .mobile_main_nav li a,
            #sub_menu li a {
                font-family: "Jost";
                font-size: 18px;
                font-weight: 700;
                line-height: 2;
                text-transform: none;
            }
            #sub_menu li a {
                font-family: "Jost";
                font-size: 18px;
                font-weight: 700;
                line-height: 2;
                text-transform: none;
            }
            .mobile_menu_wrapper {
                background-color: #000000;
            }
            .mobile_main_nav li a,
            #sub_menu li a,
            .mobile_menu_wrapper .sidebar_wrapper a,
            .mobile_menu_wrapper .sidebar_wrapper,
            #close_mobile_menu i,
            .mobile_menu_wrapper .social_wrapper ul li a,
            .fullmenu_content #copyright,
            .mobile_menu_wrapper .sidebar_wrapper h2.widgettitle {
                color: #ffffff;
            }
            .mobile_main_nav li a:hover,
            .mobile_main_nav li a:active,
            #sub_menu li a:hover,
            #sub_menu li a:active,
            .mobile_menu_wrapper .social_wrapper ul li a:hover {
                color: #ffffff;
            }
            #page_caption.hasbg {
                height: 600px;
            }
            #page_caption {
                background-color: #ffffff;
                padding-top: 60px;
                padding-bottom: 60px;
                margin-bottom: 45px;
            }
            #page_caption .page_title_wrapper .page_title_inner {
                text-align: center;
            }
            #page_caption h1 {
                font-family: "Jost";
                font-size: 45px;
                font-weight: 700;
                line-height: 1.2;
                text-transform: none;
                color: #222222;
            }
            .page_tagline,
            .thumb_content span,
            .portfolio_desc .portfolio_excerpt,
            .testimonial_customer_position,
            .testimonial_customer_company,
            .post_detail.single_post {
                font-family: "Jost";
                font-size: 13px;
                font-weight: 400;
                letter-spacing: 2px;
                text-transform: uppercase;
                color: #222222;
            }
            #page_content_wrapper .sidebar .content .sidebar_widget li h2.widgettitle,
            h2.widgettitle,
            h5.widgettitle {
                font-family: "Jost";
                font-size: 18px;
                font-weight: 700;
                letter-spacing: 0px;
                text-transform: none;
                color: #222222;
                border-color: #222222;
            }
            #page_content_wrapper .inner .sidebar_wrapper .sidebar .content,
            .page_content_wrapper .inner .sidebar_wrapper .sidebar .content {
                color: #222222;
            }
            #page_content_wrapper .inner .sidebar_wrapper a:not(.button),
            .page_content_wrapper .inner .sidebar_wrapper a:not(.button) {
                color: #222222;
            }
            #page_content_wrapper .inner .sidebar_wrapper a:hover:not(.button),
            #page_content_wrapper .inner .sidebar_wrapper a:active:not(.button),
            .page_content_wrapper .inner .sidebar_wrapper a:hover:not(.button),
            .page_content_wrapper .inner .sidebar_wrapper a:active:not(.button) {
                color: #0067da;
            }
            #page_content_wrapper .inner .sidebar_wrapper a:not(.button):before {
                background-color: #0067da;
            }
            #footer {
                font-size: 15px;
            }
            .footer_bar_wrapper {
                font-size: 13px;
            }
            .footer_bar,
            #footer {
                background-color: #222222;
            }
            #footer,
            #copyright,
            #footer_menu li a,
            #footer_menu li a:hover,
            #footer_menu li a:active,
            #footer input[type="text"],
            #footer input[type="password"],
            #footer input[type="email"],
            #footer input[type="url"],
            #footer input[type="tel"],
            #footer input[type="date"],
            #footer textarea,
            #footer select,
            #footer blockquote {
                color: #999999;
            }
            #copyright a,
            #copyright a:active,
            #footer a,
            #footer a:active,
            #footer .sidebar_widget li h2.widgettitle,
            #footer_photostream a {
                color: #ffffff;
            }
            #footer .sidebar_widget li h2.widgettitle {
                border-color: #ffffff;
            }
            #copyright a:hover,
            #footer a:hover,
            .social_wrapper ul li a:hover,
            #footer a:hover,
            #footer_photostream a:hover {
                color: #ffffff;
            }
            .footer_bar {
                background-color: #222222;
            }
            .footer_bar,
            #copyright {
                color: #999999;
            }
            .footer_bar a,
            #copyright a,
            #footer_menu li a {
                color: #ffffff;
            }
            .footer_bar a:hover,
            #copyright a:hover,
            #footer_menu li a:hover {
                color: #ffffff;
            }
            .footer_bar_wrapper,
            .footer_bar {
                border-color: #333333;
            }
            .footer_bar_wrapper .social_wrapper ul li a {
                color: #ffffff;
            }
            a#toTop {
                background: rgba(0, 0, 0, 0.1);
                color: #ffffff;
            }
            #page_content_wrapper.blog_wrapper,
            #page_content_wrapper.blog_wrapper input:not([type="submit"]),
            #page_content_wrapper.blog_wrapper textarea,
            .post_excerpt.post_tag a:after,
            .post_excerpt.post_tag a:before,
            .post_navigation .navigation_post_content {
                background-color: #ffffff;
            }
            .post_info_cat,
            .post_info_cat a {
                color: #444444;
                border-color: #444444;
            }
            .post_img_hover .post_type_icon {
                background: #0067da;
            }
            .blog_post_content_wrapper.layout_grid .post_content_wrapper,
            .blog_post_content_wrapper.layout_masonry .post_content_wrapper,
            .blog_post_content_wrapper.layout_metro .post_content_wrapper,
            .blog_post_content_wrapper.layout_classic .post_content_wrapper {
                background: #ffffff;
            }
            .post_attribute a {
                color: #222222;
            }
            .post_attribute a a:before {
                background-color: #222222;
            }
            .post_attribute a,
            .post_button_wrapper .post_attribute,
            .post_attribute a:before {
                opacity: 0.5;
            }
            .post_header h5,
            h6.subtitle,
            .post_caption h1,
            #page_content_wrapper .posts.blog li a,
            .page_content_wrapper .posts.blog li a,
            #post_featured_slider li .slider_image .slide_post h2,
            .post_header.grid h6,
            .post_info_cat,
            .comment_date,
            .post-date,
            .post_navigation h7 {
                font-family: "Jost";
                font-weight: 700;
                letter-spacing: 0px;
                text-transform: none;
            }
            body.single-post #page_caption h1,
            body.single-post #page_content_wrapper.blog_wrapper .page_title_content h1 {
                font-family: "Jost";
                font-size: 50px;
                font-weight: 700;
                line-height: 1.2;
                text-transform: none;
            }
            body.single-post #page_content_wrapper.blog_wrapper,
            .post_related .post_header_wrapper {
                background: #ffffff;
            }
            .post_excerpt.post_tag a {
                background: #f0f0f0;
                color: #444;
            }
            .post_excerpt.post_tag a:after {
                border-left-color: #f0f0f0;
            }
            .woocommerce ul.products li.product .price ins,
            .woocommerce-page ul.products li.product .price ins,
            .woocommerce ul.products li.product .price,
            .woocommerce-page ul.products li.product .price,
            p.price ins span.amount,
            .woocommerce #content div.product p.price,
            .woocommerce #content div.product span.price,
            .woocommerce div.product p.price,
            .woocommerce div.product span.price,
            .woocommerce-page #content div.product p.price,
            .woocommerce-page #content div.product span.price,
            .woocommerce-page div.product p.price,
            .woocommerce-page div.product span.price {
                color: #0067da;
            }
            .woocommerce ul.products li.product .price ins,
            .woocommerce-page ul.products li.product .price ins,
            .woocommerce ul.products li.product .price,
            .woocommerce-page ul.products li.product .price,
            p.price ins span.amount,
            .woocommerce #content div.product p.price,
            .woocommerce #content div.product span.price {
                background: #f0f0f0;
            }
            .woocommerce .products .onsale,
            .woocommerce ul.products li.product .onsale,
            .woocommerce span.onsale {
                background-color: #0067da;
            }
            .woocommerce div.product .woocommerce-tabs ul.tabs li.active a,
            .woocommerce-page div.product .woocommerce-tabs ul.tabs li.active a {
                color: #ffffff;
            }
            .woocommerce div.product .woocommerce-tabs ul.tabs li.active,
            .woocommerce-page div.product .woocommerce-tabs ul.tabs li.active {
                background: #222222;
            }
            body.single-product div.product.type-product {
                background: #ffffff;
            }
            body .course-curriculum ul.curriculum-sections .section-content .course-item.item-preview .course-item-status,
            body.learnpress-page.profile #learn-press-profile-nav .tabs > li.active > a,
            body.learnpress-page.profile #learn-press-profile-nav .tabs > li a:hover,
            body.learnpress-page.profile #learn-press-profile-nav .tabs > li:hover:not(.active) > a,
            body ul.learn-press-courses .course .course-info .course-price .price {
                background: #0067da;
            }
            body .course-item-nav .prev span,
            body .course-item-nav .next span,
            body .course-curriculum ul.curriculum-sections .section-content .course-item.current a {
                color: #0067da;
            }
            #page_content_wrapper ul.learn-press-nav-tabs .course-nav a {
                background: #f9f9f9;
                color: #222222;
            }
            #page_content_wrapper ul.learn-press-nav-tabs .course-nav.active a,
            body.learnpress-page.profile .lp-tab-sections .section-tab.active span {
                background: #000000;
                color: #ffffff;
            }
            body.learnpress-page.checkout .lp-list-table thead tr th,
            body.learnpress-page.profile .lp-list-table thead tr th {
                background: #333333;
            }
            body.learnpress-page.checkout .lp-list-table {
                color: #222222;
            }
            body .lp-list-table th,
            body .lp-list-table td,
            body .lp-list-table tbody tr td,
            body .lp-list-table tbody tr th {
                background: #ffffff;
            }
            body .lp-list-table tbody tr td,
            body .lp-list-table tbody tr th,
            body .lp-list-table td {
                border-color: #d8d8d8;
            }
            body .lp-list-table th,
            body .lp-list-table td {
                color: #222222;
            }
            body.single-lp_course #lp-single-course .single_course_title h1,
            body.single-meeting .single_course_title h1 {
                font-family: "Jost";
                font-size: 34px;
                font-weight: 700;
                line-height: 1.7;
                text-transform: none;
                color: #222222;
            }
            #single_course_meta ul.single_course_meta_data {
                background: #ffffff;
                color: #222222;
            }

            .single_post_wrapper {
                padding: 20px !important;
            }

			@charset "utf-8";
/* CSS Document */

#wrapper
{
	overflow:hidden;
	width:100%;
}
.slider img
{
	width:100%;
	height:auto;
}

.header-top {
	display: none;
}

.phone p
{
	font-family: 'Lato', sans-serif;
	font-weight:400;
	font-size:14px;
	color:#bababa;
	padding-top: 2px;
	float:left;
	padding-right: 35px;
}
.phone p a, .pack  a
{
	color:#bababa;
	text-decoration:none;
}
.phone img
{
	float:left;
    margin-right: 12px;
}
.topdata
{
	    position: absolute;
    left: 0;
    text-align: right;
    right: 0;
	z-index:99;
	top:20px;
}


.nav-heading{
  font-size: 28px;
}

.pack{
  display: flex;
  justify-content:center;
  align-items: center
}
.email img
{
		float:left;
    margin-right: 12px;
}
.email p
{
	font-family: 'Raleway', sans-serif;
	font-weight:500;
	font-size:14px;
	color:#bababa;
	padding-top: 1px;
		float:left;
		padding-right: 30px;
}
.email p a
{
	color:#bababa;
	text-decoration:none;
}
.chat img
{
		float:left;
    margin-right: 8px;
}
.chat p
{
	font-family: 'Raleway', sans-serif;
	font-weight:500;
	font-size:14px;
	color:#bababa;
	padding-top: 1px;
		float:left;
		    text-transform: uppercase;
}
.chat p a
{
	text-decoration:none;
	color:#bababa;
}

.carousel-caption h3
{
	font-family: 'Raleway', sans-serif;
	font-weight:700;
	color:#dbdbda;
	font-size:47.33px;
	text-align:center;
	text-transform:uppercase;
}
.carousel-caption h3 span
{
	font-size:42.73px;
}
.carousel-caption p
{
	font-family: 'Raleway', sans-serif;
	font-weight:400;
	font-size:22.09px;
	color:#d6d5d5;
	margin-bottom:40px;
}
.carousel-caption
{
	top: 34%;
	bottom:0;
}
.num
{
	border:#ffffff 1px solid;
	border-radius:20px;
		font-family: 'Lato', sans-serif;
	font-weight:700;
	font-size:18px;
	color:#f6f6f6;
	background:none;
	padding:11px 28px;
		text-decoration:none;
		margin-top:43px;
		margin-right:12px;
}
.num:hover
{
		color:#f6f6f6;
		text-decoration:none;
		background:#000;
		border:1px #000 solid;

}
.num:focus
{
	color:#f6f6f6;
		text-decoration:none;
}
.live
{
	border:#ffffff 1px solid;
	border-radius:20px;
	font-family: 'Raleway', sans-serif;
	font-weight:700;
	font-size:18px;
	color:#f6f6f6;
	background:none;
	padding:11px 51px;
		text-decoration:none;
		margin-top:43px;
}
.live:hover
{
		color:#f6f6f6;
		text-decoration:none;
		background:#000;
		border:1px #000 solid;
}
.live:focus
{
	color:#f6f6f6;
		text-decoration:none;
}
.bootombtn
{
	    position: absolute;
    z-index: 99;
    bottom: 86px;
    left: 0;
    right: 0;
}
.bootombtn img
{
	margin:0 auto;
	display:block;


}
.navbar-default
{
	background-color:#f6f6f6;
	border-bottom:none;
	border-radius:0;
}
.navbar-brand
{
	margin-right:77px;
}

.main-menu{
  display: flex;
  justify-content: center;
  align-items: center;
}

.menu-button{
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20px;
}

.menu li
{
	padding-top:53px;
	    padding-bottom: 50px;
}
 .navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:focus, .navbar-default .navbar-nav>.active>a:hover
{
	background-color:transparent !important;
}
.menu li a
{
		font-family: 'Raleway', sans-serif;
	font-weight:700;
	font-size:20px;
	color:#00336e !important;
	text-transform:uppercase;
	    padding: 0 15px;
}
.menu li a:after
{
	content:"";
	height:15px;
	width:2px;
	background:#002b5c;
	display:block;
	float:right;
	    margin-top: 2px;
    text-align: center;
    margin-left: 18px;
}
.menu li:last-child a:after

  .text-center {
  text-align: center;

}

.flex-row{
  display: flex;
  justify-content: center;
  align-items: center;
}


{
	display:none;
}
.getfree
{
	background:#254d86;
	font-family: 'Raleway', sans-serif;
	font-weight:700;
	font-size:14px;
	color:#ffffff;
	    padding: 9px 36px;
    border-radius: 14px;
    position: relative;
	    left: 12px;
		text-transform:uppercase;
}
.getfree:hover
{
	color:#ffffff;
	text-decoration:none;
}
.getfree:focus
{
	color:#ffffff;
	text-decoration:none;
}
.marketing
{
	padding-top:54px;
	clear:both;
}
.text p
{
	font-family: 'Raleway', sans-serif;
	font-weight:400;
	font-size:16px;
	color:#313131;
	text-align:center;
}
.bxsli
{
	margin-top:67px;
}
.bxslider img
{
	margin-left:0;
}
img.left {
    margin-left: -23px;
}
.bx-wrapper .bx-prev {
    left: -49px;
}
.bx-wrapper .bx-next {
    right: -49px;
}

.heading h4
{
	font-family: 'Raleway', sans-serif;
	font-weight:700;
	font-size:30px;
	color:#313131;
	text-align:center;
	text-transform:uppercase;
	margin-bottom:0;


}
.heading h4:before
{
	content:"";
	height:2px;
	width:212px;
	background:#00b1f8;
	clear:both;
	display:block;
	margin:100px auto 35px;
}
.heading p
{
	font-family: 'Raleway', sans-serif;
	font-weight:400;
	font-size:16px;
	color:#313131;
	text-align:center;
	padding-top:10px;
}
.imgtext
{
	margin-top:50px;

}
.imgtext h4
{
	font-family: 'Raleway', sans-serif;
	font-weight:600;
	font-size:16px;
	color:#003d83;
	text-align:center;
	text-transform:uppercase;
	line-height:23px;
}
.imgtext img
{
	margin-bottom:20px;
}
.imgtext p
{
		font-family: 'Raleway', sans-serif;
	font-weight:400;
	font-size:14px;
	color:#000;
	text-align:center;
	padding-top:20px;
	padding-bottom:80px;
}

.factbg{
	background:url(../images/bg.jpg) no-repeat;
	width:100%;
	background-position:top center;
	background-size:cover;
	clear:both;
	padding-top:60px;
	padding-bottom:90px;
}
.facthead h3
{
			font-family: 'Raleway', sans-serif;
	font-weight:700;
	font-size:30px;
	color:#ffffff;
	text-align:center;
	text-transform:uppercase;
}
.facthead p
{

			font-family: 'Raleway', sans-serif;
	font-weight:400;
	font-size:16px;
	color:#ffffff;
	text-align:center;
}
.facthead h3:before
{
	content:"";
	height:2px;
	width:212px;
	background:#00b1f8;
	clear:both;
	display:block;
	margin:0px auto 39px;
}
.facttext
{
	background:rgba(255,255,255,0.8);
	border:none;
	border-radius:0;
	margin-top:60px;
}
.facttext p
{
		font-family: 'Raleway', sans-serif;
	font-weight:500;
	font-size:15px;
	color:#515151;
	text-align:center;
       padding: 25px 30px 25px 30px;
	    overflow: hidden;
    overflow-y: auto;
    max-height: 250px;
	line-height:25px;
}
.facttext p::-webkit-resizer {
    background-color: #666;
    cursor: pointer;
}
.facttext p::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.facttext p::-webkit-scrollbar-button {
    display: none;
    cursor: pointer;
}
.facttext p::-webkit-scrollbar-corner {
    background-color: #999;
    cursor: pointer;
}
.facttext p::-webkit-scrollbar-thumb {
    height: 50px;
    cursor: pointer;
    background-color: #00b1f8;
    border-radius: 3px;
}
.facttext p::-webkit-scrollbar-track {
    background-color: #00b1f8;
    cursor: pointer;
}
.facttext p::-webkit-scrollbar-track-piece {
    background-color: rgba(255,255,255,0.8);
    cursor: pointer;
}

.detailbg
{
		background:url(../images/detailbg.jpg) no-repeat;
	width:100%;
	background-position:top center;
	background-size:cover;
	clear:both;
	padding-top:60px;
	padding-bottom:60px;
}
/*.detailtext
{
	margin-left:70px;
}*/
.detailtext h6
{
		font-family: 'Raleway', sans-serif;
	font-weight:700;
	font-size:26px;
	color:#fff;
	text-transform:uppercase;
	    line-height: 30px;
}
.details ul
{
	list-style:none;
	padding:0;
	margin-left: 76px;
	display:inline;
}
.details ul li
{
	font-size:14px;
	font-weight:700;
	font-family: 'Lato', sans-serif;
	color:#ffffff;
	border:#e9e20f 1px solid;
	display:inline-block;
	margin-right: 30px;
	padding:12px 20px 10px;
	margin-bottom: 16px;
	margin-top: 10px;
	min-width: 261px;
	text-align: center;
}
.details ul li a:hover {
    color: #e9e20f;
}
.details ul li a
{
	color:#fff;
	text-decoration:none;
}
.details ul li img
{
	margin-right:8px;
}
.detail2 ul li a:hover {
    color: #e9e20f;
}
.detail2 ul li a
{
	color:#fff;
	text-decoration:none;
}

li.change{

	font-family: 'Raleway', sans-serif !important;
	font-size:14px !important;
	font-weight:700 !important;
	display:inline-block !important;

}

.detail2 ul
{
	list-style:none;
	padding:0;
	margin-left: 76px;
	display:inline;
}
.detail2 ul li
{
	font-size:14px;
	font-weight:700;
	font-family: 'Raleway', sans-serif;
	color:#ffffff;
	border:#e9e20f 1px solid;
	display:inline-block;
	margin-right: 30px;
	padding: 12px 20px 9px 20px;
	text-transform:uppercase;
	min-width: 261px;
	text-align: center;
}
.detail2 ul li img
{
	margin-right:7px;
}

.serbg
{
		background:url(../images/servbg.jpg) no-repeat;
	width:100%;
	background-position:top center;
	background-size:cover;
	clear:both;
	padding-top:70px;
	padding-bottom:70px;
}
.serhead h4
{
		font-size:29.97px;
	font-weight:700;
	font-family: 'Raleway', sans-serif;
	color:#313131;
	text-align:center;
	text-transform:uppercase;
}
.serhead p
{
		font-size:15.99px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	text-align:center;
	color:#313131;
}
.serhead h4:before
{
	content:"";
	height:2px;
	width:212px;
	background:#00b1f8;
	clear:both;
	display:block;
	margin:0px auto 39px;
}
.sertext h6
{
		font-size:16px;
	font-weight:600;
	font-family: 'Raleway', sans-serif;
	color:#003d83;
	text-align:center;
	text-transform:uppercase;
	padding-top:40px;
}
.sertext img
{
	margin:0 auto;
	display:block;
}
.sertext p
{
			font-size:14px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	color:#777777;
	text-align:center;
	padding-top:30px;
}
.sertext
{
	margin-top:70px;
}
.bestspeaker
{
	background:#fafafa;
	width:100%;
	clear:both;
	padding-top:60px;
	padding-bottom:84px;
}
.countname
{
		width:100%;}
.countname ul
{
	list-style:none;
	padding:0;
	display:inline;
	    float: left;
		margin-top:50px;
		margin-bottom:50px;
		    margin-left: 24px;


}
.countname ul li
{
	display:inline-block;
}
.countname ul li img
{
	float:left;
	margin-right:15px;
}
.countname ul li p
{
			font-size:16px;
	font-weight:700;
	font-family: 'Raleway', sans-serif;
	color:#003d83;
	text-transform:uppercase;
	float:left;
	padding-top: 11px;
	    padding-right: 30px;
}

.hovereffect {
  width: 100%;
  height: 100%;
  float: left;
  overflow: hidden;
  position: relative;
  text-align: center;
  cursor: pointer;
}

.hovereffect .overlay {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
  left: 0;
  background-color:rgba(3, 37, 72, 0.8);
  top: -200px;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.1s ease-out 0.5s;
  transition: all 0.1s ease-out 0.5s;
  border-top:#018ac8 6px solid;
}

.hovereffect:hover .overlay {
  opacity: 1;
  filter: alpha(opacity=100);
  top: 0px;
  -webkit-transition-delay: 0.1s;
  transition-delay: 0.1s;
}

.hovereffect img {
  display: block;
  position: relative;
  width:100%;
height: 222px;
}

.hovereffect h2:before
{
	content:"";
	width:75px;
	height:1px;
	background:#00b1f8;
	margin:16px auto;
	display:block;
}
.hovereffect h2 {
  text-transform: uppercase;
  color: #fff;
  text-align: center;
  position: relative;
  font-size: 17px;
  padding: 10px;
margin-top: 0;
  -webkit-transform: translateY(-200px);
  -ms-transform: translateY(-200px);
  transform: translateY(-200px);
  -webkit-transition: all ease-in-out 0.1s;
  transition: all ease-in-out 0.1s;
  -webkit-transition-delay: 0.3s;
  transition-delay: 0.3s;
  		font-size:18px;
	font-weight:700;
	font-family: 'Raleway', sans-serif;
	margin-bottom:0;
}

.hovereffect:hover h2 {
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition-delay: 0.3s;
  transition-delay: 0.3s;
}


.padd
{
	padding-left:0;
	padding-right:0;
}
.hovereffect p
{
		font-size:15px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	color:#FFFFFF;
	text-align:center;
	  -webkit-transform: translateY(-200px);
  -ms-transform: translateY(-200px);
  transform: translateY(-200px);
  -webkit-transition: all ease-in-out 0.3s;
  transition: all ease-in-out 0.3s;
  -webkit-transition-delay: 0.2s;
  transition-delay: 0.2s;
  padding-top:5px;
  padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 10px;
	line-height: 18px;

}
.hovereffect:hover p {
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition-delay: 0.2s;
  transition-delay: 0.2s;
}

.contact
{
	background:url(../images/contact-bg.jpg) no-repeat;
	width:100%;
	clear:both;
	background-position:top center;
	background-size:cover;
	padding-top:60px;
	padding-bottom:60px;
}
.conthead h4:before
{
	content:"";
	width:212px;
	height:2px;
	background:#00b1f8;
	margin:10px auto 38px;
	display:block;
}
.conthead h4
{
		font-size:29.97px;
	font-weight:700;
	font-family: 'Raleway', sans-serif;
	color:#FFFFFF;
	text-align:center;
	text-transform:uppercase;
}
.conthead p
{
	font-size:15.99px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	text-align:center;
	color:#FFFFFF;
}
.contform
{
	margin-top:40px;
}
.contform input
{
	height:49px;
	background:rgba(255,255,255,0.8);
	font-size:14px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	border:none;
	border-radius:0;
	margin-bottom:20px;
	padding-left:20px;
}
.contform select
{
	height:49px;
	background:rgba(255,255,255,0.8);
	font-size:14px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	border:none;
	border-radius:0;
	margin-bottom:20px;
	padding-left:20px;
}
.contform textarea
{

	background:rgba(255,255,255,0.8);
	font-size:14px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	border:none;
	border-radius:0;
	margin-bottom:25px;
}

.contform input::-webkit-input-placeholder {
   color: #7e7e7e;
}

.contform input:-moz-placeholder { /* Firefox 18- */
   color: #7e7e7e;
}

.contform input::-moz-placeholder {  /* Firefox 19+ */
   color:#7e7e7e;
}

.contform input:-ms-input-placeholder {
   color: #7e7e7e;
}
.contform textarea::-webkit-input-placeholder {
   color: #7e7e7e;
}

.contform textarea:-moz-placeholder { /* Firefox 18- */
   color: #7e7e7e;
}

.contform textarea::-moz-placeholder {  /* Firefox 19+ */
   color:#7e7e7e;
}

.contform textarea:-ms-input-placeholder {
   color: #7e7e7e;
}


.btnsub{
	width:168px;
	height:47px;
	background:#254d86;
	color:#FFFFFF;
	border:none;
	border-radius:0;
	margin:0 auto;
	display:block;
		font-size:18px;
	font-weight:700;
	font-family: 'Raleway', sans-serif;
	text-transform:uppercase;
}
.maps
{
	padding:0;
}

.footer
{
	background:#002b5c;
	width:100%;
	clear:both;
	padding-top:25px;
	padding-bottom:0px;
}
.copyright p
{
		font-size:12px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	color:#ffffff;
    padding-top: 4px;

}
.socialicon
{
	display:inline;
	float:right;
	margin-bottom:10px;
}
.socialicon a
{
		display:inline-block;
  margin-right: 7px;
}

.socialicon p i
{
	border:#FFFFFF 1px solid;
	border-radius:50%;
	display:inline-block;
	width:29px;
	height:29px;
	color:#FFFFFF;
	padding-top:6px;
	text-align:center;
	    margin-right: 9px;
}
.socialicon p i:hover
{
	background:#00b1f8;
}
label.error { display: none!important; }
.error{ border: 1px solid #F00 !important; }

.is-sticky #stk{
	width:100%;
	z-index:99999;
	border-bottom:#ccc 1px solid;
}
.is-sticky .navbar-brand img
{
	width:73%;
	    margin-top: -11px;
}
.is-sticky .menu li {
    padding-top: 25px;
    padding-bottom: 32px;
}
.is-sticky .getfree
{
	top:21px;
}
.is-sticky .navbar-brand {
    margin-right: 50px;
}

.side-bars { width: 210px; height: 51px; position: fixed; top: 50%; z-index: 9999; margin-top: -230px; right: -160px; margin-right: 0; display: none; }
.side-bars .s-bar { display: block; margin-bottom: 10px; position: relative; right: 0px; cursor: pointer; }
.side-bars .s-bar .clicktocall { font-size: 18px; padding: 0px; color: #fff; float: left; height: 64px; margin-top: -5px; font-weight: 700; background: #000000; width: 290px; border-top-left-radius: 5px; border-bottom-left-radius: 5px; }
.side-bars .s-bar .clicktocall a { color: #ffffff; font-weight: 400; font-size: 16px; font-family: 'Raleway'; }
.side-bars .s-bar .clicktocall a:hover{text-decoration:none;}
.call-spr { background: url(../images/mbl.png) no-repeat; width: 51px; height: 67px; position: relative; left: 0px; float: left; margin-right: 15px;
top: -1px; }
.side-bars .s-bar .clicktocall p { color: #ffffff; font-size: 16px; padding: 9px 0 0 0; margin: 5px 0px 0px 0px; line-height: 12px; background: #000000; display: inline-block; display: block; font-family: 'Raleway'; font-weight: 600;     text-transform: uppercase; }
.side-bars .s-bar .clicktocall .pnumlive { font-size: 24px; line-height: 24px; }
.side-bars .s-bar .chatwrap { background: #000000; border-top-left-radius: 5px; width: 280px; border-bottom-left-radius: 5px; overflow: hidden; color: #ffffff; height: 63px; }
.side-bars .s-bar .chatwrap a { font-size: 24px; line-height: 24px; color: #ffffff; font-weight: bold; }
.chat-spr { background: url(../images/chat1.png) no-repeat; width: 58px; height: 64px; position: relative; left: 0px; float: left; margin-right: 15px; }
.side-bars .s-bar .chatwrap .pnumlive {     display: block;
    padding-top: 19px;
    padding-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    font-family: 'Raleway';
    text-decoration: none;
    text-transform: uppercase; }
 a:hover
{
	text-decoration:none;
}
.floating-form-wrap .form-handle { background: url(../images/shedule.png) no-repeat; width: 57px; height: 316px; float: left; cursor: pointer;    margin-left: -1px;    margin-top: -1px; }
.floating-form-wrap {  position: fixed; width: 500px; height: 315px; right: 0; margin-top: -110px; z-index: 999; background: #000000; margin-right: -451px; top: 53%; display: none;    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px !important; }
.floating-form { width: 435px; float: right; }
.signupBox { width: 405px; }
.signupBox input.form-control { border-radius: 0px; height: 34px;font-family: 'Raleway';  margin-bottom:-3px; width:99%; }
.signupBox .form-group.col-md-6, .signupBox .form-group.col-md-12 { padding: 0px 5px; }
.signupBox select.form-control { border-radius: 0px; height: 34px; }
.signupBox select.form-control.error { border: 1px solid red; }
.signupBox .cust { height: 40px; text-transform: capitalize; font-size: 17px; font-family: 'Raleway'; font-weight: 300;margin-top: 15px; }
.signupBox .col-md-12 { padding: 0px 0px 0px; }
.signupBox {     padding: 12px 10px;
    margin-left: 10px; }p.apply-custom { position: relative; padding: 0.5% 12%; font-family: 'Raleway'; font-weight: 600; font-size: 25px; color: #ffffff; background-color: #1d83c7; border-radius: 0px; display: inline-block; }

.opensideform { cursor: pointer; }
.cus-overlay { width: 100%; height: 100%; position: fixed; background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVQImWNguAkAANwA2ohRxm8AAAAASUVORK5CYII=) repeat; top: 0; left: 0; z-index: 998; display: none; }
.apply-custom-1 { font-size: 17px; color: #ffffff; text-align: center; width: 88%; }

.pad{
	padding-right:0;
}
.contform1
{
	margin-top:11px;
	margin-left: -7px;
}
.contform1 input
{
	height:40px;
	background:rgba(255,255,255,0.8);
	font-size:14px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	border:none;
	border-radius:0;
	margin-bottom:0px;
	padding-left:20px;
}
.contform1 select
{
	height:40px;
	background:rgba(255,255,255,0.8);
	font-size:14px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	border:none;
	border-radius:0;
	margin-bottom:15px;
	padding-left:20px;
}
.contform1 textarea
{

	background:rgba(255,255,255,0.8);
	font-size:14px;
	font-weight:400;
	font-family: 'Raleway', sans-serif;
	border:none;
	border-radius:0;
	margin-bottom:10px;
}
.sch{
	cursor:pointer;
}
.contform1 input::-webkit-input-placeholder {
   color: #7e7e7e;
}

.contform1 input:-moz-placeholder { /* Firefox 18- */
   color: #7e7e7e;
}

.contform1 input::-moz-placeholder {  /* Firefox 19+ */
   color:#7e7e7e;
}

.contform1 input:-ms-input-placeholder {
   color: #7e7e7e;
}
.contform1 textarea::-webkit-input-placeholder {
   color: #7e7e7e;
}

.contform1 textarea:-moz-placeholder { /* Firefox 18- */
   color: #7e7e7e;
}

.contform1 textarea::-moz-placeholder {  /* Firefox 19+ */
   color:#7e7e7e;
}

.contform1 textarea:-ms-input-placeholder {
   color: #7e7e7e;
}


#btnsub1{
	width:168px;
	height:47px;
	background:#254d86;
	color:#FFFFFF;
	border:none;
	border-radius:0;
	    margin-left: 14px;
	display:block;
		font-size:18px;
	font-weight:700;
	font-family: 'Raleway', sans-serif;
	text-transform:uppercase;
}

/******************************************************Responsive*********************************************************/



@media (min-width:1200px){
}

@media (min-width:980px) and (max-width:1030px){
	.pack {
    float: right;
    width: 86%;
}
.carousel-caption {
    top: 14%;
    bottom: 0;
}
.bootombtn {

    bottom: 34px;

}
.carousel-caption h3 {

    font-size: 41.33px;
}
.carousel-caption h3 span {
    font-size: 35.73px;
}
.navbar-brand {
    margin-right: 0;
}
.getfree {

    font-size: 10px;
    color: #ffffff;
    padding: 9px 5px;

}
.is-sticky .navbar-brand {
    margin-right: 0px;
}
.bx-wrapper .bx-prev {
    left: -29px;
}
.bx-wrapper .bx-next {
    right: -18px;
}
.details ul li {



    display: inline-block;
    margin-right: 8px;
    padding: 8px 11px;
    margin-bottom: 16px;
    margin-top: 10px;
}

.details ul {

    margin-left: 0;

}
.detail2 ul li {



    display: inline-block;
    margin-right: 8px;
    padding: 8px 26px;
    margin-bottom: 16px;
    margin-top: 10px;
}
.detail2 ul {

    margin-left: 0;

}
.detailtext h6 {

    font-size: 22px;
}
.countname ul li p {

    padding-right: 2px;
}
.hovereffect img {

    width: 100%;
    height: auto;

}
.contform input {

    font-size: 12px;
}
}

@media (min-width:768px) and (max-width:979px){
	.pack {
    float: right;
    width: 96%;
}
.phone p {

    padding-right: 16px;
}
.email p {

    padding-right: 16px;
}
.carousel-caption h3 {

    font-size: 31.33px;
}
.is-sticky .navbar-brand {
    margin-right: -57px;
}
.carousel-caption h3 span {
    font-size: 25.73px;
}
.carousel-caption {
    top: 19%;
}
.navbar-brand
{
	margin-right:0;
}
.navbar-brand img
{
	    text-align: center;
    display: block;
    margin-left: 254px;
    margin-top: -10px;

}
.carousel-caption p {

    font-size: 14.09px;
}
.bootombtn {

    bottom: 31px;
}
.menu li {
    padding-top: 57px;
    padding-bottom: 50px;
    padding-left: 0;
    margin-left: 16px;
}
.getfree {

    top: 143px;
    left: -203px;
}
.bx-wrapper .bx-prev {
    left: -25px;
}
.bx-wrapper .bx-next {
    right: 4px;
}
.imgtext img {

    width: 100%;
    height: auto;
    margin-left: -12px;
}
.is-sticky .navbar-brand img {
    width: 30%;
    margin-top: -11px;
    margin-left: 291px;
}
.is-sticky .getfree {
    top: 21px;
    left: 20px;
}
.imgtext p {

    padding-bottom: 24px;
}
.facttext {


    margin-left: -11px;
    width: 100%;
}
.detailtext h6 {

    font-size: 22px;
}
.hovereffect img {

    width: 100%;
    height: 264px;
}
.countname ul {

    margin-top: 50px;
    margin-bottom: 35px;
    margin-left: 38px;
}
.socialicon {

    margin-bottom: 10px;
    margin-top: -31px;
    margin-right: 9px;
}
}

@media (max-width:767px) {

.pack {
    float: none;
    width: 66%;
    text-align: center;
    margin: 0 auto;
    display: block;
}
.phone p {

    padding-right: 6px;
	font-size:13px;
}
.email p {

    padding-right: 6px;
}
.carousel-caption h3 {

    font-size: 31.33px;
}
.carousel-caption h3 span {
    font-size: 25.73px;
}
.carousel-caption {
    top: 24%;
}
.carousel-caption p {

    font-size: 15.09px;
}
.bootombtn img
{
	display:none;
}
.navbar-brand img
{
	    width: 73%;
    margin-top: -11px;
}
.phone img {
    float: left;
    margin-right: 4px;
}
.email img {
    float: left;
    margin-right: 4px;
}
.bootombtn {

    bottom: 13px;
}
.navbar-brand {
    margin-right: 0;
    display: block;
    height: auto;
	margin-bottom: -18px;
}
.navbar-toggle {

    margin-top:21px;
}
.navbar-default {

    margin-bottom: 0;

    z-index: 99999;
}
.menu li {
       padding-top: 12px;
    padding-bottom: 7px;
}
ul.nav.navbar-nav {
    margin-left: 10px;
	    margin-bottom: 33px;
}
.navbar-collapse.in {
    overflow-y:hidden;
}
.menu li a:after
{
	display:none;
}
.menu li a
{
	border-bottom:1px #002b5c solid;
}
.facthead h3 {

    font-size: 24px;
}
.getfree {

    top: -18px;
}
.is-sticky .menu li {
    padding-top: 12px;
    padding-bottom: 7px;
}
.is-sticky .getfree {
    top: -18px;
}
.is-sticky .navbar-toggle {
    margin-top: 19px;
}
.bx-wrapper .bx-prev {
    left: -27px;
}
.bx-wrapper .bx-next {
    right: -15px;
}
.imgtext img {
    margin-bottom: 20px;
    width: 100%;
    height: auto;
}
.imgtext p {

    padding-bottom: 30px;
}
.details ul li {

    margin-right: 17px;
}
.details ul {

    margin-left: 0;
}
.detail2 ul li {

    margin-right: 17px;
}
 .detail2 ul {

    margin-left: 0;
}
.serhead h4 {
    font-size: 21.97px;
}
.detailtext h6 {

    font-size: 19px;
}
.detailtext
{
	margin-left:0px;
}
.countname ul {

    margin-left: 26px;
}
.countname ul li {
    display: inline-block;
    width: 48%;
}

.hovereffect .overlay {
    top: 0px;
}
.hovereffect h2{
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    transform: translateY(0px);
    -webkit-transition-delay: 0.3s;
    transition-delay: 0.3s;
}
.hovereffect p {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    transform: translateY(0px);
    -webkit-transition-delay: 0.2s;
    transition-delay: 0.2s;
}
.conthead h4 {
    font-size: 25.97px;
}
.hovereffect img {

    width: 100%;
    height: auto;
}
.hovereffect h2 {

    margin-bottom: 0;
    margin-top: 100px;
}
.copyright p {

    text-align: center;
}
.socialicon {

    float: none;
    margin-bottom: 10px;
    margin: 0 auto;
    display: block;
    text-align: center;
}
}
@media (max-width:480px) {
.hovereffect {
    margin-bottom: 20px;
}
.hovereffect h2 {
    margin-top: 20px;
}
	.pack {
    float: left;
    width: 100%;
    margin-left: 0px;
    padding: 10px 10px 0px;
}
.carousel-caption h3 {
    font-size: 20px;
	margin-bottom: 0;
}
.carousel-caption h3 span {
    font-size: 14px;
}
.topdata {
    text-align: left;
    top: 0px;
}
.phone p, .email p, .chat p {
	display: none;
}
 .footer {
	 padding-bottom: 35px;
 }
.carousel-caption p {
    font-family: 'Raleway', sans-serif;
    font-weight: 400;
    font-size: 11px;
    margin-bottom: 15px;
}
.carousel-caption {
    top: 0px;
}
.num {

    font-size: 10px;

    padding: 10px 11px;

    margin-top: 40px;
    margin-right: 10px;
}
.carousel-caption {
    right: 10%;
	left: 10%;
}
.live {


    font-size: 10px;


    padding: 10px 29px;

    margin-top: 43px;
}
.facthead h3 {
    font-size: 20px;
}
.detailtext h6 {
    font-size: 18px;
    text-align: center;
    margin-bottom: 34px;
}
.details
{
	    margin-left: 0px;
}
.details ul li
{

	display:block !important;
	text-align:center;
	margin:0 auto 24px;
}
.detail2
{
	    margin-left: 0px;

}
.countname ul li {
    display: block;
    width: 100%;
}
.countname ul {
    margin-left: 26px;
    display: block;
    float: none;
    margin-top: 30px;
    margin-bottom: 30px;
}
.detail2 ul li
{

	display:block;
	text-align:center;
	margin:0 auto 24px;
}
.heading h4 {

    font-size: 24px;
}
.serhead h4 {
    font-size: 17.97px;
}
.conthead h4 {
    font-size: 17.97px;
}
.countname ul li p {

    padding-right: 12px;
	float: none;
}
.pack div {
    display: inline-block;
}
}

@media (max-width:320px) {
	.pack div {
    display: inline-block;
}
.carousel-caption {
    top: 9%;
}
.carousel-caption h3 {
    font-size: 18px;
}
.carousel-caption h3 span {
    font-size: 12px;
}
a.num {
    display: none;
}
a.live {
    display: none;
}
.bootombtn img {
    margin: 0 auto;
    display: none;
}
.is-sticky .navbar-brand img {
    width: 73%;
    margin-top: -77px;
}
.heading h4 {

    font-size: 23px;
}
.facthead h3 {
    font-size: 19px;
}
.detailtext h6 {
    font-size: 15px;
}
.hovereffect h2 {
    margin-bottom: 0;
    margin-top: 0;
}
.socialicon p i {

    margin-right: 7px;
}
.details ul li, .detail2 ul li  {
    margin: 0 auto 15px;
	text-align: center;
	font-size: 13px;
}
.bestspeaker .padd .hovereffect {
    margin-bottom: 15px;
}
.countname ul {
    display: block;
    float: none;
    margin-top: 30px;
    margin-bottom: 30px;
}
.countname ul li p {
    float: none;
}
.countname ul li {
    display: block;
    width: 100%;
}
.hovereffect p {
    font-size: 13px;
}
.heading h4 {
    font-size: 18px;
}
.heading p {
    font-size: 14px;
}
.sertext p {
    padding-top: 0px;
}
.imgtext p {
    padding-bottom: 0px;
}
#philosophy {
    margin-top: 40px;
}
}
@media only screen
and (min-device-width : 320px)
and (max-device-width : 568px)
and (orientation : landscape) {
	.pack div {
    display: inline-block;
}
.num, .live {
    display: none;
}
.bxsli {
    margin-top: 35px;
}
.heading h4 {
    font-size: 22px;
}
.imgtext p {
    padding-bottom: 0px;
    padding-top: 0px;
}
#philosophy {
    margin-top: 10px;
}
.imgtext {
    margin-top: 50px;
    width: 70%;
    margin: 50px auto;
}
.heading p {
    font-size: 14px;
}
.facthead h3 {
    font-size: 20px;
}
.facthead h3 {
    font-size: 20px;
}
.detailtext h6 {
    text-align: center;
}
.details ul li {
    margin-right: 0px;
	margin-bottom: 15px;
    margin-top: 15px;
}
.details ul {
    margin-left: 0px;
    display: table;
    margin: 0 auto;
    float: none;
    width: 261px;
}
.detail2 ul li {
    margin-right: 0px;
    margin-bottom: 15px;
}
.detail2 ul {
    margin-left: 0;
    display: block;
    width: 261px;
    margin: 0 auto;
}
.details ul li:first-child {
    margin-bottom: 0px;
}
.detail2 ul li {
    font-size: 13px;
}
.sertext p {
    padding-top: 0px;
}
.bestspeaker .padd {
    width: 75%;
    margin: 10px auto;
    float: none;
}
.bestspeaker .padd .hovereffect {
    margin-bottom: 15px;
}
.conthead h4 {
    font-size: 22px;
}
li.change.ch2 {
    margin-bottom: 15px!important;
}
.carousel-caption h3 span {
    font-size: 20px;
}
}
