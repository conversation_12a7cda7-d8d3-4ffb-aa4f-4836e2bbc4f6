<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\PortfolioResource;
use App\Models\Portfolio;
use Illuminate\Http\Request;

class ProposalPortfolioController extends Controller
{
    public function index(Request $request)
    {

      //Search for portfolios take 8 from the database
      if($request->has('query')){
        return PortfolioResource::collection(Portfolio::search($request->input('query'))->take(8)->get());
      }

      return PortfolioResource::collection(Portfolio::take(8)->get());
    }



    //Update the status of the portfolio

    public function updateStatus(Request $request, Portfolio $portfolio): PortfolioResource
    {
      $portfolio->update([
        'status' => $request->input('status')
      ]);

      return PortfolioResource::make($portfolio->refresh());
    }


    //confirm speaker on the proposal
  public function confirmPortfolio(Request $request, Portfolio $portfolio): PortfolioResource
  {
    $portfolio->update([
      'confirmed' => $request->input('confirmed')
    ]);

    //send notifications to the confirmed speaker


    //send notifications to the proposal owner


    //send notification to unconfirmed speakers



    return PortfolioResource::make($portfolio->refresh());
  }
}
