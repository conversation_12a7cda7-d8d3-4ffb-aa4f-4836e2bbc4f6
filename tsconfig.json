{"compilerOptions": {"allowJs": true, "module": "ESNext", "moduleResolution": "bundler", "jsx": "react-jsx", "strict": true, "isolatedModules": true, "target": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "paths": {"@/*": ["./resources/js/*"], "ziggy-js": ["./vendor/tightenco/ziggy"]}}, "include": ["resources/js/**/*.ts", "resources/js/**/*.tsx", "resources/js/**/*.d.ts", "node_modules/laravel-echo/typings/index.d.ts", "node_modules/laravel-echo/typings/index.d.ts"]}