<!DOCTYPE html>
<html lang="en-US" data-menu="leftalign">
<head>

  <title>Terms & Conditions - MENA SPEAKERS</title>

  <meta name="robots" content="noindex,nofollow" />
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
  <meta name="format-detection" content="telephone=no" />

  <link rel="stylesheet" href="{{ asset('js/plugins/modal-for-elementor/css/bootstrap.css') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/modal-for-elementor/css/popup.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/reset.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/wordpress.css') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/style.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/modulobox.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/leftalignmenu.css') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/font-awesome.min.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/themify-icons.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/tooltipster.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/demo.css') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/loftloader/assets/css/loftloader.min.css?ver=2020072001') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/elementor/assets/lib/eicons/css/elementor-icons.min.css?ver=5.7.0 ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/elementor/assets/lib/animations/animations.min.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/elementor/assets/css/frontend.min.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/swiper.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/animatedheadline.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/flickity.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/justifiedGallery.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/owl.theme.default.min.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/switchery.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/dotlife-elementor.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/dotlife-elementor-responsive.css ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/elementor/assets/lib/font-awesome/css/font-awesome.min.css?ver=4.7.0 ') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/responsive.css') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('js/plugins/dotlife-elementor/assets/css/typedjs.min.css') }}" type="text/css" media="all" />
  <link rel="stylesheet" href="{{ asset('css/custom.css') }}" type="text/css" media="all" />

  <link rel="icon" href="upload/TG-Thumb.png" sizes="32x32" />
  <link rel="icon" href="upload/TG-Thumb.png" sizes="192x192" />
  <link rel="apple-touch-icon" href="upload/TG-Thumb.png" />
  <meta name="msapplication-TileImage" content="upload/TG-Thumb.png" />


</head>

<body data-rsssl="1"  class="home page-template-default page page-id-1737 woocommerce-no-js tg_menu_transparent tg_lightbox_black leftalign tg_footer_reveal loftloader-lite-enabled elementor-default elementor-kit-3076 elementor-page elementor-page-1737" >

<div id="perspective" style="">
  <input type="hidden" id="pp_menu_layout" name="pp_menu_layout" value="leftalign" />
  <input type="hidden" id="pp_enable_right_click" name="pp_enable_right_click" value="" />
  <input type="hidden" id="pp_enable_dragging" name="pp_enable_dragging" value="" />
  <input type="hidden" id="pp_image_path" name="pp_image_path" value="images/" />
  <input type="hidden" id="pp_homepage_url" name="pp_homepage_url" value="/" />
  <input type="hidden" id="pp_fixed_menu" name="pp_fixed_menu" value="1" />
  <input type="hidden" id="tg_sidebar_sticky" name="tg_sidebar_sticky" value="1" />
  <input type="hidden" id="tg_footer_reveal" name="tg_footer_reveal" value="1" />
  <input type="hidden" id="tg_header_content" name="tg_header_content" value="content" />
  <input type="hidden" id="pp_topbar" name="pp_topbar" value="" />
  <input type="hidden" id="post_client_column" name="post_client_column" value="4" />
  <input type="hidden" id="pp_back" name="pp_back" value="Back" />
  <input type="hidden" id="tg_lightbox_thumbnails" name="tg_lightbox_thumbnails" value="thumbnail" />
  <input type="hidden" id="tg_lightbox_thumbnails_display" name="tg_lightbox_thumbnails_display" value="1" />
  <input type="hidden" id="tg_lightbox_timer" name="tg_lightbox_timer" value="7000" />


  <input type="hidden" id="tg_live_builder" name="tg_live_builder" value="0" />

  <input type="hidden" id="pp_footer_style" name="pp_footer_style" value="3" />

  <!-- Begin mobile menu -->
  <x-mobile-menu />
  <!-- End mobile menu -->
  <!-- Begin template wrapper -->
  <div id="wrapper" class="" style="background-color: #f9f9f9;">

    <x-head-section />

    <!-- Begin content -->
    <div id="page_content_wrapper" class="">
      <div class="inner">
        <!-- Begin main content -->
        <div class="inner_wrapper">
          <div class="sidebar_content full_width">


            <div data-elementor-type="wp-post" data-elementor-id="1644" class="elementor elementor-1644">
              <div class="elementor-inner">
                <div class="elementor-section-wrap">
                  <section class="elementor-section elementor-top-section elementor-element elementor-element-371f533 elementor-section-stretched elementor-section-full_width elementor-section-height-default elementor-section-height-default" data-id="371f533" data-element_type="section" data-settings="{&quot;stretch_section&quot;:&quot;section-stretched&quot;}" style="width: 1519px; left: -137.1px;">
                    <div class="elementor-container elementor-column-gap-default">
                      <div class="elementor-row">
                        <div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-688b491" data-id="688b491" data-element_type="column">
                          <div class="elementor-column-wrap elementor-element-populated">
                            <div class="elementor-widget-wrap">
                              <div class="elementor-element elementor-element-377342d elementor-widget elementor-widget-dotlife-mouse-driven-vertical-carousel" data-id="377342d" data-element_type="widget" data-widget_type="dotlife-mouse-driven-vertical-carousel.default">
                                <div class="elementor-widget-container">
                                  <div class="tg_mouse_driven_vertical_carousel_wrapper">
                                    <header class="c-header c-header--archive c-header--project-list">
                                      <div class="c-mouse-vertical-carousel js-carousel u-media-wrapper">

                                        <div class="carousel__header">
                                          <div class="carousel__sub_header"> </div>
                                          <h2>Terms and conditions</h2>
                                        </div>

                                        <ul class="c-mouse-vertical-carousel__list js-carousel-list" style="transform: matrix(1, 0, 0, 1, 0, -187.667);">
                                          <li class="c-mouse-vertical-carousel__list-item js-carousel-list-item" data-item-id="0">
                                            <a href="">
                                              <p class="c-mouse-vertical-carousel__eyebrow">
                                              </p>
                                              <p class="c-mouse-vertical-carousel__title">
                                              </p>
                                            </a>
                                          </li>
                                        </ul>
                                        <i class="c-mouse-vertical-carousel__bg-img js-carousel-bg-img" style="background-image: url(&quot;https://mena-speakers.com/wp-content/uploads/2020/04/i405L3GqkB.jpg&quot;); visibility: inherit; opacity: 1; transform: matrix(1, 0, 0, 1, 0, 0);"></i>
                                        <i class="c-gradient-overlay"></i>
                                      </div>
                                    </header>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </section>
                  <section class="elementor-section elementor-top-section elementor-element elementor-element-241b67b elementor-section-stretched elementor-section-boxed elementor-section-height-default elementor-section-height-default" data-id="241b67b" data-element_type="section" data-settings="{&quot;stretch_section&quot;:&quot;section-stretched&quot;}" style="width: 1519px; left: -137.1px;">
                    <div class="elementor-container elementor-column-gap-default">
                      <div class="elementor-row">
                        <div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-d4a95cd" data-id="d4a95cd" data-element_type="column">
                          <div class="elementor-column-wrap elementor-element-populated">
                            <div class="elementor-widget-wrap">
                              <div class="elementor-element elementor-element-d398a44 animated-fast elementor-widget elementor-widget-heading animated fadeInUp" data-id="d398a44" data-element_type="widget" data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:0}" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                  <span class="elementor-heading-title elementor-size-default">Terms and conditions</span>		</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </section>
                  <section class="elementor-section elementor-top-section elementor-element elementor-element-5d30e10 elementor-section-stretched elementor-section-boxed elementor-section-height-default elementor-section-height-default" data-id="5d30e10" data-element_type="section" data-settings="{&quot;stretch_section&quot;:&quot;section-stretched&quot;}" style="width: 1519px; left: -137.1px;">
                    <div class="elementor-container elementor-column-gap-default">
                      <div class="elementor-row">
                        <div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-42c09b6" data-id="42c09b6" data-element_type="column">
                          <div class="elementor-column-wrap elementor-element-populated">
                            <div class="elementor-widget-wrap">
                              <section class="elementor-section elementor-inner-section elementor-element elementor-element-464b98e elementor-section-boxed elementor-section-height-default elementor-section-height-default" data-id="464b98e" data-element_type="section">
                                <div class="elementor-container elementor-column-gap-default">
                                  <div class="elementor-row">
                                    <div class="elementor-column elementor-col-100 elementor-inner-column elementor-element elementor-element-fcf3d16" data-id="fcf3d16" data-element_type="column">
                                      <div class="elementor-column-wrap elementor-element-populated">
                                        <div class="elementor-widget-wrap">
                                          <div class="elementor-element elementor-element-1a4bfe3 animated-fast elementor-widget elementor-widget-text-editor animated fadeInUp" data-id="1a4bfe3" data-element_type="widget" data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:400}" data-widget_type="text-editor.default">
                                            <div class="elementor-widget-container">
                                              <div class="elementor-text-editor elementor-clearfix">
                                                <p>MENA Speakers maintains the https://mena-speakers.com Website ("Site"). The use of this website (www.mena-speakers.com) is subject to the conditions of use described hereunder.
                                                  The mere use of the website implies the knowledge and the acceptance of these conditions of use and the privacy policy.</p>
                                                <p><strong>Copyrights</strong>
                                                  <br>Copyright MENA SPEAKERS FZE. All Rights Reserved. The text, images, graphics, sound files, animation files, video files and their arrangement on the
                                                  MENA SPEAKERS FZE website are all subject to Copyright and other intellectual property protection.
                                                  These objects may not be copied for commercial use or distribution, nor may these objects be modified or reposted to other websites.
                                                  Some of the webpages of MENA SPEAKERS FZE may also contain material that is subject to the copyright rights of third parties and their providers.</p>

                                                <p><strong>Information</strong><br>The information available on or via the website is of a general nature and is only provided for a general use.
                                                  This information is not adapted to personal or specific circumstances. No rights can be derived from the information.</p>
                                                <p>Despite the fact that best efforts were made to draw up the content of the website with due care, it cannot be excluded that information is obsolete, incomplete or in any other way incorrect.
                                                  MENA SPEAKERS cannot give any guarantees concerning the nature or the content of the information available on or via the website.</p>
                                                <p>"Visa and Master Card and which AED will be accepted for payment"</p>
                                                <p>‘’Cardholder must retain a copy of transaction records and Merchant policies and rules’’</p>
                                                <p>‘’User is responsible for maintaining the confidentiality of his account’’</p>

                                                <p>Should you notice errors in the information available on or via the website, please contact <NAME_EMAIL>.</p>
                                                <p><strong>No Licenses</strong><br>Your access to neither this Internet website, nor any material contained therein shall in any way grant or be taken to grant any person a license to the
                                                  Intellectual Property of MENA SPEAKERS.
                                                  <br>No intellectual property rights regarding the website itself, part of the website or information available on or via the website are transferred to you.
                                                  <br>The information that is made available to you on or via the website, can only be used for your own internal purposes. You shall not use the information or
                                                  databases for any other purpose, especially for commercial exploitation.</p>
                                                <p><strong>Interruptions</strong><br>MENA SPEAKERS makes every effort to prevent, as much as possible, interruptions due to technical errors. Nevertheless,
                                                  MENA SPEAKERS cannot guarantee that the website will be completely free of any kind of interruption or will not be subject to other technical problems.</p>
                                                <p><strong>Links and references</strong><br>The website can contain links to other websites. MENA SPEAKERS does not monitor those websites and has no technical or content
                                                  control on those websites. Therefore, MENA SPEAKERS cannot guarantee the completeness or the accuracy of the content, nor the availability of those websites.</p>
                                                <p>The incorporation of links to other websites does not imply any connection, partnership, relationship or approval of those websites or their content.</p>
                                                <p>Should you desire to create links from your website to this website, please contact <NAME_EMAIL> beforehand.</p>
                                                <p><strong>Liability</strong><br>MENA SPEAKERS shall not be liable for any damages arising or resulting from any direct or indirect use of the website or of the
                                                  information made available on or via the website.
                                                  MENA SPEAKERS is not liable for damages resulting from potential interruptions of the website, caused by technical errors, even if the error is qualified as a
                                                  “serious fault” under United Arab Emirates law,
                                                  “United Arab Emirates is our country of domicile” and stipulate that the governing law is the local law.
                                                  viruses or any other disturbing element, or the consultation or the use of the websites (e.g. via links) referred to on or via the website or the information available on the former.</p>
                                                <p>MENA SPEAKERS is not liable for electronic communications via the website, e.g. e-mails, or delays, interceptions or manipulation by third parties of those communications.</p>
                                                <p>Any use of the information available on or via the website is therefore completely at your own risk. Consequently, you are liable for any of your choices made based on the information available on or via the website.</p><p>You shall take all reasonable precautions to prevent your equipment from being harmed by viruses, bugs, Trojan horses, etc. MENA SPEAKERS does not guarantee any compatibility of your equipment with the files being part of or present on the website, or any accessibility of those elements.</p>
                                                <p>‘’We will not trade with or provide any services to OFAC and sanctioned countries’’</p>
                                                <p>
                                                  ‘’Customer using the website who are Minor /under the age of 18 shall not register as a User of the website and shall not transact on or use the website’’
                                                </p>
                                                <p><strong>Applicable law and jurisdiction</strong>
                                                  <br>This website and its use are subject to the United Arab Emirates law and regulations. Should any of the provision be considered void under the United Arab Emirates law and regulations or any mandatory regulations of another country that would apply the other provisions will remain in full force and effect.</p><p>Last updated: October&nbsp;2022</p>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </section>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </section>
                </div>
              </div>
            </div>
            <div class="comment_disable_clearer"></div>
          </div>
        </div>
        <!-- End main content -->
      </div>
      <br class="clear" />
    </div>



  </div>

  <x-footer />

</div>


<script type="text/javascript" src="{{ asset('js/jquery.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/jquery-migrate.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/learnpress/assets/js/vendor/plugins.all.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/underscore.min.js')}}"></script>
<script type="text/javascript">
  /* <![CDATA[ */
  var userSettings = { url: "//dotlife//demo//", uid: "0", time: "1614677679", secure: "1" };
  /* ]]> */
</script>
<script type="text/javascript" src="{{ asset('js/plugins/utils.min.js')}}"></script>
<script type="text/javascript">
  /* <![CDATA[ */
  var lpGlobalSettings = {
    url: null,
    siteurl: "/",
    ajax: "#",
    theme: "dotlife",
    localize: { button_ok: "OK", button_cancel: "Cancel", button_yes: "Yes", button_no: "No" },
  };
  /* ]]> */
</script>
<script type="text/javascript" src="{{ asset('js/plugins/learnpress/assets/js/global.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/learnpress/assets/js/utils.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/learnpress/assets/js/frontend/learnpress.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/learnpress/assets/js/frontend/course.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/learnpress/assets/js/frontend/become-teacher.js')}}"></script>

<script>
  (function () {
    function maybePrefixUrlField() {
      if (this.value.trim() !== "" && this.value.indexOf("http") !== 0) {
        this.value = "http://" + this.value;
      }
    }

    var urlFields = document.querySelectorAll('.mc4wp-form input[type="url"]');
    if (urlFields) {
      for (var j = 0; j < urlFields.length; j++) {
        urlFields[j].addEventListener("blur", maybePrefixUrlField);
      }
    }
  })();
</script>


<script type="text/javascript" src="{{ asset('js/plugins/imagesloaded.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/masonry.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/jquery.lazy.js')}}"></script>
<script type="text/javascript">
  jQuery(function ($) {
    jQuery("img.lazy").each(function () {
      var currentImg = jQuery(this);

      jQuery(this).Lazy({
        onFinishedAll: function () {
          currentImg.parent("div.post_img_hover").removeClass("lazy");
          currentImg.parent(".tg_gallery_lightbox").parent("div.gallery_grid_item").removeClass("lazy");
          currentImg.parent("div.gallery_grid_item").removeClass("lazy");
        },
      });
    });
  });
</script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/modulobox.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/jquery.parallax-scroll.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/jquery.smoove.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/parallax.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/jquery.blast.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/jquery.visible.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/jarallax.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/modal-for-elementor/js/jquery.cookie.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/modal-for-elementor/js/bootstrap.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/modal-for-elementor/js/popup.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/ui/core.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/ui/effect.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/waypoints.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/tilt.jquery.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/jquery.stellar.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/core/custom_plugins.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/core/custom.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/jquery.sticky-kit.min.js')}}"></script>
<script type="text/javascript">
  jQuery(function ($) {
    jQuery("#page_content_wrapper .sidebar_wrapper").stick_in_parent({ offset_top: 100 });

    if (jQuery(window).width() < 768 || is_touch_device()) {
      jQuery("#page_content_wrapper .sidebar_wrapper").trigger("sticky_kit:detach");
    }
  });
</script>
<script type="text/javascript" src="{{ asset('js/plugins/jquery.tooltipster.min.js')}}"></script>
<script type="text/javascript">
  jQuery(function ($) {
    jQuery(".demotip").tooltipster({
      position: "left",
    });
  });
</script>
<script type="text/javascript" src="{{ asset('js/plugins/loftloader/assets/js/loftloader.min.js?ver=2020072001')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/webfont.js')}}"></script>
<script type="text/javascript">
  /* <![CDATA[ */
  var tgAjax = { ajaxurl: "#", ajax_nonce: "941535503d" };
  /* ]]> */
</script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/dotlife-elementor.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/anime.min.js?ver=5.4.4')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/tweenmax.min.js?ver=5.4.4')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/elementor/assets/lib/swiper/swiper.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/elementor/assets/lib/jquery-numerator/jquery-numerator.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/dotlife-elementor/assets/js/typed.min.js')}}"></script>
<script type="text/javascript">
  jQuery(function ($) {
    jQuery(".type-wrap").show();
    jQuery("#typed").typed({
      stringsElement: jQuery("#typed-strings"),
      typeSpeed: 65,
      backDelay: 2500,
      loop: true,
      loopCount: Infinity,
      contentType: "html", // or text
      loopCount: true,
    });
  });
</script>
<script type="text/javascript" src="{{ asset('js/plugins/jquery.cookie.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/core/demo.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/elementor/assets/js/frontend-modules.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/ui/position.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/elementor/assets/lib/dialog/dialog.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/elementor/assets/lib/waypoints/waypoints.min.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/plugins/elementor/assets/lib/share-link/share-link.min.js')}}"></script>
<script type="text/javascript">
  var elementorFrontendConfig = {
    environmentMode: { edit: false, wpPreview: false },
    i18n: { shareOnFacebook: "Share on Facebook", shareOnTwitter: "Share on Twitter", pinIt: "Pin it", downloadImage: "Download image" },
    is_rtl: false,
    breakpoints: { xs: 0, sm: 480, md: 768, lg: 1025, xl: 1440, xxl: 1600 },
    version: "2.9.14",
    urls: { assets: "plugins//elementor//assets//" },
    settings: {
      page: [],
      general: {
        elementor_global_image_lightbox: "yes",
        elementor_lightbox_enable_counter: "yes",
        elementor_lightbox_enable_fullscreen: "yes",
        elementor_lightbox_enable_zoom: "yes",
        elementor_lightbox_enable_share: "yes",
        elementor_lightbox_title_src: "title",
        elementor_lightbox_description_src: "description",
      },
      editorPreferences: [],
    },
    post: { id: 1737, title: "DotLife%20%7C%20Online%20Courses%20for%20Life%20Coach%20%E2%80%93%20Just%20another%20WordPress%20site", excerpt: "", featuredImage: false },
  };
</script>
<script type="text/javascript" src="{{ asset('js/plugins/elementor/assets/js/frontend.min.js')}}"></script>
</body>
</html>
