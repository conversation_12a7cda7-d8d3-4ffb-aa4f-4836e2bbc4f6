<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewLeadNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $lead;
    /**
     * Create a new message instance.
     */
    public function __construct( $lead)
    {
        $this->lead = $lead;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Lead Notification',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.leads.new',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

  public function build()
  {
    return $this->subject('New Lead -' . ' - '.$this->lead->name)
      ->bcc('<EMAIL>')
      ->bcc('<EMAIL>')
      ->bcc('<EMAIL>')
      ->bcc('<EMAIL>')
      ->bcc('<EMAIL>')
      ->bcc('<EMAIL>')
      ->bcc('<EMAIL>')
      ->bcc('<EMAIL>')
      ->markdown('emails.leads.new', ['lead' => $this->lead]);
  }
}
