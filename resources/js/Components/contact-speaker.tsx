import { SpeakerType } from "@/types/speaker-type";
import { useEffect, useRef } from "react";

export default function ContactSpeaker({ speaker }: { speaker: SpeakerType }) {
  const divRef = useRef<HTMLDivElement | null>(null);
  const speakerName = speaker.first_name + " " + speaker.last_name;

  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://cdn.bitrix24.com/b25531643/crm/form/loader_129.js?" +
      ((Date.now() / 180000) | 0);
    script.async = true;
    script.dataset.b24Form = "inline/129/wc4jbq";
    script.dataset.skipMoving = "true";

    // Function to populate the speaker field
    const populateSpeakerField = () => {
      const speakerInput = divRef.current?.querySelector(
        ".b24-form-field-string input.b24-form-control"
      ) as HTMLInputElement;
      if (speakerInput) {
        speakerInput.value = speakerName;
        // Trigger change event to ensure Bitrix24 recognizes the value
        const changeEvent = new Event("change", { bubbles: true });
        speakerInput.dispatchEvent(changeEvent);
        const inputEvent = new Event("input", { bubbles: true });
        speakerInput.dispatchEvent(inputEvent);
      }
    };

    // Observer to watch for form creation
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          // Check if the speaker field exists
          const speakerField = divRef.current?.querySelector(
            ".b24-form-field-string"
          );
          if (speakerField) {
            populateSpeakerField();
            observer.disconnect(); // Stop observing once we've found and filled the field
          }
        }
      });
    });

    // Start observing
    if (divRef.current) {
      observer.observe(divRef.current, {
        childList: true,
        subtree: true,
      });
      divRef.current.appendChild(script);
    }

    // Cleanup function
    return () => {
      observer.disconnect();
    };
  }, [speakerName]);

  return <div ref={divRef} style={{ width: "100%", height: "100%" }}></div>;
}
