<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SpeakerPitchedNotification extends Notification
{
    use Queueable;


    public $speaker;
    public $deal;

    /**
     * Create a new notification instance.
     */
    public function __construct(object $speaker, object $deal)
    {
      $this->speaker = $speaker;
      $this->deal = $deal;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $speaker = $this->speaker;
        $deal = $this->deal;
        return (new MailMessage)->markdown('speakers.pitching.mail', compact('speaker', 'deal'))
          ->bcc('<EMAIL>')
          ->subject('You have been pitched');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
