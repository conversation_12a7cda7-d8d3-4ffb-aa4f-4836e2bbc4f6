import React, { useState, useEffect } from "react";
import MainLayout from "@/Layouts/MainLayout";
import { Head } from "@inertiajs/react";
import Masonry, { ResponsiveMasonry } from "react-responsive-masonry";
import {
  X,
  ZoomIn,
  Download,
  Calendar,
  Users,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { GalleryType } from "@/types/media";

interface GalleryPageProps {
  gallery: GalleryType[];
}

function Index({ gallery }: GalleryPageProps) {
  const [selectedImage, setSelectedImage] = useState<GalleryType | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [imageLoading, setImageLoading] = useState<{ [key: number]: boolean }>(
    {}
  );

  const openLightbox = (image: GalleryType, index: number) => {
    setSelectedImage(image);
    setSelectedIndex(index);
    document.body.style.overflow = "hidden";
  };

  const closeLightbox = () => {
    setSelectedImage(null);
    document.body.style.overflow = "unset";
  };

  const navigateImage = (direction: "prev" | "next") => {
    if (!gallery || gallery.length === 0) return;

    let newIndex = selectedIndex;
    if (direction === "prev") {
      newIndex = selectedIndex > 0 ? selectedIndex - 1 : gallery.length - 1;
    } else {
      newIndex = selectedIndex < gallery.length - 1 ? selectedIndex + 1 : 0;
    }

    setSelectedIndex(newIndex);
    setSelectedImage(gallery[newIndex]);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!selectedImage) return;

      switch (e.key) {
        case "Escape":
          closeLightbox();
          break;
        case "ArrowLeft":
          navigateImage("prev");
          break;
        case "ArrowRight":
          navigateImage("next");
          break;
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => document.removeEventListener("keydown", handleKeyPress);
  }, [selectedImage, selectedIndex]);

  const handleImageLoad = (imageId: number) => {
    setImageLoading((prev) => ({ ...prev, [imageId]: false }));
  };

  const handleImageLoadStart = (imageId: number) => {
    setImageLoading((prev) => ({ ...prev, [imageId]: true }));
  };

  return (
    <MainLayout>
      <Head>
        <title>Event Gallery | MENA Speakers</title>
        <meta
          name="description"
          content="Explore our gallery showcasing memorable moments from conferences, workshops, and speaking events across the Middle East region."
        />
        <link rel="canonical" href="https://mena-speakers.com/gallery" />
      </Head>

      {/* Hero Section */}
      <section className="relative h-[550px] -mt-6">
        <img
          aria-label="Gallery hero image"
          src="/images/gallery-hero-image.jpeg"
          alt="MENA Speakers events and conferences gallery"
          className="w-full h-full object-cover"
        />

        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/30">
          <div className="max-w-7xl mx-auto flex items-center justify-center h-full px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-white text-5xl md:text-6xl font-bold mb-4 animate-pulse">
                Event Gallery
              </h1>
              <p className="text-white/90 text-lg md:text-xl max-w-2xl mx-auto">
                Capturing memorable moments from our conferences, workshops, and
                speaking events
              </p>
              <div className="flex items-center justify-center space-x-8 text-sm md:text-base mt-6">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>Latest Events</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>Professional Speakers</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Event Highlights
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
              Browse through our collection of events, conferences and workshops
              featuring our speakers.
            </p>
            {gallery && gallery.length > 0 && (
              <div className="inline-flex items-center space-x-2 text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-full">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span>{gallery.length} images in gallery</span>
              </div>
            )}
          </div>

          {/* Gallery Grid */}
          {gallery && gallery.length > 0 ? (
            <div className="relative">
              <ResponsiveMasonry
                columnsCountBreakPoints={{ 350: 1, 768: 2, 1024: 3, 1280: 4 }}
              >
                <Masonry gutter="1rem">
                  {gallery.map((image, index) => (
                    <div
                      key={image.id || index}
                      className="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer bg-white"
                      onClick={() => openLightbox(image, index)}
                    >
                      {/* Loading Skeleton */}
                      {imageLoading[image.id || index] && (
                        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-xl"></div>
                      )}

                      <img
                        src={image.url}
                        alt={`Gallery image ${index + 1}`}
                        loading="lazy"
                        className="w-full h-auto transition-transform duration-500 group-hover:scale-105"
                        onLoadStart={() =>
                          handleImageLoadStart(image.id || index)
                        }
                        onLoad={() => handleImageLoad(image.id || index)}
                      />

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-y-4 group-hover:translate-y-0">
                          <div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
                            <ZoomIn className="w-6 h-6 text-gray-800" />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </Masonry>
              </ResponsiveMasonry>
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-24 h-24 mx-auto mb-6 bg-gray-200 rounded-full flex items-center justify-center">
                <Calendar className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No Images Available
              </h3>
              <p className="text-gray-600">
                We're currently updating our gallery. Please check back soon for
                the latest event photos.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Lightbox Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm"
          onClick={closeLightbox}
        >
          <div className="relative max-w-7xl max-h-[90vh] mx-4">
            {/* Close Button */}
            <button
              onClick={closeLightbox}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200 z-10"
              aria-label="Close lightbox"
            >
              <X className="w-8 h-8" />
            </button>

            {/* Download Button */}
            <a
              href={selectedImage.url}
              download
              className="absolute -top-12 right-12 text-white hover:text-gray-300 transition-colors duration-200 z-10"
              aria-label="Download image"
              onClick={(e) => e.stopPropagation()}
            >
              <Download className="w-6 h-6" />
            </a>

            {/* Navigation Arrows */}
            {gallery && gallery.length > 1 && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    navigateImage("prev");
                  }}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors duration-200 z-10 bg-black/50 rounded-full p-2 hover:bg-black/70"
                  aria-label="Previous image"
                >
                  <ChevronLeft className="w-8 h-8" />
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    navigateImage("next");
                  }}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors duration-200 z-10 bg-black/50 rounded-full p-2 hover:bg-black/70"
                  aria-label="Next image"
                >
                  <ChevronRight className="w-8 h-8" />
                </button>
              </>
            )}

            {/* Image Counter */}
            {gallery && gallery.length > 1 && (
              <div className="absolute -top-12 left-0 text-white text-sm">
                {selectedIndex + 1} / {gallery.length}
              </div>
            )}

            {/* Image */}
            <img
              src={selectedImage.url}
              alt="Gallery image enlarged view"
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      )}
    </MainLayout>
  );
}

export default Index;
